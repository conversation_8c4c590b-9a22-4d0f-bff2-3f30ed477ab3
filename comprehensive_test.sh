#!/bin/bash

# 🎯 COMPREHENSIVE JEWELRY E-COMMERCE TEST
# Complete test suite for Lucky Wheel, Cart, Checkout

echo "🎯 Comprehensive Jewelry E-commerce Test Suite"
echo "=============================================="

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="comprehensive_test_$(date +%s)"
EMAIL="<EMAIL>"

echo "📧 Email: $EMAIL"
echo "🆔 Session: $SESSION_ID"
echo "🌐 Base URL: $BASE_URL"
echo ""

# Function to check API response
check_response() {
    local response="$1"
    local test_name="$2"
    
    if echo "$response" | jq -e '.success' >/dev/null 2>&1; then
        echo "   ✅ $test_name: SUCCESS"
        return 0
    else
        echo "   ❌ $test_name: FAILED"
        echo "   📄 Response: $response"
        return 1
    fi
}

# ========================================
# 1. PRODUCTS API TESTING
# ========================================
echo "📦 1. TESTING PRODUCTS API"
echo "=========================="

echo "   🔍 Getting all products..."
PRODUCTS_RESPONSE=$(curl -s -m 10 "$BASE_URL/products" 2>/dev/null)
if check_response "$PRODUCTS_RESPONSE" "Products API"; then
    PRODUCT_COUNT=$(echo "$PRODUCTS_RESPONSE" | jq '.data.products | length')
    echo "   📊 Found $PRODUCT_COUNT products"
    
    # Get first product for testing
    FIRST_PRODUCT_ID=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].id')
    FIRST_PRODUCT_NAME=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].name')
    echo "   📋 First product: $FIRST_PRODUCT_NAME (ID: $FIRST_PRODUCT_ID)"
fi

echo ""

# ========================================
# 2. LUCKY WHEEL TESTING
# ========================================
echo "🎰 2. TESTING LUCKY WHEEL"
echo "========================="

echo "   🎯 Getting Lucky Wheel promotions..."
PROMOTIONS_RESPONSE=$(curl -s -m 10 "$BASE_URL/lucky-wheel/promotions" 2>/dev/null)
if check_response "$PROMOTIONS_RESPONSE" "Lucky Wheel Promotions"; then
    PROMOTION_COUNT=$(echo "$PROMOTIONS_RESPONSE" | jq '.data.total_count')
    echo "   📊 Found $PROMOTION_COUNT promotions"
    
    # Show promotion types
    echo "   🏆 Promotion types:"
    echo "$PROMOTIONS_RESPONSE" | jq -r '.data.promotions[] | "      - \(.name) (\(.program_type)) - \(.lucky_wheel_probability)%"' | head -5
fi

echo ""
echo "   🎲 Testing Lucky Wheel spin..."
SPIN_RESPONSE=$(curl -s -m 10 -X POST "$BASE_URL/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{\"email\": \"$EMAIL\"}" 2>/dev/null)

if check_response "$SPIN_RESPONSE" "Lucky Wheel Spin"; then
    PRIZE_NAME=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.name // "Unknown"')
    VOUCHER_CODE=$(echo "$SPIN_RESPONSE" | jq -r '.data.voucher_code // "None"')
    echo "   🎉 Prize won: $PRIZE_NAME"
    echo "   🎟️ Voucher code: $VOUCHER_CODE"
    
    # Save voucher for later use
    if [ "$VOUCHER_CODE" != "None" ] && [ "$VOUCHER_CODE" != "null" ]; then
        LUCKY_VOUCHER="$VOUCHER_CODE"
        echo "   💾 Saved voucher for checkout test"
    fi
fi

echo ""

# ========================================
# 3. CART TESTING
# ========================================
echo "🛒 3. TESTING CART OPERATIONS"
echo "============================="

echo "   📥 Getting empty cart..."
EMPTY_CART_RESPONSE=$(curl -s -m 10 "$BASE_URL/cart/get" \
  -H "X-Session-ID: $SESSION_ID" 2>/dev/null)
check_response "$EMPTY_CART_RESPONSE" "Empty Cart"

echo ""
echo "   ➕ Adding product to cart..."
if [ ! -z "$FIRST_PRODUCT_ID" ]; then
    ADD_CART_RESPONSE=$(curl -s -m 10 -X POST "$BASE_URL/cart/add" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: $SESSION_ID" \
      -d "{
        \"product_id\": $FIRST_PRODUCT_ID,
        \"quantity\": 1
      }" 2>/dev/null)
    
    if check_response "$ADD_CART_RESPONSE" "Add to Cart"; then
        CART_TOTAL=$(echo "$ADD_CART_RESPONSE" | jq -r '.data.cart.total_amount // 0')
        echo "   💰 Cart total: $CART_TOTAL"
    fi
else
    echo "   ⚠️ No product ID available for cart test"
fi

echo ""
echo "   🛒 Getting updated cart..."
UPDATED_CART_RESPONSE=$(curl -s -m 10 "$BASE_URL/cart/get" \
  -H "X-Session-ID: $SESSION_ID" 2>/dev/null)
if check_response "$UPDATED_CART_RESPONSE" "Updated Cart"; then
    CART_ITEMS=$(echo "$UPDATED_CART_RESPONSE" | jq '.data.cart.items | length // 0')
    echo "   📦 Cart items: $CART_ITEMS"
fi

echo ""

# ========================================
# 4. VOUCHER TESTING
# ========================================
echo "🎟️ 4. TESTING VOUCHER VALIDATION"
echo "================================="

# Test sample vouchers
SAMPLE_VOUCHERS=("SAVE15-001" "GIFT100K-001" "RING50-001" "TEST-COUPON-15")

for VOUCHER in "${SAMPLE_VOUCHERS[@]}"; do
    echo "   🔍 Testing voucher: $VOUCHER"
    VOUCHER_RESPONSE=$(curl -s -m 10 "$BASE_URL/vouchers/validate/$VOUCHER" 2>/dev/null)
    
    if check_response "$VOUCHER_RESPONSE" "Voucher $VOUCHER"; then
        VOUCHER_TYPE=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.program_type // "Unknown"')
        DISCOUNT_PERCENT=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_percent // 0')
        DISCOUNT_FIXED=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_fixed // 0')
        echo "      Type: $VOUCHER_TYPE, Discount: ${DISCOUNT_PERCENT}% / ${DISCOUNT_FIXED} fixed"
    fi
done

# Test Lucky Wheel voucher if available
if [ ! -z "$LUCKY_VOUCHER" ]; then
    echo ""
    echo "   🎰 Testing Lucky Wheel voucher: $LUCKY_VOUCHER"
    LUCKY_VOUCHER_RESPONSE=$(curl -s -m 10 "$BASE_URL/vouchers/validate/$LUCKY_VOUCHER" 2>/dev/null)
    check_response "$LUCKY_VOUCHER_RESPONSE" "Lucky Wheel Voucher"
fi

echo ""

# ========================================
# 5. CHECKOUT TESTING
# ========================================
echo "💳 5. TESTING CHECKOUT"
echo "====================="

echo "   📋 Creating order without voucher..."
CHECKOUT_RESPONSE=$(curl -s -m 15 -X POST "$BASE_URL/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{
    \"customer_info\": {
      \"full_name\": \"Test Customer\",
      \"email\": \"$EMAIL\",
      \"phone\": \"0123456789\",
      \"address\": \"123 Test Street, Test City\"
    },
    \"payment_method\": \"cod\",
    \"notes\": \"Comprehensive test order\"
  }" 2>/dev/null)

if check_response "$CHECKOUT_RESPONSE" "Checkout without voucher"; then
    ORDER_ID=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.id // "Unknown"')
    ORDER_TOTAL=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.total_amount // 0')
    echo "   🆔 Order ID: $ORDER_ID"
    echo "   💰 Order Total: $ORDER_TOTAL"
fi

# Test checkout with voucher if cart has items
echo ""
echo "   🎟️ Testing checkout with voucher..."

# Add another product for voucher test
if [ ! -z "$FIRST_PRODUCT_ID" ]; then
    echo "   ➕ Adding product for voucher test..."
    curl -s -m 10 -X POST "$BASE_URL/cart/add" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: ${SESSION_ID}_voucher" \
      -d "{
        \"product_id\": $FIRST_PRODUCT_ID,
        \"quantity\": 2
      }" >/dev/null 2>&1
    
    VOUCHER_CHECKOUT_RESPONSE=$(curl -s -m 15 -X POST "$BASE_URL/checkout/create-order" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: ${SESSION_ID}_voucher" \
      -d "{
        \"customer_info\": {
          \"full_name\": \"VIP Customer\",
          \"email\": \"vip@$EMAIL\",
          \"phone\": \"**********\",
          \"address\": \"456 VIP Avenue, Luxury District\"
        },
        \"voucher_code\": \"SAVE15-001\",
        \"payment_method\": \"bank_transfer\",
        \"notes\": \"Order with voucher discount\"
      }" 2>/dev/null)
    
    if check_response "$VOUCHER_CHECKOUT_RESPONSE" "Checkout with voucher"; then
        VOUCHER_ORDER_ID=$(echo "$VOUCHER_CHECKOUT_RESPONSE" | jq -r '.data.order.id // "Unknown"')
        VOUCHER_ORDER_TOTAL=$(echo "$VOUCHER_CHECKOUT_RESPONSE" | jq -r '.data.order.total_amount // 0')
        DISCOUNT_APPLIED=$(echo "$VOUCHER_CHECKOUT_RESPONSE" | jq -r '.data.order.discount_amount // 0')
        echo "   🆔 Voucher Order ID: $VOUCHER_ORDER_ID"
        echo "   💰 Order Total: $VOUCHER_ORDER_TOTAL"
        echo "   🎯 Discount Applied: $DISCOUNT_APPLIED"
    fi
fi

echo ""

# ========================================
# 6. SUMMARY REPORT
# ========================================
echo "📊 TEST SUMMARY REPORT"
echo "======================"

# Count successful tests
TOTAL_TESTS=0
PASSED_TESTS=0

# This is a simplified summary - in real implementation, 
# you'd track each test result
echo "🎯 Test Categories:"
echo "   📦 Products API: Tested"
echo "   🎰 Lucky Wheel: Tested (Promotions + Spin)"
echo "   🛒 Cart Operations: Tested (Get + Add)"
echo "   🎟️ Voucher Validation: Tested (4 sample vouchers)"
echo "   💳 Checkout: Tested (With and without voucher)"

echo ""
echo "📈 Key Metrics:"
if [ ! -z "$PRODUCT_COUNT" ]; then
    echo "   📦 Products found: $PRODUCT_COUNT"
fi
if [ ! -z "$PROMOTION_COUNT" ]; then
    echo "   🎰 Promotions available: $PROMOTION_COUNT"
fi
if [ ! -z "$PRIZE_NAME" ]; then
    echo "   🎉 Lucky Wheel prize: $PRIZE_NAME"
fi
if [ ! -z "$ORDER_ID" ]; then
    echo "   💳 Orders created: 1-2"
fi

echo ""
echo "🎯 COMPREHENSIVE TEST COMPLETED!"
echo "📁 Check individual test results above for detailed analysis."
echo "🔧 For troubleshooting, see: MANUAL_TESTING_GUIDE.md"

# Save test summary
cat > /tmp/comprehensive_test_summary.json << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "session_id": "$SESSION_ID",
  "email": "$EMAIL",
  "results": {
    "products_count": ${PRODUCT_COUNT:-0},
    "promotions_count": ${PROMOTION_COUNT:-0},
    "lucky_wheel_prize": "${PRIZE_NAME:-null}",
    "voucher_code": "${VOUCHER_CODE:-null}",
    "order_created": "${ORDER_ID:-null}"
  }
}
EOF

echo "💾 Test summary saved to /tmp/comprehensive_test_summary.json"
