#!/bin/bash

# 🎰 JEWELRY ECOMMERCE COMPLETE TEST SUITE
# Test Lucky Wheel, Cart, Checkout with Enhanced Jewelry Data

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="test_jewelry_$(date +%s)"
EMAIL="<EMAIL>"

echo "🎯 Starting Jewelry E-commerce Test Suite..."
echo "📧 Email: $EMAIL"
echo "🆔 Session: $SESSION_ID"
echo "🌐 Base URL: $BASE_URL"
echo ""

# ========================================
# 1. TEST PRODUCTS API
# ========================================
echo "📦 1. TESTING PRODUCTS API..."

echo "   📋 Getting all products..."
curl -s "$BASE_URL/products" | jq '.data.products | length' > /tmp/product_count.txt
PRODUCT_COUNT=$(cat /tmp/product_count.txt)
echo "   ✅ Found $PRODUCT_COUNT products"

echo "   🔍 Getting product details..."
curl -s "$BASE_URL/products" | jq '.data.products[0] | {id, name, price, attributes}' > /tmp/first_product.json
FIRST_PRODUCT_ID=$(cat /tmp/first_product.json | jq -r '.id')
echo "   ✅ First product ID: $FIRST_PRODUCT_ID"

echo "   🎯 Testing product with variants..."
curl -s "$BASE_URL/products/9" | jq '.data.variants | length' > /tmp/variant_count.txt
VARIANT_COUNT=$(cat /tmp/variant_count.txt)
echo "   ✅ Product 9 has $VARIANT_COUNT variants"

echo ""

# ========================================
# 2. TEST CATEGORIES API
# ========================================
echo "🏷️ 2. TESTING CATEGORIES API..."

echo "   📂 Getting all categories..."
curl -s "$BASE_URL/categories" | jq '.data.categories | length' > /tmp/category_count.txt
CATEGORY_COUNT=$(cat /tmp/category_count.txt)
echo "   ✅ Found $CATEGORY_COUNT categories"

echo "   📋 Category list:"
curl -s "$BASE_URL/categories" | jq -r '.data.categories[] | "   - \(.name) (ID: \(.id))"'

echo ""

# ========================================
# 3. TEST LUCKY WHEEL API
# ========================================
echo "🎰 3. TESTING LUCKY WHEEL API..."

echo "   🎯 Getting Lucky Wheel promotions..."
curl -s "$BASE_URL/lucky-wheel/promotions" | jq '.data.total_count' > /tmp/promotion_count.txt
PROMOTION_COUNT=$(cat /tmp/promotion_count.txt)
echo "   ✅ Found $PROMOTION_COUNT Lucky Wheel promotions"

echo "   🎲 Testing Lucky Wheel spin..."
SPIN_RESULT=$(curl -s -X POST "$BASE_URL/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{\"email\": \"$EMAIL\"}")

echo "$SPIN_RESULT" > /tmp/spin_result.json

if echo "$SPIN_RESULT" | jq -e '.success' > /dev/null; then
    PRIZE_NAME=$(echo "$SPIN_RESULT" | jq -r '.data.prize.name // "Unknown"')
    VOUCHER_CODE=$(echo "$SPIN_RESULT" | jq -r '.data.voucher_code // "None"')
    echo "   🎉 Spin successful!"
    echo "   🏆 Prize: $PRIZE_NAME"
    echo "   🎟️ Voucher: $VOUCHER_CODE"
else
    echo "   ❌ Spin failed or no response"
    echo "   📄 Response: $SPIN_RESULT"
fi

echo ""

# ========================================
# 4. TEST CART API
# ========================================
echo "🛒 4. TESTING CART API..."

echo "   📥 Getting empty cart..."
CART_RESULT=$(curl -s "$BASE_URL/cart/get" \
  -H "X-Session-ID: $SESSION_ID")

echo "$CART_RESULT" > /tmp/cart_empty.json
echo "   ✅ Empty cart retrieved"

echo "   ➕ Adding product to cart..."
ADD_RESULT=$(curl -s -X POST "$BASE_URL/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{
    \"product_id\": $FIRST_PRODUCT_ID,
    \"quantity\": 1
  }")

echo "$ADD_RESULT" > /tmp/cart_add.json

if echo "$ADD_RESULT" | jq -e '.success' > /dev/null; then
    echo "   ✅ Product added to cart successfully"
    CART_TOTAL=$(echo "$ADD_RESULT" | jq -r '.data.cart.total_amount // 0')
    echo "   💰 Cart total: $CART_TOTAL"
else
    echo "   ❌ Failed to add product to cart"
    echo "   📄 Response: $ADD_RESULT"
fi

echo "   🛒 Getting updated cart..."
CART_UPDATED=$(curl -s "$BASE_URL/cart/get" \
  -H "X-Session-ID: $SESSION_ID")

echo "$CART_UPDATED" > /tmp/cart_updated.json
CART_ITEMS=$(echo "$CART_UPDATED" | jq '.data.cart.items | length // 0')
echo "   ✅ Cart has $CART_ITEMS items"

echo ""

# ========================================
# 5. TEST VOUCHER VALIDATION
# ========================================
echo "🎟️ 5. TESTING VOUCHER VALIDATION..."

# Test with sample voucher codes
VOUCHER_CODES=("SAVE15-001" "GIFT100K-001" "RING50-001" "TEST-COUPON-15")

for VOUCHER in "${VOUCHER_CODES[@]}"; do
    echo "   🔍 Testing voucher: $VOUCHER"
    VOUCHER_RESULT=$(curl -s "$BASE_URL/vouchers/validate/$VOUCHER")
    
    if echo "$VOUCHER_RESULT" | jq -e '.success' > /dev/null; then
        VOUCHER_TYPE=$(echo "$VOUCHER_RESULT" | jq -r '.data.program_type // "Unknown"')
        VOUCHER_DISCOUNT=$(echo "$VOUCHER_RESULT" | jq -r '.data.discount_info.discount_percent // .data.discount_info.discount_fixed // "Unknown"')
        echo "   ✅ Valid - Type: $VOUCHER_TYPE, Discount: $VOUCHER_DISCOUNT"
    else
        echo "   ❌ Invalid or not found"
    fi
done

echo ""

# ========================================
# 6. TEST CHECKOUT API
# ========================================
echo "💳 6. TESTING CHECKOUT API..."

echo "   📋 Creating test order..."
CHECKOUT_RESULT=$(curl -s -X POST "$BASE_URL/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{
    \"customer_info\": {
      \"full_name\": \"Test Customer\",
      \"email\": \"$EMAIL\",
      \"phone\": \"0123456789\",
      \"address\": \"123 Test Street, Test City\"
    },
    \"voucher_code\": \"TEST-COUPON-15\",
    \"payment_method\": \"cod\",
    \"notes\": \"Test order from automated script\"
  }")

echo "$CHECKOUT_RESULT" > /tmp/checkout_result.json

if echo "$CHECKOUT_RESULT" | jq -e '.success' > /dev/null; then
    ORDER_ID=$(echo "$CHECKOUT_RESULT" | jq -r '.data.order.id // "Unknown"')
    ORDER_TOTAL=$(echo "$CHECKOUT_RESULT" | jq -r '.data.order.total_amount // 0')
    echo "   ✅ Order created successfully!"
    echo "   🆔 Order ID: $ORDER_ID"
    echo "   💰 Order Total: $ORDER_TOTAL"
else
    echo "   ❌ Checkout failed"
    echo "   📄 Response: $CHECKOUT_RESULT"
fi

echo ""

# ========================================
# 7. TEST ENHANCED PRODUCTS
# ========================================
echo "💎 7. TESTING ENHANCED JEWELRY PRODUCTS..."

echo "   🔍 Looking for enhanced products..."
ENHANCED_PRODUCTS=$(curl -s "$BASE_URL/products" | jq '.data.products[] | select(.name | contains("Enhanced") or contains("Premium") or contains("Đa")) | {id, name, variants: (.variants | length)}')

if [ ! -z "$ENHANCED_PRODUCTS" ]; then
    echo "   ✅ Found enhanced products:"
    echo "$ENHANCED_PRODUCTS" | jq -r '"   - \(.name) (ID: \(.id), Variants: \(.variants))"'
else
    echo "   ⚠️ No enhanced products found yet (may need module upgrade)"
fi

echo ""

# ========================================
# 8. SUMMARY REPORT
# ========================================
echo "📊 TEST SUMMARY REPORT"
echo "======================="
echo "📦 Products: $PRODUCT_COUNT found"
echo "🏷️ Categories: $CATEGORY_COUNT found"
echo "🎰 Lucky Wheel: $PROMOTION_COUNT promotions"
echo "🛒 Cart: $CART_ITEMS items added"
echo "💳 Checkout: $([ -f /tmp/checkout_result.json ] && echo "Tested" || echo "Failed")"
echo ""

# ========================================
# 9. PERFORMANCE TEST
# ========================================
echo "⚡ 9. PERFORMANCE TEST..."

echo "   ⏱️ Testing API response times..."
START_TIME=$(date +%s%N)
curl -s "$BASE_URL/products" > /dev/null
END_TIME=$(date +%s%N)
PRODUCTS_TIME=$(( (END_TIME - START_TIME) / 1000000 ))
echo "   📦 Products API: ${PRODUCTS_TIME}ms"

START_TIME=$(date +%s%N)
curl -s "$BASE_URL/lucky-wheel/promotions" > /dev/null
END_TIME=$(date +%s%N)
LUCKY_TIME=$(( (END_TIME - START_TIME) / 1000000 ))
echo "   🎰 Lucky Wheel API: ${LUCKY_TIME}ms"

START_TIME=$(date +%s%N)
curl -s "$BASE_URL/categories" > /dev/null
END_TIME=$(date +%s%N)
CATEGORIES_TIME=$(( (END_TIME - START_TIME) / 1000000 ))
echo "   🏷️ Categories API: ${CATEGORIES_TIME}ms"

echo ""

# ========================================
# 10. CLEANUP
# ========================================
echo "🧹 CLEANING UP..."
echo "   📁 Test files saved in /tmp/"
echo "   🗂️ Available files:"
echo "   - /tmp/spin_result.json (Lucky Wheel result)"
echo "   - /tmp/cart_add.json (Cart add result)"
echo "   - /tmp/checkout_result.json (Checkout result)"
echo ""

echo "🎯 TEST SUITE COMPLETED!"
echo "📊 Check individual results above for detailed analysis."
echo ""

# Save summary to file
cat > /tmp/test_summary.json << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "session_id": "$SESSION_ID",
  "email": "$EMAIL",
  "results": {
    "products_count": $PRODUCT_COUNT,
    "categories_count": $CATEGORY_COUNT,
    "promotions_count": $PROMOTION_COUNT,
    "cart_items": $CART_ITEMS,
    "performance": {
      "products_api_ms": $PRODUCTS_TIME,
      "lucky_wheel_api_ms": $LUCKY_TIME,
      "categories_api_ms": $CATEGORIES_TIME
    }
  }
}
EOF

echo "💾 Summary saved to /tmp/test_summary.json"
