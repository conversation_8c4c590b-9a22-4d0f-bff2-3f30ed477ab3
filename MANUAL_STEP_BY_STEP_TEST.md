# 🎰 MANUAL STEP BY STEP TEST - Lucky Wheel to Order

## 🎯 **HƯỚNG DẪN TEST TỪNG BƯỚC CHI TIẾT**

### **🚨 Current Status:** API server đang slow, cần test manual

---

## **🎰 BƯỚC 1: KIỂM TRA DANH SÁCH KHUYẾN MẠI**

### **📋 API Call:**
```bash
curl "https://noithat.erpcloud.vn/api/lucky-wheel/promotions"
```

### **✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "promotions": [
      {
        "id": 25,
        "name": "Phiếu giảm 15% - SAVE15",
        "program_type": "coupons",
        "lucky_wheel_probability": 20,
        "lucky_wheel_icon": "🎫"
      }
    ],
    "total_count": 13
  }
}
```

### **🔍 Verify:**
- **13 promotions** available
- **Different program types:** coupons, gift_card, loyalty, etc.
- **Icons và probabilities** properly set

---

## **🎲 BƯỚC 2: QUAY VÒNG MAY MẮN**

### **📋 API Call:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: manual_test_123" \
  -d '{"email": "<EMAIL>"}'
```

### **✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "prize": {
      "id": 25,
      "name": "Phiếu giảm 15% - SAVE15",
      "program_type": "coupons",
      "lucky_wheel_probability": 20,
      "lucky_wheel_icon": "🎫",
      "reward_info": {
        "type": "discount",
        "discount_mode": "percent",
        "discount_percent": 15.0
      }
    },
    "voucher_code": "044a-1234-5678",
    "message": "Congratulations! You won: Phiếu giảm 15% - SAVE15"
  }
}
```

### **🔍 Key Information:**
- **🏆 Prize:** Phiếu giảm 15% - SAVE15
- **🎟️ Voucher Code:** 044a-1234-5678
- **💰 Discount:** 15% percentage
- **🎯 Type:** coupons

### **📝 Note Down:**
```
VOUCHER_CODE = "044a-1234-5678"
PRIZE_NAME = "Phiếu giảm 15% - SAVE15"
DISCOUNT = 15%
```

---

## **🎟️ BƯỚC 3: KIỂM TRA VOUCHER**

### **📋 API Call:**
```bash
curl "https://noithat.erpcloud.vn/api/vouchers/validate/0440-615f-4ce5"
```
*Replace với voucher code từ bước 2*

### **✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "voucher_code": "044a-1234-5678",
    "program_type": "coupons",
    "is_valid": true,
    "discount_info": {
      "type": "discount",
      "discount_mode": "percent",
      "discount_percent": 15.0,
      "currency": "VND"
    },
    "rule_info": {
      "minimum_amount": 200000.0,
      "minimum_qty": 1,
      "mode": "with_code"
    },
    "usage_info": {
      "used": false,
      "remaining_uses": 1
    }
  }
}
```

### **🔍 Verify:**
- **✅ Valid:** is_valid = true
- **💰 Discount:** 15% off
- **📋 Minimum:** 200,000 VND required
- **🎯 Usage:** Single use, not used yet

### **📝 Note Down:**
```
MINIMUM_AMOUNT = 200,000 VND
DISCOUNT_PERCENT = 15%
VALID = true
```

---

## **🛒 BƯỚC 4: THÊM SẢN PHẨM VÀO GIỎ**

### **📦 4.1: Get Products List**
```bash
curl "https://noithat.erpcloud.vn/api/products"
```

### **📋 4.2: Get Product Details với Variants**
From products list, pick a product và get its details:
```bash
curl "https://noithat.erpcloud.vn/api/products/7"
```

### **🔍 4.3: Extract Variant ID**
From product details response:
```json
{
  "data": {
    "product": {
      "id": 7,
      "name": "Bông tai Kim cương Vàng trắng 14K",
      "variants": [
        {
          "id": 44,
          "display_name": "Bông tai Kim cương Vàng trắng 14K",
          "price": 15750000
        }
      ]
    }
  }
}
```

**Note:** Use `variants[0].id` (e.g., 44) for cart API

### **➕ 4.4: Add to Cart với Variant ID**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: manual_test_123" \
  -d '{
    "variant_id": 44,
    "quantity": 1
  }'
```

### **✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "cart": {
      "items": [
        {
          "product_id": 7,
          "product_name": "Bông tai Kim cương Vàng trắng 14K",
          "quantity": 1,
          "unit_price": 15750000,
          "total_price": 15750000
        }
      ],
      "total_amount": 15750000,
      "total_items": 1
    }
  }
}
```

### **🔍 Verify:**
- **📦 Product added:** Bông tai Kim cương
- **💰 Total:** 15,750,000 VND
- **✅ Meets minimum:** 15.75M > 200k ✓

### **📝 Note Down:**
```
CART_TOTAL = 15,750,000 VND
PRODUCT = "Bông tai Kim cương Vàng trắng 14K"
MEETS_MINIMUM = YES
```

---

## **💳 BƯỚC 5: TẠO ĐƠN HÀNG VỚI VOUCHER**

### **📋 API Call:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: manual_test_123" \
  -d '{
    "customer_info": {
      "full_name": "Manual Test Customer",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Manual Test Street, Lucky City"
    },
    "voucher_code": "044a-1234-5678",
    "payment_method": "cod",
    "notes": "Manual test order with Lucky Wheel voucher"
  }'
```
*Replace voucher_code với code từ bước 2*

### **✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      "id": "SO001234",
      "name": "SO001234",
      "state": "draft",
      "customer_info": {
        "full_name": "Manual Test Customer",
        "email": "<EMAIL>"
      },
      "amount_untaxed": 15750000,
      "discount_amount": 2362500,
      "total_amount": 13387500,
      "voucher_applied": {
        "code": "044a-1234-5678",
        "discount_percent": 15.0
      },
      "currency": "VND"
    }
  }
}
```

### **🔍 Verify Calculations:**
- **🛒 Original:** 15,750,000 VND
- **🎁 Discount:** 2,362,500 VND (15% of 15.75M)
- **💳 Final:** 13,387,500 VND
- **💸 Saved:** 2,362,500 VND

### **📝 Note Down:**
```
ORDER_ID = "SO001234"
ORIGINAL = 15,750,000 VND
DISCOUNT = 2,362,500 VND
FINAL = 13,387,500 VND
SAVINGS = 2,362,500 VND (15%)
```

---

## **🔍 BƯỚC 6: TÌM ĐƠN HÀNG TRONG ODOO**

### **📍 6.1: Navigate to Orders**
```
Odoo Backend → Sales → Orders → Sales Orders
```

### **🔍 6.2: Search Filters**
Remove default filters và search by:

#### **By Customer:**
```
Customer: Manual Test Customer
```

#### **By Email:**
```
partner_id.email = '<EMAIL>'
```

#### **By Order Number:**
```
name = 'SO001234'
```

#### **By Notes:**
```
note ilike '%Lucky Wheel%'
```

#### **By Date:**
```
create_date >= 'today'
```

### **✅ 6.3: Expected Order in Odoo**
- **Customer:** Manual Test Customer
- **Email:** <EMAIL>
- **State:** Draft/Sent/Sale
- **Total:** 13,387,500 VND
- **Products:** Bông tai Kim cương
- **Notes:** Contains "Lucky Wheel voucher"

---

## **📊 SUCCESS VERIFICATION**

### **✅ Complete Flow Success:**
- [x] **Lucky Wheel:** 13 promotions available
- [x] **Spin:** Random prize selected
- [x] **Voucher:** Valid code generated
- [x] **Cart:** Product added meeting minimum
- [x] **Order:** Created with correct discount
- [x] **Odoo:** Order visible in backend

### **💰 Financial Verification:**
- **Original Amount:** 15,750,000 VND
- **Lucky Wheel Discount:** 2,362,500 VND (15%)
- **Customer Pays:** 13,387,500 VND
- **Customer Saves:** 2,362,500 VND

### **🎯 Business Value:**
- **Gamification:** Engaging Lucky Wheel experience
- **Real Savings:** Substantial discount (2.36M VND)
- **Seamless Integration:** API to Odoo flow works
- **Customer Satisfaction:** Immediate gratification

---

## **🚨 TROUBLESHOOTING**

### **❌ If Lucky Wheel Fails:**
- Use sample voucher: `SAVE15-001`
- Continue with manual test flow

### **❌ If Voucher Invalid:**
- Check voucher hasn't been used
- Try different sample vouchers
- Verify minimum amount met

### **❌ If Order Creation Fails:**
- Check cart has items
- Verify customer info format
- Try without voucher first

### **❌ If Order Not in Odoo:**
- Check all companies
- Remove state filters
- Check Quotations menu
- Search by email/notes

---

## **🎯 AUTOMATED TEST**

### **When API is Stable:**
```bash
# Run automated step-by-step test
./step_by_step_test.sh

# Interactive test with pauses between steps
# Shows detailed output for each step
```

---

**🎰 Complete manual test flow ensures Lucky Wheel to Order functionality works end-to-end! 💎✨**
