# 🧪 SIMPLE TEST COMMANDS (Copy & Paste)

# Test 1: Add product to cart (fixed)
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" -H "Content-Type: application/json" -H "X-Session-ID: test123" -d '{"product_id": 13, "variant_id": 23, "quantity": 1}'

# Test 2: Get cart with items
curl -H "X-Session-ID: test123" "https://noithat.erpcloud.vn/api/cart"

# Test 3: Get payment methods
curl "https://noithat.erpcloud.vn/api/checkout/payment-methods"

# Test 4: Create order
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" -H "Content-Type: application/json" -H "X-Session-ID: test123" -d '{"customer": {"full_name": "Test User", "email": "<EMAIL>", "phone": "0123456789", "address": "Test Address"}, "payment_method": "cod"}'

# Test 5: Track orders
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"
