# 🎯 LOYALTY SAMPLE DATA - COMPLETE GUIDE

## 📋 **OVERVIEW**

Khi cài đặt module `jewelry_ecommerce`, bạn sẽ có sẵn **8 chương trình khuyến mại mẫu** với **40+ loyalty cards** để test ngay lập tức.

---

## 🎫 **1. COUPONS - Phiếu giảm 15%**

### **📊 Program Details:**
- **Name:** "Phiếu giảm 15% - SAVE15"
- **Code:** SAVE15
- **Discount:** 15% cho đơn từ 200k
- **Lucky Wheel:** ✅ (20% probability)
- **Icon:** 🎫

### **🎟️ Sample Cards:**
```
SAVE15-001, SAVE15-002, SAVE15-003
044a-1234-5678, 044b-2345-6789, 044c-3456-7890
TEST-COUPON-15
```

### **🧪 Test Commands:**
```bash
# Test coupon validation
curl "https://noithat.erpcloud.vn/api/vouchers/validate/SAVE15-001"

# Test checkout with coupon
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -d '{"voucher_code": "SAVE15-001", ...}'
```

---

## 🔥 **2. PROMOTIONS - Jewelry Sale 10%**

### **📊 Program Details:**
- **Name:** "Jewelry Sale - Tự động giảm 10%"
- **Trigger:** Auto (không cần mã)
- **Discount:** 10% cho đơn từ 500k
- **Lucky Wheel:** ✅ (15% probability)
- **Icon:** 🔥

### **🧪 Test Scenario:**
```bash
# Add jewelry products > 500k → Auto 10% discount
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -d '{"product_id": 19, "quantity": 3}'  # > 500k

# Checkout → Should auto-apply 10% discount
```

---

## 🎁 **3. GIFT CARD - Thẻ quà tặng 100k**

### **📊 Program Details:**
- **Name:** "Thẻ quà tặng 100k"
- **Value:** 100,000 VND
- **Usage:** Dùng dần trong các đơn hàng
- **Lucky Wheel:** ✅ (10% probability)
- **Icon:** 🎁

### **🎟️ Sample Cards:**
```
GIFT100K-001 (100k points)
GIFT100K-002 (100k points)
GIFT50K-001 (50k points)
044d-4567-8901 (100k points)
044e-5678-9012 (50k points)
TEST-GIFT-100K (100k points)
```

### **🧪 Test Commands:**
```bash
# Test gift card
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -d '{"voucher_code": "GIFT100K-001", ...}'
```

---

## ⭐ **4. LOYALTY - Khách hàng thân thiết**

### **📊 Program Details:**
- **Name:** "Khách hàng thân thiết"
- **Points:** 1 point per 1 VND spent
- **Rewards:** 
  - 100k points → 5% discount
  - 200k points → 10% discount
- **Lucky Wheel:** ✅ (5% probability)
- **Icon:** ⭐

### **🧪 Test Scenario:**
```bash
# Customer tích điểm qua nhiều đơn hàng
# Khi đủ 100k/200k points → Có thể đổi discount
```

---

## 💳 **5. EWALLET - Ví điện tử**

### **📊 Program Details:**
- **Name:** "eWallet - Ví điện tử"
- **Usage:** Nạp tiền trước, dùng dần
- **Rate:** 1 point = 1 VND
- **Lucky Wheel:** ✅ (10% probability)
- **Icon:** 💳

### **🎟️ Sample Cards:**
```
EWALLET30K-001 (30k points)
EWALLET50K-001 (50k points)
044f-6789-0123 (30k points)
044g-7890-1234 (20k points)
TEST-EWALLET-30K (30k points)
```

### **🧪 Test Commands:**
```bash
# Test eWallet
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -d '{"voucher_code": "EWALLET30K-001", ...}'
```

---

## 🏷️ **6. PROMO CODE - RING50**

### **📊 Program Details:**
- **Name:** "RING50 - Giảm 50k cho nhẫn"
- **Code:** RING50
- **Discount:** 50,000 VND fixed
- **Target:** Ring products
- **Lucky Wheel:** ✅ (20% probability)
- **Icon:** 🏷️

### **🎟️ Sample Cards:**
```
RING50-001, RING50-002
044h-8901-2345, 044i-9012-3456
TEST-RING50
```

### **🧪 Test Commands:**
```bash
# Test promo code for rings
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -d '{"voucher_code": "RING50-001", ...}'
```

---

## 🛒 **7. BUY X GET Y - Mua 2 Tặng 1**

### **📊 Program Details:**
- **Name:** "Mua 2 Tặng 1"
- **Rule:** Mua 2 sản phẩm từ 100k
- **Reward:** Giảm 15% cho sản phẩm rẻ nhất
- **Lucky Wheel:** ✅ (100% probability)
- **Icon:** 🛒

### **🎟️ Sample Cards:**
```
BUY2GET1-001
```

### **🧪 Test Scenario:**
```bash
# Add 2+ products > 100k → Auto discount on cheapest
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -d '{"product_id": 19, "quantity": 2}'
```

---

## 📅 **8. NEXT ORDER COUPONS - Giảm lần sau 20%**

### **📊 Program Details:**
- **Name:** "Mua ngay - Giảm lần sau 20%"
- **Rule:** Đơn hiện tại từ 300k
- **Reward:** 20% discount cho đơn tiếp theo
- **Lucky Wheel:** ✅ (15% probability)
- **Icon:** 📅

### **🎟️ Sample Cards:**
```
NEXT20-001, NEXT20-002
044j-0123-4567, 044k-1234-5678
TEST-NEXT-20
```

### **🧪 Test Scenario:**
```bash
# Order 1: > 300k → Nhận next order coupon
# Order 2: Dùng coupon → 20% discount
```

---

## 🎰 **LUCKY WHEEL INTEGRATION**

### **🔧 All Programs Ready:**
- ✅ **8 program types** với `is_lucky_wheel = True`
- ✅ **Probability settings** từ 5% đến 100%
- ✅ **Icons** phù hợp cho từng loại
- ✅ **40+ voucher codes** sẵn sàng

### **🧪 Test Lucky Wheel:**
```bash
# Get all Lucky Wheel programs
curl "https://noithat.erpcloud.vn/api/lucky-wheel/promotions"

# Spin Lucky Wheel
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "X-Session-ID: test_sample_data" \
  -d '{"email": "<EMAIL>"}'
```

### **🎯 Expected Results:**
- **Random selection** từ 8 program types
- **Real voucher codes** từ sample data
- **Immediate usability** trong checkout

---

## 🧪 **COMPLETE TEST SUITE**

### **📋 Test Checklist:**

#### **✅ Coupon Tests:**
- [ ] SAVE15-001 validates correctly
- [ ] 15% discount applies on 200k+ orders
- [ ] Single-use validation works

#### **✅ Promotion Tests:**
- [ ] Auto-applies on 500k+ orders
- [ ] 10% discount calculates correctly
- [ ] No code required

#### **✅ Gift Card Tests:**
- [ ] GIFT100K-001 has 100k balance
- [ ] Deducts from balance correctly
- [ ] Multiple usage until depleted

#### **✅ Loyalty Tests:**
- [ ] Points accumulate correctly
- [ ] Rewards unlock at thresholds
- [ ] Multiple reward tiers work

#### **✅ eWallet Tests:**
- [ ] EWALLET30K-001 has 30k balance
- [ ] Deducts correctly from wallet
- [ ] Balance tracking accurate

#### **✅ Promo Code Tests:**
- [ ] RING50-001 validates for rings
- [ ] 50k fixed discount applies
- [ ] Product restrictions work

#### **✅ Buy X Get Y Tests:**
- [ ] Auto-triggers on 2+ items
- [ ] Discount applies to cheapest
- [ ] Quantity validation works

#### **✅ Next Order Tests:**
- [ ] Generates coupon after 300k order
- [ ] 20% applies to next order
- [ ] Future usage validation

#### **✅ Lucky Wheel Tests:**
- [ ] All 8 programs appear in API
- [ ] Random selection works
- [ ] Voucher codes are valid
- [ ] Checkout integration works

---

## 🚀 **DEPLOYMENT INSTRUCTIONS**

### **📦 Installation:**
1. **Deploy module** với sample data
2. **Upgrade jewelry_ecommerce** module
3. **Verify programs** trong Loyalty → Programs
4. **Test APIs** với sample voucher codes

### **🔧 Customization:**
1. **Edit programs** theo business needs
2. **Add more cards** nếu cần
3. **Adjust probabilities** cho Lucky Wheel
4. **Update icons** và descriptions

### **📊 Monitoring:**
1. **Track usage** của sample cards
2. **Monitor API performance** với real data
3. **Analyze conversion** rates
4. **Optimize** based on results

---

## 🎯 **BUSINESS VALUE**

### **✅ Immediate Benefits:**
- **Zero setup time** - Cài là dùng được ngay
- **Complete testing** - Tất cả scenarios covered
- **Real-world examples** - Business-ready programs
- **Lucky Wheel ready** - Gamification từ ngày 1

### **📈 Growth Opportunities:**
- **A/B testing** với different program types
- **Customer segmentation** với targeted offers
- **Seasonal campaigns** dựa trên sample templates
- **Data-driven optimization** từ usage analytics

---

**🎯 Với sample data này, bạn có complete loyalty ecosystem ngay sau khi cài module! 💎✨**
