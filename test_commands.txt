# 🧪 COPY-PASTE TEST COMMANDS

# 1. Get empty cart
curl -H "X-Session-ID: test123" "https://noithat.erpcloud.vn/api/cart"

# 2. Add product to cart
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{"product_id": 13, "variant_id": 23, "quantity": 1}'

# 3. Get cart with items
curl -H "X-Session-ID: test123" "https://noithat.erpcloud.vn/api/cart"

# 4. Get payment methods
curl "https://noithat.erpcloud.vn/api/checkout/payment-methods"

# 5. Create order
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "customer": {
      "full_name": "Test User",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Test Address"
    },
    "payment_method": "cod"
  }'

# 6. Track orders
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"

# 7. Test error - invalid product
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: error123" \
  -d '{"product_id": 999, "variant_id": 999, "quantity": 1}'

# 8. Clear cart
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/clear" \
  -H "X-Session-ID: test123"
