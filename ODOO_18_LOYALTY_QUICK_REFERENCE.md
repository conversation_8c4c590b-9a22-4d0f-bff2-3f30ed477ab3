# 🚀 ODOO 18 LOYALTY - <PERSON><PERSON><PERSON>K REFERENCE GUIDE

## 📋 **QUICK SETUP CHEAT SHEET**

### **🎫 1. COUPONS - Phiếu giảm giá**
```
Program Type: Coupons
Application: With a promotion code
Code: SAVE15
Minimum Purchase: 200,000
Reward: 15% discount
Lucky Wheel: ✅ (20%)
```

### **🔥 2. PROMOTIONS - Khuyến mãi tự động**
```
Program Type: Promotions  
Application: Automatic
Minimum Purchase: 500,000
Categories: Jewelry
Reward: 10% discount on jewelry
Lucky Wheel: ✅ (15%)
```

### **🎁 3. GIFT CARD - Thẻ quà tặng**
```
Program Type: Gift Card
Reward Mode: Per money spent
Reward: 1 point per VND
Discount Mode: Per point (1:1)
Value: 100,000 VND
Lucky Wheel: ✅ (10%)
```

### **⭐ 4. LOYALTY - Thẻ khách hàng thân thiết**
```
Program Type: Loyalty Cards
Application: Automatic
Reward Mode: Per money spent
Reward: 1 point per 1000 VND
Rewards: 
- 100 points → 5% discount
- 200 points → 10% discount
Lucky Wheel: ✅ (5%)
```

### **💳 5. EWALLET - Ví điện tử**
```
Program Type: eWallet
Reward Mode: Per money spent
Reward: 1 point per VND
Discount Mode: Per point (1:1)
Generate: Manual for customers
Lucky Wheel: ✅ (10%)
```

### **🏷️ 6. PROMO CODE - Mã giảm giá sản phẩm**
```
Program Type: Discount Code
Application: With a promotion code
Code: RING50
Product Tag: Ring
Reward: 50,000 VND fixed discount
Apply On: Specific products (Ring)
Lucky Wheel: ✅ (20%)
```

### **🛒 7. BUY X GET Y - Mua X Tặng Y**
```
Program Type: Buy X Get Y
Application: Automatic
Minimum Quantity: 2
Reward Mode: Per unit paid
Reward: 1 point per unit
Free Product: Gift item
Quantity: 1
Points needed: 2
Lucky Wheel: ✅ (100%)
```

### **📅 8. NEXT ORDER COUPONS - Phiếu giảm giá đơn tiếp theo**
```
Program Type: Next Order Coupons
Application: Automatic
Minimum Purchase: 300,000 (current order)
Reward Mode: Per order
Reward: 1 point per order
Discount: 20% (next order)
Lucky Wheel: ✅ (15%)
```

---

## ⚡ **SPEED SETUP - 5 PHÚT MỖI PROGRAM**

### **🔧 Template cho Coupons:**
1. **Create Program** → Name: "Coupon [Discount]% - [CODE]"
2. **Type:** Coupons
3. **Rules:** With code → Code: [CODE] → Min: [AMOUNT]
4. **Rewards:** Discount → [%] → On Order
5. **Lucky Wheel:** ✅ → [%] → 🎫
6. **Save** → Generate Coupons → [QUANTITY]

### **🔧 Template cho Promotions:**
1. **Create Program** → Name: "Auto [Discount]% - [CATEGORY]"
2. **Type:** Promotions  
3. **Rules:** Automatic → Min: [AMOUNT] → Category: [CATEGORY]
4. **Rewards:** Discount → [%] → Specific → [CATEGORY]
5. **Lucky Wheel:** ✅ → [%] → 🔥
6. **Save**

### **🔧 Template cho Gift Cards:**
1. **Create Program** → Name: "Gift Card [VALUE]k"
2. **Type:** Gift Card
3. **Rules:** Per money spent → 1 point per VND
4. **Rewards:** Per point → 1:1 → Points: [VALUE]
5. **Lucky Wheel:** ✅ → [%] → 🎁
6. **Save** → Generate Gift Cards

---

## 🎯 **LUCKY WHEEL OPTIMIZATION**

### **📊 Recommended Probabilities:**
- **High Value (50k+):** 5-10%
- **Medium Value (20-50k):** 15-20%  
- **Low Value (<20k):** 25-30%
- **Special Offers:** 100% (limited time)

### **🎨 Icon Mapping:**
```
Coupons → 🎫    Gift Card → 🎁
Loyalty → ⭐    Promotion → 🔥  
eWallet → 💳   Promo Code → 🏷️
Buy X Get Y → 🛒   Next Order → 📅
```

### **🔄 Rotation Strategy:**
- **Weekly:** Rotate high-value offers
- **Monthly:** Change program mix
- **Seasonal:** Holiday-specific programs
- **Flash:** Limited-time 100% probability

---

## 🚨 **TROUBLESHOOTING**

### **❌ Program không hoạt động:**
- [ ] Check Active = True
- [ ] Check Start/End dates
- [ ] Verify minimum conditions
- [ ] Test with small order

### **❌ Lucky Wheel không hiển thị:**
- [ ] Check is_lucky_wheel = True
- [ ] Verify program is active
- [ ] Check date validity
- [ ] Restart Odoo service

### **❌ Voucher không apply:**
- [ ] Check code spelling
- [ ] Verify minimum purchase
- [ ] Check product restrictions
- [ ] Ensure not expired/used

### **❌ Discount không đúng:**
- [ ] Check discount mode (% vs fixed)
- [ ] Verify apply on (order vs specific)
- [ ] Check currency settings
- [ ] Review tax calculations

---

## 📱 **MOBILE-FRIENDLY TIPS**

### **🎯 Program Names:**
- **Keep short:** Max 30 characters
- **Clear benefit:** "Giảm 15%" not "Program A"
- **Include conditions:** "Từ 200k" 
- **Action-oriented:** "Mua 2 Tặng 1"

### **📝 Descriptions:**
- **One line:** Brief, clear benefit
- **No jargon:** Simple Vietnamese
- **Include value:** "Tiết kiệm 50k"
- **Call to action:** "Áp dụng ngay"

---

## 🎮 **GAMIFICATION IDEAS**

### **🎰 Lucky Wheel Themes:**
- **Daily Spin:** 1 spin per day
- **Purchase Unlock:** Spin after buying
- **Milestone Rewards:** Spin at spending levels
- **Seasonal Events:** Holiday-themed prizes

### **🏆 Program Combinations:**
- **Loyalty + Coupons:** Points unlock better coupons
- **Gift Card + Next Order:** Gift card gives next order bonus
- **Buy X Get Y + Promotion:** Stack discounts
- **eWallet + Loyalty:** Wallet refills earn points

---

## 📊 **SUCCESS METRICS**

### **📈 Track These KPIs:**
- **Participation Rate:** % customers using programs
- **Redemption Rate:** % coupons/cards used
- **Average Order Value:** Impact on AOV
- **Customer Retention:** Repeat purchase rate
- **Revenue Attribution:** Sales from programs

### **🎯 Optimization Targets:**
- **Participation:** >30%
- **Redemption:** >60%
- **AOV Increase:** >15%
- **Retention:** >25%
- **ROI:** >300%

---

## 🔧 **MAINTENANCE CHECKLIST**

### **📅 Weekly:**
- [ ] Review program performance
- [ ] Check coupon inventory
- [ ] Monitor redemption rates
- [ ] Update Lucky Wheel probabilities

### **📅 Monthly:**
- [ ] Analyze customer feedback
- [ ] A/B test new offers
- [ ] Refresh program mix
- [ ] Update seasonal themes

### **📅 Quarterly:**
- [ ] Full program audit
- [ ] ROI analysis
- [ ] Strategy adjustment
- [ ] System optimization

---

**🚀 Quick Reference này giúp bạn setup và manage Odoo Loyalty System hiệu quả! 💎✨**
