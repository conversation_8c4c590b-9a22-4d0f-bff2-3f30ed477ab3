# 📚 ODOO 18 LOYALTY SYSTEM - HƯỚNG DẪN CHI TIẾT CHO ADMIN

## 🎯 **TỔNG QUAN**

Hướng dẫn này sẽ chỉ bạn cách tạo tất cả 8 loại chương trình khuyến mại trong Odoo 18 qua giao diện web.

### **🔗 Truy cập Loyalty System:**
1. **Đăng nhập Odoo** với quyền admin
2. **Vào menu:** Apps → Tìm "Loyalty" → Install (nếu chưa có)
3. **Truy cập:** Loyalty → Programs

---

## 🎫 **1. COUPONS - Phiếu giảm giá**

### **📋 Mục đích:** Tạo mã giảm giá một lần sử dụng

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program mới**
1. **Vào:** Loyalty → Programs → Create
2. **Program Name:** "Phiếu giảm 15% - SAVE15"
3. **Program Type:** Chọn "Coupons"

#### **BƯỚC 2: <PERSON><PERSON><PERSON> hình c<PERSON> bản**
- **Start Date:** Ngày bắt đầu (tùy chọn)
- **End Date:** Ngày kết thúc (tùy chọn)
- **Company:** Chọn công ty
- **Currency:** Chọn tiền tệ (VND/USD)

#### **BƯỚC 3: Tab "Conditional Rules" (Quy tắc có điều kiện)**
**Ý nghĩa:** Điều kiện để khách hàng được áp dụng khuyến mại

**Các trường quan trọng:**
- **Application:** "With a promotion code" (Cần nhập mã)
- **Discount code:** "SAVE15" (Mã khách hàng nhập)
- **Minimum Purchase:** 200,000 VND (Đơn hàng tối thiểu)
- **Minimum Quantity:** 1 (Số lượng tối thiểu)

**Products/Categories (Tùy chọn):**
- **Products:** Chọn sản phẩm cụ thể
- **Categories:** Chọn danh mục sản phẩm
- **Product Tag:** Chọn tag sản phẩm

#### **BƯỚC 4: Tab "Rewards" (Phần thưởng)**
**Ý nghĩa:** Phần thưởng khách hàng nhận được

**Click "Add a reward":**
- **Reward Type:** "Discount" (Giảm giá)
- **Discount Mode:** "Percentage" (Giảm theo %)
- **Discount:** 15 (Giảm 15%)
- **Apply On:** "On Order" (Áp dụng toàn đơn)
- **Points needed:** 1 (Điểm cần để đổi)

#### **BƯỚC 5: Lucky Wheel Configuration (Nếu có)**
- **Lucky Wheel:** ✅ Check
- **Lucky Wheel Probability (%):** 20 (Chỉ để hiển thị)
- **Lucky Wheel Icon:** 🎫

#### **BƯỚC 6: Save và Generate Coupons**
1. **Click Save**
2. **Click "Generate Coupons"** (nút màu xanh)
3. **Nhập số lượng:** 100 coupons
4. **Click Generate**

### **✅ Kết quả:**
- Khách hàng nhập mã "SAVE15"
- Đơn hàng từ 200k → Giảm 15%
- Mỗi mã chỉ dùng 1 lần

---

## 🔥 **2. PROMOTIONS - Khuyến mãi tự động**

### **📋 Mục đích:** Tự động giảm giá khi đủ điều kiện

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "Jewelry Sale - Tự động giảm 10%"
- **Program Type:** "Promotions"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Application:** "Automatic" (Tự động)
- **Minimum Purchase:** 500,000 VND
- **Categories:** Chọn "Jewelry" (nếu có)

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Discount"
- **Discount Mode:** "Percentage"
- **Discount:** 10
- **Apply On:** "On specific products"
- **Categories:** Chọn "Jewelry"

### **✅ Kết quả:**
- Khách mua jewelry từ 500k → Tự động giảm 10%
- Không cần nhập mã

---

## 🎁 **3. GIFT CARD - Thẻ quà tặng**

### **📋 Mục đích:** Tạo thẻ quà tặng có giá trị tiền

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "Thẻ quà tặng 100k"
- **Program Type:** "Gift Card"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Reward:** 1 (Điểm thưởng mỗi đơn)
- **Reward Mode:** "Per money spent" (Theo tiền chi tiêu)

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Discount"
- **Discount Mode:** "Per point"
- **Discount:** 1 (1 điểm = 1 VND)
- **Points needed:** 100000 (100k điểm = 100k VND)

#### **BƯỚC 4: Generate Gift Cards**
1. **Click "Generate Gift Cards"**
2. **Nhập số lượng và giá trị**
3. **Click Generate**

### **✅ Kết quả:**
- Thẻ quà tặng trị giá 100k
- Dùng dần trong các đơn hàng

---

## ⭐ **4. LOYALTY - Thẻ khách hàng thân thiết**

### **📋 Mục đích:** Tích điểm đổi thưởng

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "Khách hàng thân thiết"
- **Program Type:** "Loyalty Cards"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Application:** "Automatic"
- **Reward:** 1 (1 điểm mỗi 1000 VND)
- **Reward Mode:** "Per money spent"
- **Minimum Purchase:** 50,000 VND

#### **BƯỚC 3: Tab "Rewards"**
**Reward 1: Giảm 5% (100 điểm)**
- **Reward Type:** "Discount"
- **Discount Mode:** "Percentage"
- **Discount:** 5
- **Points needed:** 100

**Reward 2: Giảm 10% (200 điểm)**
- **Add a reward**
- **Discount:** 10
- **Points needed:** 200

### **✅ Kết quả:**
- Khách tích điểm mỗi lần mua
- 100 điểm → Giảm 5%
- 200 điểm → Giảm 10%

---

## 💳 **5. EWALLET - Ví điện tử**

### **📋 Mục đích:** Nạp tiền trước, dùng dần

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "eWallet - Ví điện tử"
- **Program Type:** "eWallet"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Reward:** 1
- **Reward Mode:** "Per money spent"

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Discount"
- **Discount Mode:** "Per point"
- **Discount:** 1 (1 điểm = 1 VND)

#### **BƯỚC 4: Generate eWallet**
1. **Click "Generate eWallet"**
2. **Chọn customers**
3. **Nhập số tiền nạp**

### **✅ Kết quả:**
- Khách có ví điện tử
- Dùng dần trong đơn hàng

---

## 🏷️ **6. PROMO CODE - Mã giảm giá sản phẩm**

### **📋 Mục đích:** Giảm giá cho sản phẩm/danh mục cụ thể

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "RING50 - Giảm 50k cho nhẫn"
- **Program Type:** "Discount Code"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Application:** "With a promotion code"
- **Discount code:** "RING50"
- **Product Tag:** Chọn "Ring" (nhẫn)
- **Minimum Quantity:** 1

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Discount"
- **Discount Mode:** "Fixed Amount"
- **Fixed Amount:** 50,000 VND
- **Apply On:** "On specific products"
- **Product Tag:** "Ring"

### **✅ Kết quả:**
- Khách nhập "RING50"
- Giảm 50k cho nhẫn

---

## 🛒 **7. BUY X GET Y - Mua X Tặng Y**

### **📋 Mục đích:** Mua đủ số lượng → Tặng sản phẩm

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "Mua 2 Tặng 1"
- **Program Type:** "Buy X Get Y"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Application:** "Automatic"
- **Minimum Quantity:** 2
- **Reward:** 1 (1 điểm mỗi sản phẩm)
- **Reward Mode:** "Per unit paid"

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Free Product"
- **Free Product:** Chọn sản phẩm tặng
- **Quantity:** 1
- **Points needed:** 2

### **✅ Kết quả:**
- Mua 2 sản phẩm → Tự động tặng 1

---

## 📅 **8. NEXT ORDER COUPONS - Phiếu giảm giá đơn tiếp theo**

### **📋 Mục đích:** Đơn hiện tại → Coupon cho đơn sau

### **🔧 Các bước tạo:**

#### **BƯỚC 1: Tạo Program**
- **Program Name:** "Mua ngay - Giảm lần sau"
- **Program Type:** "Next Order Coupons"

#### **BƯỚC 2: Tab "Conditional Rules"**
- **Application:** "Automatic"
- **Minimum Purchase:** 300,000 VND
- **Reward:** 1
- **Reward Mode:** "Per order"

#### **BƯỚC 3: Tab "Rewards"**
- **Reward Type:** "Discount"
- **Discount Mode:** "Percentage"
- **Discount:** 20
- **Apply On:** "On Order"

### **✅ Kết quả:**
- Mua đơn 300k → Nhận coupon 20%
- Dùng cho đơn tiếp theo

---

## 🎰 **LUCKY WHEEL CONFIGURATION**

### **🔧 Để program xuất hiện trong Lucky Wheel:**

#### **Thêm vào mọi program:**
1. **Lucky Wheel:** ✅ Check
2. **Lucky Wheel Probability (%):** 10-30 (chỉ hiển thị)
3. **Lucky Wheel Icon:** Chọn icon 🎁💎🔥⭐🏆🎫💳🌟

#### **Icons phù hợp:**
- **Coupons:** 🎫
- **Gift Card:** 🎁
- **Loyalty:** ⭐
- **Promotion:** 🔥
- **eWallet:** 💳
- **Promo Code:** 🏷️
- **Buy X Get Y:** 🛒
- **Next Order:** 📅

---

## ✅ **CHECKLIST HOÀN THÀNH**

### **Sau khi tạo program:**
- [ ] **Test program** với đơn hàng thử
- [ ] **Check Lucky Wheel API** có hiển thị program
- [ ] **Verify voucher codes** được tạo đúng
- [ ] **Test checkout** với voucher
- [ ] **Check email notifications** (nếu có)

### **Best Practices:**
- **Tên program** rõ ràng, dễ hiểu
- **Điều kiện** hợp lý với business
- **Thời gian** có giới hạn để tạo urgency
- **Test thoroughly** trước khi launch

---

## 🔧 **FIELD MEANINGS - Ý nghĩa các trường**

### **📋 Program Level Fields:**

#### **Basic Information:**
- **Program Name:** Tên hiển thị của chương trình
- **Program Type:** Loại chương trình (8 loại)
- **Active:** Bật/tắt chương trình
- **Company:** Công ty áp dụng
- **Currency:** Tiền tệ sử dụng
- **Start Date:** Ngày bắt đầu hiệu lực
- **End Date:** Ngày kết thúc hiệu lực

#### **Advanced Settings:**
- **Pricelist:** Bảng giá áp dụng (tùy chọn)
- **Portal Visible:** Hiển thị trên portal khách hàng
- **Portal Point Name:** Tên điểm thưởng hiển thị

### **📋 Rule Level Fields:**

#### **Application Mode:**
- **Automatic:** Tự động áp dụng khi đủ điều kiện
- **With a promotion code:** Cần nhập mã để kích hoạt

#### **Product Conditions:**
- **Products:** Sản phẩm cụ thể
- **Categories:** Danh mục sản phẩm
- **Product Tag:** Tag sản phẩm
- **Product Domain:** Điều kiện phức tạp (dev mode)

#### **Order Conditions:**
- **Minimum Purchase:** Giá trị đơn hàng tối thiểu
- **Minimum Quantity:** Số lượng sản phẩm tối thiểu
- **Tax Mode:** Tính thuế (bao gồm/không bao gồm)

#### **Reward Points:**
- **Reward:** Số điểm thưởng
- **Reward Mode:** Cách tính điểm
  - **Per order:** Mỗi đơn hàng
  - **Per money spent:** Theo tiền chi tiêu
  - **Per unit paid:** Theo số lượng sản phẩm
- **Split per unit:** Tách coupon theo từng đơn vị

### **📋 Reward Level Fields:**

#### **Reward Type:**
- **Discount:** Giảm giá
- **Free Product:** Sản phẩm miễn phí

#### **Discount Settings:**
- **Discount Mode:**
  - **Percentage:** Giảm theo phần trăm
  - **Fixed Amount:** Giảm số tiền cố định
  - **Per point:** Giảm theo điểm (1 điểm = X VND)
- **Discount:** Giá trị giảm
- **Apply On:** Áp dụng cho
  - **On Order:** Toàn bộ đơn hàng
  - **On specific products:** Sản phẩm cụ thể
  - **On cheapest product:** Sản phẩm rẻ nhất

#### **Product Reward:**
- **Free Product:** Sản phẩm tặng
- **Quantity:** Số lượng tặng
- **Points needed:** Điểm cần để đổi thưởng

---

## 🚨 **COMMON MISTAKES - Lỗi thường gặp**

### **❌ Lỗi cấu hình:**
1. **Quên set Start/End Date** → Program chạy mãi mãi
2. **Minimum Purchase = 0** → Áp dụng cho mọi đơn hàng
3. **Points needed = 0** → Reward miễn phí
4. **Duplicate promo codes** → Lỗi validation
5. **Wrong currency** → Tính toán sai

### **❌ Lỗi logic:**
1. **Auto + With Code** → Conflict trong rules
2. **Current + Future** → Áp dụng sai thời điểm
3. **Product restrictions** không match với rewards
4. **Minimum conditions** quá cao → Không ai dùng được

### **✅ Best Practices:**
1. **Test với đơn hàng nhỏ** trước khi launch
2. **Set expiry date** để tạo urgency
3. **Clear naming convention** để dễ quản lý
4. **Document conditions** cho team support
5. **Monitor usage** và adjust theo feedback

---

## 📊 **MONITORING & ANALYTICS**

### **🔍 Theo dõi hiệu quả:**
1. **Vào Loyalty → Programs**
2. **Click vào program** → Xem statistics
3. **Check "Items" column** → Số lượng cards/coupons
4. **Monitor usage rate** → Tỷ lệ sử dụng

### **📈 Key Metrics:**
- **Total Order Count:** Tổng số đơn hàng sử dụng
- **Coupon Count:** Số lượng coupon đã tạo
- **Usage Rate:** Tỷ lệ sử dụng (used/total)
- **Revenue Impact:** Tác động đến doanh thu

### **🎯 Optimization Tips:**
- **A/B test** different discount rates
- **Seasonal adjustments** cho holidays
- **Customer segmentation** với pricelist
- **Regular review** và update conditions

---

**🎯 Với hướng dẫn chi tiết này, bạn có thể master Odoo 18 Loyalty System! 💎✨**
