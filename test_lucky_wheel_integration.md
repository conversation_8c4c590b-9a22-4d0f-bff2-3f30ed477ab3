# 🎰 LUCKY WHEEL INTEGRATION TEST PLAN

## 🎯 **MỤC TIÊU ĐÃ HOÀN THÀNH:**

### **✅ Lucky Wheel API - Sync với Odoo Loyalty System:**

#### **1. 📊 Dynamic Prize Loading:**
- **Source:** Lấy từ active Odoo loyalty programs
- **Real-time:** Sync với khuyến mại trong Odoo admin
- **Auto-update:** Admin tạo/sửa program → Tự động update prizes

#### **2. 🎲 Real Prize Generation:**
- **Won program:** Tạo voucher từ loyalty program thật
- **Unique codes:** Generate LUCKY + random + date
- **Odoo integration:** Tạo loyalty.card trong Odoo

#### **3. 🔄 Complete Integration:**
- **API endpoints:** `/api/lucky-wheel/prizes`, `/api/lucky-wheel/spin`
- **Voucher creation:** Từ existing loyalty programs
- **Auto-apply:** Voucher tự động áp dụng vào checkout

---

## 🧪 **TEST PLAN:**

### **STEP 1: Setup Loyalty Programs trong Odoo (5 mins)**

#### **Tạo test loyalty programs:**
1. **Vào Odoo Admin → Loyalty Programs**
2. **Tạo program 1:**
   - Name: "Lucky Wheel - Giảm 10%"
   - Type: Coupons
   - Trigger: With code
   - Reward: 10% discount
   - Active: ✅

3. **Tạo program 2:**
   - Name: "Lucky Wheel - Giảm 50k"
   - Type: Gift Card
   - Trigger: With code
   - Reward: 50,000 VND fixed discount
   - Active: ✅

4. **Tạo program 3:**
   - Name: "Lucky Wheel - Free Shipping"
   - Type: Promotion
   - Trigger: With code
   - Reward: Free delivery
   - Active: ✅

### **STEP 2: Deploy Lucky Wheel API (10 mins)**

#### **Copy files to server:**
```bash
# Copy API controller
scp jewelry_ecommerce/controllers/api_lucky_wheel.py server:/root/noithat/addons/jewelry_ecommerce/controllers/

# Copy model
scp jewelry_ecommerce/models/lucky_wheel_spin.py server:/root/noithat/addons/jewelry_ecommerce/models/

# Update __init__.py files
# Add imports for new files
```

#### **Upgrade module:**
1. **Vào Odoo Admin → Apps**
2. **Find jewelry_ecommerce module**
3. **Click Upgrade**
4. **Verify new model created:** lucky.wheel.spin

### **STEP 3: Test Lucky Wheel APIs (10 mins)**

#### **Test 1: Get Prizes from Loyalty Programs**
```bash
curl "https://noithat.erpcloud.vn/api/lucky-wheel/prizes"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "prizes": [
      {
        "id": 1,
        "name": "Giảm 10%",
        "type": "discount",
        "value": 10,
        "program_id": 1,
        "program_type": "coupons",
        "probability": 20,
        "description": "Lucky Wheel - Giảm 10%",
        "color": "#FFD700",
        "icon": "🎫"
      },
      {
        "id": 2,
        "name": "Giảm 50,000đ",
        "type": "discount",
        "value": 50000,
        "program_id": 2,
        "program_type": "gift_card",
        "probability": 15,
        "description": "Lucky Wheel - Giảm 50k",
        "color": "#FF6B6B",
        "icon": "🎁"
      }
    ]
  }
}
```

#### **Test 2: Spin Lucky Wheel**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_lucky_wheel" \
  -d '{"email": "<EMAIL>"}'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "prize": {
      "id": 1,
      "name": "Giảm 10%",
      "type": "discount",
      "value": 10,
      "description": "Lucky Wheel - Giảm 10%",
      "color": "#FFD700",
      "icon": "🎫"
    },
    "voucher_code": "LUCKY12340526",
    "spin_time": "2025-05-26T10:30:00",
    "spins_remaining": 9
  }
}
```

#### **Test 3: Verify Voucher Created in Odoo**
1. **Vào Odoo Admin → Loyalty → Loyalty Cards**
2. **Tìm voucher code:** LUCKY12340526
3. **Verify:**
   - Program: "Lucky Wheel - Giảm 10%"
   - Code: LUCKY12340526
   - Partner: Public Partner
   - Status: Active

#### **Test 4: Test Voucher in Checkout**
```bash
# Add product to cart
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_lucky_wheel" \
  -d '{"product_id": 13, "variant_id": 23, "quantity": 1}'

# Create order with lucky wheel voucher
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_lucky_wheel" \
  -d '{
    "customer": {
      "full_name": "Lucky Test",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Lucky Address"
    },
    "payment_method": "cod",
    "voucher_code": "LUCKY12340526"
  }'
```

**Expected:** Order created with 10% discount applied

#### **Test 5: Test Daily Spin Limits**
```bash
# Spin 10 times with same session
for i in {1..10}; do
  curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
    -H "Content-Type: application/json" \
    -H "X-Session-ID: limit_test" \
    -d '{"email": "<EMAIL>"}'
  echo "Spin $i completed"
done

# 11th spin should fail
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: limit_test" \
  -d '{"email": "<EMAIL>"}'
```

**Expected:** 11th spin returns error "Daily spin limit reached"

---

## 🎯 **INTEGRATION VERIFICATION:**

### **✅ Checklist:**
- [ ] Lucky Wheel API deployed successfully
- [ ] Prizes load from active Odoo loyalty programs
- [ ] Spin generates real voucher codes
- [ ] Voucher codes created in Odoo loyalty.card
- [ ] Vouchers work in checkout API
- [ ] Daily spin limits enforced
- [ ] Spin history tracked
- [ ] Error handling works

### **🔧 Admin Workflow:**
1. **Admin tạo loyalty program mới trong Odoo**
2. **Program tự động xuất hiện trong Lucky Wheel prizes**
3. **User quay wheel và win program**
4. **Voucher code được tạo từ program đó**
5. **User dùng voucher trong checkout**
6. **Discount được apply theo program rules**

---

## 🚀 **FRONTEND INTEGRATION:**

### **Update LuckyWheelAPI.tsx:**
```typescript
// Component đã sẵn sàng, chỉ cần:
// 1. Replace existing LuckyWheel component
// 2. Test với real API endpoints
// 3. Verify voucher auto-apply works
```

### **Test Frontend:**
```bash
# Replace component
mv src/components/LuckyWheel.tsx src/components/LuckyWheel.backup.tsx
mv src/components/LuckyWheelAPI.tsx src/components/LuckyWheel.tsx

# Test in browser
npm run dev
# Open lucky wheel, verify:
# - Prizes load from API
# - Spin creates real vouchers
# - Vouchers auto-apply to cart
```

---

## 🎉 **SUCCESS CRITERIA:**

### **✅ Backend Integration:**
- Lucky Wheel API sync với Odoo loyalty programs
- Real voucher generation từ existing programs
- Seamless integration với checkout flow

### **✅ Frontend Integration:**
- Dynamic prize display từ API
- Real-time spin với actual rewards
- Auto-apply vouchers to cart

### **✅ Admin Experience:**
- Tạo loyalty program → Tự động có trong Lucky Wheel
- Full control over prizes và probabilities
- Track spin history và voucher usage

### **✅ User Experience:**
- Exciting lucky wheel với real rewards
- Instant voucher application
- Clear daily limits và history

---

## 🎯 **FINAL RESULT:**

**🎰 Lucky Wheel hoàn toàn sync với Odoo Loyalty System:**
- **Dynamic Prizes:** Từ active loyalty programs
- **Real Vouchers:** Tạo loyalty cards thật trong Odoo
- **Seamless Integration:** Auto-apply vào checkout
- **Admin Control:** Full management qua Odoo interface

**🚀 Ready for production deployment! 💎✨**
