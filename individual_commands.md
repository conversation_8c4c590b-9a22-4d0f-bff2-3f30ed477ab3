# 🎰 INDIVIDUAL COMMANDS - Test từng lệnh riêng biệt

## **🎯 HƯỚNG DẪN: Test từng API call riêng lẻ**

### **Khi API server stable, chạy từng lệnh này:**

---

## **🎰 BƯỚC 1: KIỂM TRA KHUYẾN MẠI**

### **Command:**
```bash
curl "https://noithat.erpcloud.vn/api/lucky-wheel/promotions"
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "total_count": 13,
    "promotions": [...]
  }
}
```

### **Check:**
- ✅ success = true
- ✅ total_count = 13
- ✅ promotions array có data

---

## **🎲 BƯỚC 2: QUAY VÒNG MAY MẮN**

### **Command:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_session_$(date +%s)" \
  -d '{"email": "<EMAIL>"}'
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "prize": {
      "name": "Phiếu giảm 15% - SAVE15",
      "program_type": "coupons"
    },
    "voucher_code": "044a-1234-5678"
  }
}
```

### **Save Voucher Code:**
```bash
# Copy voucher_code từ response
VOUCHER_CODE="044a-1234-5678"  # Replace với code thực tế
```

---

## **🎟️ BƯỚC 3: KIỂM TRA VOUCHER**

### **Command:**
```bash
# Replace VOUCHER_CODE với code từ bước 2
curl "https://noithat.erpcloud.vn/api/vouchers/validate/VOUCHER_CODE"

# Ví dụ:
curl "https://noithat.erpcloud.vn/api/vouchers/validate/044a-1234-5678"
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "discount_info": {
      "discount_percent": 15.0
    },
    "rule_info": {
      "minimum_amount": 200000.0
    }
  }
}
```

### **Check:**
- ✅ is_valid = true
- ✅ discount_percent có giá trị
- ✅ minimum_amount requirements

---

## **📦 BƯỚC 4: LẤY DANH SÁCH SẢN PHẨM**

### **Command:**
```bash
curl "https://noithat.erpcloud.vn/api/products"
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": 7,
        "name": "Bông tai Kim cương Vàng trắng 14K",
        "price": 15750000
      }
    ]
  }
}
```

### **Choose Product:**
```bash
# Pick product với price >= minimum_amount
PRODUCT_ID=7  # Bông tai Kim cương
```

---

## **🛒 BƯỚC 5: THÊM VÀO GIỎ HÀNG**

### **Command:**
```bash
# Replace SESSION_ID và PRODUCT_ID
SESSION_ID="test_session_$(date +%s)"
PRODUCT_ID=7

curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{
    \"product_id\": $PRODUCT_ID,
    \"quantity\": 1
  }"
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "cart": {
      "total_amount": 15750000,
      "items": [...]
    }
  }
}
```

### **Check:**
- ✅ success = true
- ✅ total_amount >= minimum voucher requirement
- ✅ items array có sản phẩm

---

## **💳 BƯỚC 6: TẠO ĐƠN HÀNG**

### **Command:**
```bash
# Replace SESSION_ID và VOUCHER_CODE với values thực tế
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer_info": {
      "full_name": "Test Lucky Customer",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Test Street"
    },
    "voucher_code": "VOUCHER_CODE",
    "payment_method": "cod",
    "notes": "Test order with Lucky Wheel voucher"
  }'
```

### **Expected Output:**
```json
{
  "success": true,
  "data": {
    "order": {
      "id": "SO001234",
      "name": "SO001234",
      "amount_untaxed": 15750000,
      "discount_amount": 2362500,
      "total_amount": 13387500
    }
  }
}
```

### **Check:**
- ✅ success = true
- ✅ order.id có giá trị
- ✅ discount_amount > 0
- ✅ total_amount = amount_untaxed - discount_amount

---

## **🔍 BƯỚC 7: TÌM TRONG ODOO**

### **Search in Odoo Backend:**
```
1. Sales → Orders → Sales Orders
2. Remove filters (click X on filter chips)
3. Search by:
   - Customer: "Test Lucky Customer"
   - Email: "<EMAIL>"
   - Order: "SO001234"
   - Notes: contains "Lucky Wheel"
```

### **Expected in Odoo:**
- ✅ Order exists với correct customer
- ✅ Total amount matches API response
- ✅ Discount applied correctly
- ✅ Products match cart contents

---

## **📋 SAMPLE VALUES FOR TESTING**

### **If Lucky Wheel Fails, Use Sample Vouchers:**
```bash
# Test với sample vouchers
curl "https://noithat.erpcloud.vn/api/vouchers/validate/SAVE15-001"
curl "https://noithat.erpcloud.vn/api/vouchers/validate/GIFT100K-001"
curl "https://noithat.erpcloud.vn/api/vouchers/validate/RING50-001"
curl "https://noithat.erpcloud.vn/api/vouchers/validate/TEST-COUPON-15"
```

### **Sample Session IDs:**
```bash
SESSION_ID="manual_test_$(date +%s)"
SESSION_ID="lucky_wheel_test_123"
SESSION_ID="step_by_step_test"
```

### **Sample Customer Emails:**
```bash
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
"<EMAIL>"
```

---

## **🚨 TROUBLESHOOTING INDIVIDUAL STEPS**

### **❌ Step 1 Fails (Promotions):**
```bash
# Check if API is responding
curl -I "https://noithat.erpcloud.vn/api/products"
# Should return 200 OK quickly
```

### **❌ Step 2 Fails (Spin):**
```bash
# Use sample voucher instead
VOUCHER_CODE="SAVE15-001"
# Continue with step 3
```

### **❌ Step 3 Fails (Voucher):**
```bash
# Try different sample vouchers
curl "https://noithat.erpcloud.vn/api/vouchers/validate/GIFT100K-001"
```

### **❌ Step 4 Fails (Products):**
```bash
# Check if products exist
curl "https://noithat.erpcloud.vn/api/products" | jq '.data.products | length'
```

### **❌ Step 5 Fails (Cart):**
```bash
# Check cart first
curl -H "X-Session-ID: $SESSION_ID" "https://noithat.erpcloud.vn/api/cart/get"
```

### **❌ Step 6 Fails (Order):**
```bash
# Try without voucher first
# Remove "voucher_code" field from request
```

---

## **⚡ QUICK TEST SEQUENCE**

### **When API is responsive:**
```bash
# 1. Quick check
curl -s "https://noithat.erpcloud.vn/api/lucky-wheel/promotions" | jq '.data.total_count'

# 2. If returns 13, continue with spin
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: quick_test" \
  -d '{"email": "<EMAIL>"}' | jq '.data.voucher_code'

# 3. Use returned voucher code in subsequent steps
```

---

**🎯 Test từng command riêng lẻ để identify exactly where issues occur! 🔍✨**
