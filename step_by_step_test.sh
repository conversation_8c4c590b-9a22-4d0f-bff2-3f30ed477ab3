#!/bin/bash

# 🎰 STEP BY STEP TEST: Lucky Wheel → Order Creation
# Test từng bước một cách chi tiết

echo "🎰 STEP BY STEP TEST: LUCKY WHEEL → ORDER"
echo "========================================"

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="step_test_$(date +%s)"
EMAIL="<EMAIL>"

echo "📧 Email: $EMAIL"
echo "🆔 Session: $SESSION_ID"
echo ""

# ========================================
# BƯỚC 1: KIỂM TRA DANH SÁCH KHUYẾN MẠI
# ========================================
echo "🎯 BƯỚC 1: KIỂM TRA DANH SÁCH KHUYẾN MẠI"
echo "======================================="

echo "   📋 Đang lấy danh sách khuyến mại..."
PROMOTIONS_RESPONSE=$(timeout 20 curl -s "$BASE_URL/lucky-wheel/promotions" 2>/dev/null)

if [ $? -eq 0 ] && echo "$PROMOTIONS_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    PROMOTION_COUNT=$(echo "$PROMOTIONS_RESPONSE" | jq '.data.total_count')
    echo "   ✅ Thành công! Tìm thấy $PROMOTION_COUNT khuyến mại"
    
    echo ""
    echo "   🏆 Top 5 khuyến mại có sẵn:"
    echo "$PROMOTIONS_RESPONSE" | jq -r '.data.promotions[0:5][] | "      - \(.name) (\(.program_type)) - Tỷ lệ: \(.lucky_wheel_probability)% - \(.lucky_wheel_icon)"'
    
    # Lưu để reference
    echo "$PROMOTIONS_RESPONSE" > /tmp/step1_promotions.json
    echo "   💾 Đã lưu vào /tmp/step1_promotions.json"
    
else
    echo "   ❌ Lỗi: Không thể lấy danh sách khuyến mại"
    echo "   📄 Response: $PROMOTIONS_RESPONSE"
    echo ""
    echo "🚨 DỪNG TEST: API không phản hồi"
    echo "   → Thử lại khi server ổn định"
    echo "   → Hoặc test manual theo hướng dẫn bên dưới"
    exit 1
fi

echo ""
read -p "👆 Nhấn Enter để tiếp tục BƯỚC 2 (Quay vòng may mắn)..."
echo ""

# ========================================
# BƯỚC 2: QUAY VÒNG MAY MẮN
# ========================================
echo "🎲 BƯỚC 2: QUAY VÒNG MAY MẮN"
echo "============================"

echo "   🎰 Đang quay vòng may mắn..."
SPIN_RESPONSE=$(timeout 20 curl -s -X POST "$BASE_URL/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{\"email\": \"$EMAIL\"}" 2>/dev/null)

if [ $? -eq 0 ] && echo "$SPIN_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "   🎉 Quay thành công!"
    
    # Lấy thông tin giải thưởng
    PRIZE_NAME=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.name // "Unknown"')
    PRIZE_TYPE=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.program_type // "Unknown"')
    VOUCHER_CODE=$(echo "$SPIN_RESPONSE" | jq -r '.data.voucher_code // "None"')
    PRIZE_ICON=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.lucky_wheel_icon // "🎁"')
    
    echo ""
    echo "   🏆 GIẢI THƯỞNG NHẬN ĐƯỢC:"
    echo "   ========================"
    echo "   🎁 Tên: $PRIZE_NAME"
    echo "   🎯 Loại: $PRIZE_TYPE"
    echo "   🎟️ Mã voucher: $VOUCHER_CODE"
    echo "   $PRIZE_ICON Icon: $PRIZE_ICON"
    
    # Lưu kết quả quay
    echo "$SPIN_RESPONSE" > /tmp/step2_spin_result.json
    echo "   💾 Đã lưu vào /tmp/step2_spin_result.json"
    
    if [ "$VOUCHER_CODE" = "None" ] || [ "$VOUCHER_CODE" = "null" ]; then
        echo ""
        echo "   ⚠️ Không nhận được mã voucher - có thể là khuyến mại tự động"
        echo "   🔄 Sử dụng mã voucher mẫu để test: SAVE15-001"
        VOUCHER_CODE="SAVE15-001"
        PRIZE_NAME="Sample 15% Discount"
    fi
    
else
    echo "   ❌ Lỗi: Không thể quay vòng may mắn"
    echo "   📄 Response: $SPIN_RESPONSE"
    echo ""
    echo "   🔄 Sử dụng mã voucher mẫu để tiếp tục test: SAVE15-001"
    VOUCHER_CODE="SAVE15-001"
    PRIZE_NAME="Sample 15% Discount"
    PRIZE_TYPE="coupons"
fi

echo ""
read -p "👆 Nhấn Enter để tiếp tục BƯỚC 3 (Kiểm tra voucher)..."
echo ""

# ========================================
# BƯỚC 3: KIỂM TRA VOUCHER
# ========================================
echo "🎟️ BƯỚC 3: KIỂM TRA VOUCHER"
echo "==========================="

echo "   🔍 Đang kiểm tra voucher: $VOUCHER_CODE"
VOUCHER_RESPONSE=$(timeout 15 curl -s "$BASE_URL/vouchers/validate/$VOUCHER_CODE" 2>/dev/null)

if [ $? -eq 0 ] && echo "$VOUCHER_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "   ✅ Voucher hợp lệ!"
    
    # Lấy thông tin voucher
    VOUCHER_TYPE=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.program_type // "Unknown"')
    DISCOUNT_PERCENT=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_percent // 0')
    DISCOUNT_FIXED=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_fixed // 0')
    MIN_AMOUNT=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.rule_info.minimum_amount // 0')
    
    echo ""
    echo "   📋 CHI TIẾT VOUCHER:"
    echo "   ==================="
    echo "   🎯 Loại: $VOUCHER_TYPE"
    echo "   💰 Giảm giá: ${DISCOUNT_PERCENT}% / ${DISCOUNT_FIXED} VND"
    echo "   📊 Đơn tối thiểu: $MIN_AMOUNT VND"
    
    # Lưu thông tin voucher
    echo "$VOUCHER_RESPONSE" > /tmp/step3_voucher_validation.json
    echo "   💾 Đã lưu vào /tmp/step3_voucher_validation.json"
    
else
    echo "   ❌ Lỗi: Voucher không hợp lệ hoặc API lỗi"
    echo "   📄 Response: $VOUCHER_RESPONSE"
    echo ""
    echo "   ⚠️ Tiếp tục test với voucher này (có thể vẫn hoạt động)"
    MIN_AMOUNT=200000  # Default minimum
fi

echo ""
read -p "👆 Nhấn Enter để tiếp tục BƯỚC 4 (Thêm sản phẩm vào giỏ)..."
echo ""

# ========================================
# BƯỚC 4: THÊM SẢN PHẨM VÀO GIỎ HÀNG
# ========================================
echo "🛒 BƯỚC 4: THÊM SẢN PHẨM VÀO GIỎ HÀNG"
echo "====================================="

echo "   📦 Đang lấy danh sách sản phẩm..."
PRODUCTS_RESPONSE=$(timeout 15 curl -s "$BASE_URL/products" 2>/dev/null)

if [ $? -eq 0 ] && echo "$PRODUCTS_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    PRODUCT_COUNT=$(echo "$PRODUCTS_RESPONSE" | jq '.data.products | length')
    echo "   ✅ Tìm thấy $PRODUCT_COUNT sản phẩm"
    
    # Lấy sản phẩm đầu tiên
    FIRST_PRODUCT_ID=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].id')
    FIRST_PRODUCT_NAME=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].name')
    FIRST_PRODUCT_PRICE=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].price')
    
    echo "   📋 Sản phẩm được chọn:"
    echo "      - ID: $FIRST_PRODUCT_ID"
    echo "      - Tên: $FIRST_PRODUCT_NAME"
    echo "      - Giá: $FIRST_PRODUCT_PRICE VND"
    
    # Kiểm tra đủ điều kiện minimum
    if [ "$FIRST_PRODUCT_PRICE" -lt "$MIN_AMOUNT" ]; then
        echo "   ⚠️ Giá sản phẩm ($FIRST_PRODUCT_PRICE) < Minimum ($MIN_AMOUNT)"
        echo "   🔄 Thêm 2 sản phẩm để đủ điều kiện"
        QUANTITY=2
    else
        QUANTITY=1
    fi
    
    echo ""
    echo "   ➕ Đang thêm $QUANTITY sản phẩm vào giỏ..."
    ADD_CART_RESPONSE=$(timeout 15 curl -s -X POST "$BASE_URL/cart/add" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: $SESSION_ID" \
      -d "{
        \"product_id\": $FIRST_PRODUCT_ID,
        \"quantity\": $QUANTITY
      }" 2>/dev/null)
    
    if [ $? -eq 0 ] && echo "$ADD_CART_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
        echo "   ✅ Đã thêm sản phẩm vào giỏ!"
        
        CART_TOTAL=$(echo "$ADD_CART_RESPONSE" | jq -r '.data.cart.total_amount // 0')
        CART_ITEMS=$(echo "$ADD_CART_RESPONSE" | jq '.data.cart.items | length // 0')
        
        echo ""
        echo "   📊 TỔNG KẾT GIỎ HÀNG:"
        echo "   ==================="
        echo "   📦 Số sản phẩm: $CART_ITEMS"
        echo "   💰 Tổng tiền: $CART_TOTAL VND"
        
        # Kiểm tra đủ điều kiện voucher
        if [ "$CART_TOTAL" -ge "$MIN_AMOUNT" ]; then
            echo "   ✅ Đủ điều kiện sử dụng voucher ($MIN_AMOUNT VND)"
        else
            echo "   ❌ Chưa đủ điều kiện voucher (cần $MIN_AMOUNT VND)"
        fi
        
        # Lưu trạng thái giỏ hàng
        echo "$ADD_CART_RESPONSE" > /tmp/step4_cart_with_products.json
        echo "   💾 Đã lưu vào /tmp/step4_cart_with_products.json"
        
    else
        echo "   ❌ Lỗi: Không thể thêm sản phẩm vào giỏ"
        echo "   📄 Response: $ADD_CART_RESPONSE"
        echo ""
        echo "🚨 DỪNG TEST: Không thể tiếp tục mà không có sản phẩm trong giỏ"
        exit 1
    fi
    
else
    echo "   ❌ Lỗi: Không thể lấy danh sách sản phẩm"
    echo "   📄 Response: $PRODUCTS_RESPONSE"
    exit 1
fi

echo ""
read -p "👆 Nhấn Enter để tiếp tục BƯỚC 5 (Tạo đơn hàng với voucher)..."
echo ""

# ========================================
# BƯỚC 5: TẠO ĐƠN HÀNG VỚI VOUCHER
# ========================================
echo "💳 BƯỚC 5: TẠO ĐƠN HÀNG VỚI VOUCHER"
echo "==================================="

echo "   📋 Đang tạo đơn hàng với voucher: $VOUCHER_CODE"

# Tạo dữ liệu đơn hàng
ORDER_DATA="{
  \"customer_info\": {
    \"full_name\": \"Lucky Winner Test\",
    \"email\": \"$EMAIL\",
    \"phone\": \"0123456789\",
    \"address\": \"123 Lucky Street, Test City\"
  },
  \"voucher_code\": \"$VOUCHER_CODE\",
  \"payment_method\": \"cod\",
  \"notes\": \"Đơn hàng test với voucher từ vòng quay may mắn: $PRIZE_NAME\"
}"

echo "   💳 Đang xử lý thanh toán..."
CHECKOUT_RESPONSE=$(timeout 25 curl -s -X POST "$BASE_URL/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "$ORDER_DATA" 2>/dev/null)

if [ $? -eq 0 ] && echo "$CHECKOUT_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "   🎉 TẠO ĐƠN HÀNG THÀNH CÔNG!"
    
    # Lấy thông tin đơn hàng
    ORDER_ID=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.id // "Unknown"')
    ORDER_NAME=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.name // "Unknown"')
    ORDER_STATE=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.state // "Unknown"')
    ORIGINAL_AMOUNT=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.amount_untaxed // 0')
    DISCOUNT_AMOUNT=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.discount_amount // 0')
    FINAL_TOTAL=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.total_amount // 0')
    
    echo ""
    echo "   🎯 CHI TIẾT ĐƠN HÀNG:"
    echo "   ===================="
    echo "   🆔 ID: $ORDER_ID"
    echo "   📄 Số đơn: $ORDER_NAME"
    echo "   📊 Trạng thái: $ORDER_STATE"
    echo "   💰 Tiền gốc: $ORIGINAL_AMOUNT VND"
    echo "   🎁 Giảm giá: $DISCOUNT_AMOUNT VND"
    echo "   💳 Tổng cuối: $FINAL_TOTAL VND"
    echo "   🎟️ Voucher: $VOUCHER_CODE"
    echo "   🏆 Từ giải: $PRIZE_NAME"
    
    # Tính toán tiết kiệm
    if [ "$DISCOUNT_AMOUNT" != "0" ] && [ "$DISCOUNT_AMOUNT" != "null" ]; then
        echo ""
        echo "   💸 TIẾT KIỆM: $DISCOUNT_AMOUNT VND"
        
        if [ "$ORIGINAL_AMOUNT" != "0" ]; then
            SAVINGS_PERCENT=$(echo "scale=1; $DISCOUNT_AMOUNT * 100 / $ORIGINAL_AMOUNT" | bc 2>/dev/null || echo "N/A")
            echo "   📊 Tỷ lệ tiết kiệm: ${SAVINGS_PERCENT}%"
        fi
    fi
    
    # Lưu chi tiết đơn hàng
    echo "$CHECKOUT_RESPONSE" > /tmp/step5_final_order.json
    echo "   💾 Đã lưu vào /tmp/step5_final_order.json"
    
    echo ""
    echo "   🔍 TÌM ĐƠN HÀNG TRONG ODOO:"
    echo "   ==========================="
    echo "   📍 Vào: Sales → Orders → Sales Orders"
    echo "   🔍 Tìm theo:"
    echo "      - Khách hàng: Lucky Winner Test"
    echo "      - Email: $EMAIL"
    echo "      - Số đơn: $ORDER_NAME"
    echo "      - Ghi chú: chứa '$PRIZE_NAME'"
    
else
    echo "   ❌ LỖI: KHÔNG THỂ TẠO ĐƠN HÀNG"
    echo "   📄 Response: $CHECKOUT_RESPONSE"
    
    # Phân tích lỗi
    if echo "$CHECKOUT_RESPONSE" | grep -q "voucher"; then
        echo "   🔍 Có thể lỗi liên quan đến voucher"
    elif echo "$CHECKOUT_RESPONSE" | grep -q "cart"; then
        echo "   🔍 Có thể lỗi liên quan đến giỏ hàng"
    elif echo "$CHECKOUT_RESPONSE" | grep -q "customer"; then
        echo "   🔍 Có thể lỗi liên quan đến thông tin khách hàng"
    fi
    
    # Lưu lỗi để debug
    echo "$CHECKOUT_RESPONSE" > /tmp/step5_order_error.json
    echo "   💾 Lỗi đã lưu vào /tmp/step5_order_error.json"
fi

echo ""

# ========================================
# TỔNG KẾT
# ========================================
echo "📊 TỔNG KẾT TEST TỪNG BƯỚC"
echo "=========================="

echo "🎰 Vòng quay may mắn:"
echo "   🏆 Giải thưởng: $PRIZE_NAME"
echo "   🎟️ Voucher: $VOUCHER_CODE"

echo ""
echo "🛒 Giỏ hàng:"
echo "   📦 Sản phẩm: $FIRST_PRODUCT_NAME"
echo "   💰 Tổng tiền: $CART_TOTAL VND"

echo ""
echo "💳 Đơn hàng:"
if [ ! -z "$ORDER_ID" ] && [ "$ORDER_ID" != "Unknown" ]; then
    echo "   ✅ Thành công: $ORDER_NAME"
    echo "   💰 Tổng cuối: $FINAL_TOTAL VND"
    echo "   🎁 Tiết kiệm: $DISCOUNT_AMOUNT VND"
else
    echo "   ❌ Thất bại - xem lỗi ở trên"
fi

echo ""
echo "📁 Files được tạo:"
echo "   - /tmp/step1_promotions.json"
echo "   - /tmp/step2_spin_result.json"
echo "   - /tmp/step3_voucher_validation.json"
echo "   - /tmp/step4_cart_with_products.json"
echo "   - /tmp/step5_final_order.json (hoặc step5_order_error.json)"

echo ""
echo "🎯 TEST TỪNG BƯỚC HOÀN THÀNH!"
