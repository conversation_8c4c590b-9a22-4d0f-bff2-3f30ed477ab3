<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- GENERIC TAX REPORT -->
        <record id="generic_tax_report" model="account.report">
            <field name="name">Generic Tax report</field>
            <field name="filter_multi_company">tax_units</field>
            <field name="filter_fiscal_position" eval="1"/>
            <field name="default_opening_date_filter">previous_tax_period</field>
            <field name="only_tax_exigible" eval="True"/>
            <field name="column_ids">
                <record id="generic_tax_report_column_net" model="account.report.column">
                    <field name="name">Net</field>
                    <field name="expression_label">net</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="generic_tax_report_column_tax" model="account.report.column">
                    <field name="name">Tax</field>
                    <field name="expression_label">tax</field>
                    <field name="figure_type">monetary</field>
                </record>
            </field>
        </record>

        <record id="generic_tax_report_account_tax" model="account.report">
            <field name="name">Group by: Account &gt; Tax </field>
            <field name="root_report_id" ref="generic_tax_report"/>
            <field name="availability_condition">always</field>
            <field name="column_ids">
                <record id="generic_tax_report_account_tax_column_net" model="account.report.column">
                    <field name="name">Net</field>
                    <field name="expression_label">net</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="generic_tax_report_account_tax_column_tax" model="account.report.column">
                    <field name="name">Tax</field>
                    <field name="expression_label">tax</field>
                    <field name="figure_type">monetary</field>
                </record>
            </field>
        </record>

        <record id="generic_tax_report_tax_account" model="account.report">
            <field name="name">Group by: Tax &gt; Account </field>
            <field name="root_report_id" ref="generic_tax_report"/>
            <field name="availability_condition">always</field>
            <field name="column_ids">
                <record id="generic_tax_report_tax_account_column_net" model="account.report.column">
                    <field name="name">Net</field>
                    <field name="expression_label">net</field>
                    <field name="figure_type">monetary</field>
                </record>
                <record id="generic_tax_report_tax_account_column_tax" model="account.report.column">
                    <field name="name">Tax</field>
                    <field name="expression_label">tax</field>
                    <field name="figure_type">monetary</field>
                </record>
            </field>
        </record>

    </data>
</odoo>
