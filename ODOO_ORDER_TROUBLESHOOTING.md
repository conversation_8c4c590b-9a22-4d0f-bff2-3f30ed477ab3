# 🔍 ODOO ORDER TROUBLESHOOTING GUIDE

## 🚨 **ISSUE: Orders Missing in Odoo Backend**

### **Possible Causes:**
1. **Orders created in different company**
2. **Orders in draft state (hidden by filters)**
3. **API endpoint not creating orders properly**
4. **Database sync issues**
5. **Permission/access problems**

---

## 🏢 **STEP 1: CHECK ODOO BACKEND LOCATIONS**

### **📋 1.1 Sales Orders (Main Location):**
```
Navigation: Sales → Orders → Sales Orders
```
**Check these filters:**
- **State:** Remove "Sales Order" filter → Show "All"
- **Date:** Set to "Today" or "This Week"
- **Company:** Switch between companies if multiple

### **📋 1.2 Quotations:**
```
Navigation: Sales → Orders → Quotations
```
**Why check:** Orders might be created as quotations first

### **📋 1.3 All Sales Documents:**
```
Navigation: Sales → Reporting → Sales Analysis
```
**Filter by:** Today's date, all states

---

## 🔍 **STEP 2: ADVANCED SEARCH FILTERS**

### **🔎 2.1 Search by Customer Email:**
In Sales Orders search bar:
```
partner_id.email = '<EMAIL>'
partner_id.email = '<EMAIL>'
partner_id.email = '<EMAIL>'
```

### **🔎 2.2 Search by Creation Date:**
```
create_date >= '2025-05-27 00:00:00'
create_date >= 'today'
```

### **🔎 2.3 Search by Order Notes:**
```
note ilike '%Debug order%'
note ilike '%Lucky Wheel%'
note ilike '%test%'
```

### **🔎 2.4 Search by All States:**
```
state in ('draft', 'sent', 'sale', 'done', 'cancel')
```

---

## 🏗️ **STEP 3: TECHNICAL INVESTIGATION**

### **⚙️ 3.1 Direct Database View:**
```
Navigation: Settings → Technical → Database Structure → Models
Search: sale.order
Click: sale.order → View Records
```

### **⚙️ 3.2 Check Recent Records:**
In sale.order records:
- **Filter:** `create_date >= today`
- **Sort:** By create_date (newest first)
- **Look for:** Recent entries

### **⚙️ 3.3 Check Logs:**
```
Navigation: Settings → Technical → Logging
Filter: Today's logs
Search: 'sale.order' or 'checkout' or 'api'
```

---

## 🏢 **STEP 4: COMPANY SWITCHING**

### **🔄 4.1 Switch Companies:**
1. **Top-right corner** → Company selector
2. **Try each company** if multiple exist
3. **Check orders** in each company

### **🔄 4.2 Multi-Company View:**
1. **Settings → Users & Companies → Companies**
2. **Check which companies** are active
3. **Verify API** creates orders in correct company

---

## 🧪 **STEP 5: TEST ORDER CREATION**

### **📝 5.1 Manual Order Creation:**
1. **Sales → Orders → Create**
2. **Add same customer:** <EMAIL>
3. **Add same product** used in API
4. **Compare** with API-created orders

### **📝 5.2 API Debug Test:**
Run debug script:
```bash
./debug_orders_missing.sh
```

Expected output will show:
- ✅ API connectivity
- ✅ Cart functionality  
- ✅/❌ Order creation result
- 📋 Specific order details to search for

---

## 🔧 **STEP 6: COMMON SOLUTIONS**

### **✅ 6.1 If Orders Exist but Hidden:**
**Problem:** Default filters hide draft orders
**Solution:** 
- Remove "Sales Order" state filter
- Show "All" states
- Check "Quotations" menu

### **✅ 6.2 If Orders in Wrong Company:**
**Problem:** API creates orders in different company
**Solution:**
- Switch companies in Odoo
- Check API company configuration
- Update API to specify correct company

### **✅ 6.3 If Orders Not Created:**
**Problem:** API endpoint has issues
**Solution:**
- Check API logs for errors
- Verify database connectivity
- Test with minimal order data
- Check Odoo server logs

### **✅ 6.4 If Permission Issues:**
**Problem:** User can't see orders
**Solution:**
- Check user permissions
- Try with admin user
- Verify access rights to sale.order model

---

## 📊 **STEP 7: VERIFICATION CHECKLIST**

### **✅ Orders Found - Success!**
If you find orders:
- [ ] **Verify order details** match API calls
- [ ] **Check customer information** is correct
- [ ] **Verify product lines** are accurate
- [ ] **Confirm totals** match expected amounts
- [ ] **Check voucher discounts** if applied

### **❌ Orders Still Missing - Further Investigation:**
If no orders found:
- [ ] **Check Odoo server logs** for errors
- [ ] **Verify API endpoint** is working
- [ ] **Test database connectivity**
- [ ] **Check module installation** (jewelry_ecommerce)
- [ ] **Verify user permissions**

---

## 🎯 **STEP 8: SPECIFIC SEARCH EXAMPLES**

### **🔍 Search for Debug Test Orders:**
```sql
-- In Technical → Database → sale.order
partner_id.email = '<EMAIL>'
AND create_date >= '2025-05-27'
```

### **🔍 Search for Lucky Wheel Orders:**
```sql
note ilike '%Lucky Wheel%'
OR note ilike '%voucher%'
OR partner_id.email ilike '%lucky%'
```

### **🔍 Search for API Orders:**
```sql
note ilike '%API%'
OR note ilike '%test%'
OR origin ilike '%API%'
```

---

## 📱 **STEP 9: MOBILE/BROWSER CACHE**

### **🔄 Clear Cache:**
1. **Hard refresh:** Ctrl+F5 (Windows) / Cmd+Shift+R (Mac)
2. **Clear browser cache**
3. **Try incognito/private mode**
4. **Different browser**

---

## 🚨 **EMERGENCY DEBUGGING**

### **If Nothing Works:**
1. **Check Odoo is running:** Access main dashboard
2. **Check database:** Other records (products, customers) visible?
3. **Check API module:** jewelry_ecommerce installed and updated?
4. **Check server logs:** Any Python/Odoo errors?
5. **Contact system admin:** Database/server issues?

---

## 📋 **EXPECTED RESULTS**

### **✅ Successful Order in Odoo:**
- **Customer:** Debug Test Customer / Lucky Winner
- **Email:** <EMAIL> / <EMAIL>
- **Products:** Jewelry items from API
- **State:** draft, sent, or sale
- **Notes:** Contains API/test references
- **Amount:** Matches API response

### **🎯 Order Details to Look For:**
- **Order Reference:** SO001234 (or similar)
- **Customer Name:** From API customer_info
- **Product Lines:** Items added to cart
- **Total Amount:** With/without discounts
- **Payment Method:** COD/Bank Transfer
- **Creation Date:** Recent (today)

---

**🎯 Follow this guide systematically to locate orders in Odoo backend! 🔍✨**
