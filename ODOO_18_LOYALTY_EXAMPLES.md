# 🎯 ODOO 18 LOYALTY SYSTEM - PRACTICAL EXAMPLES

## 📋 **PROGRAM CREATION EXAMPLES**

### **1. 🎟️ COUPONS - Mã giảm giá một lần**

#### **Scenario:** Tạo mã giảm 15% cho đơn hàng từ 200k
```python
program = env['loyalty.program'].create({
    'name': 'Giảm 15% cho đơn từ 200k',
    'program_type': 'coupons',
    'trigger': 'with_code',
    'applies_on': 'current',
    'active': True,
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'code': 'SAVE15',                    # Mã khách nhập
        'minimum_amount': 200000,            # Tối thiểu 200k
        'minimum_qty': 0,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 15,                      # G<PERSON><PERSON><PERSON> 15%
        'discount_applicability': 'order',   # Giảm toàn đơn
        'required_points': 1,
    })],
})
```

#### **Usage:**
- Customer nhập mã `SAVE15` khi checkout
- System check: đơn hàng >= 200k → Apply 15% discount
- Mã chỉ dùng được 1 lần

---

### **2. 🔥 PROMOTIONS - Khuyến mại tự động**

#### **Scenario:** Tự động giảm 10% khi mua jewelry từ 500k
```python
program = env['loyalty.program'].create({
    'name': 'Jewelry Sale - Giảm 10%',
    'program_type': 'promotion',
    'trigger': 'auto',                       # Tự động kích hoạt
    'applies_on': 'current',
    'active': True,
    'rule_ids': [(0, 0, {
        'mode': 'auto',
        'minimum_amount': 500000,            # Tối thiểu 500k
        'product_category_id': jewelry_category.id,  # Chỉ jewelry
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 10,
        'discount_applicability': 'specific', # Chỉ jewelry
        'discount_product_category_id': jewelry_category.id,
    })],
})
```

#### **Usage:**
- Customer thêm jewelry vào cart
- Khi total >= 500k → Tự động giảm 10%
- Không cần nhập mã

---

### **3. 📅 NEXT ORDER COUPONS - Coupon đơn tiếp theo**

#### **Scenario:** Mua từ 300k → Nhận coupon 20% cho lần mua sau
```python
program = env['loyalty.program'].create({
    'name': 'Mua ngay - Giảm lần sau',
    'program_type': 'next_order_coupons',
    'trigger': 'auto',
    'applies_on': 'future',                  # Dùng cho đơn sau
    'active': True,
    'rule_ids': [(0, 0, {
        'mode': 'auto',
        'minimum_amount': 300000,            # Mua từ 300k
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 20,                      # Giảm 20% lần sau
        'discount_applicability': 'order',
    })],
})
```

#### **Usage:**
- Customer mua đơn 300k → System tạo loyalty.card với code unique
- Email gửi code cho customer
- Customer dùng code cho đơn tiếp theo

---

### **4. 🏷️ PROMO CODE - Mã giảm giá sản phẩm**

#### **Scenario:** Mã RING50 giảm 50k cho nhẫn
```python
program = env['loyalty.program'].create({
    'name': 'Giảm 50k cho nhẫn',
    'program_type': 'promo_code',
    'trigger': 'with_code',
    'applies_on': 'current',
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'code': 'RING50',
        'product_tag_id': ring_tag.id,       # Chỉ nhẫn
        'minimum_qty': 1,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'fixed_amount',
        'discount_fixed_amount': 50000,      # Giảm 50k
        'discount_applicability': 'specific',
        'discount_product_tag_id': ring_tag.id,
    })],
})
```

---

## 🎫 **LOYALTY CARD MANAGEMENT**

### **🔧 Card Creation Strategies:**

#### **1. Pre-generate Cards:**
```python
# Tạo sẵn 100 cards cho program
cards = []
for i in range(100):
    cards.append({
        'program_id': program.id,
        'code': f'LUCKY{1000+i}',
        'points': 0,
        'active': True,
    })
env['loyalty.card'].create(cards)
```

#### **2. Dynamic Card Creation:**
```python
def create_card_for_program(program_id):
    """Tạo card mới khi cần"""
    return env['loyalty.card'].create({
        'program_id': program_id,
        'code': env['loyalty.card']._generate_code(),
        'points': 0,
        'active': True,
    })
```

### **🔍 Card Validation:**
```python
def is_card_valid(card):
    """Check card có hợp lệ không"""
    if not card.active:
        return False, "Card đã bị vô hiệu hóa"
    
    if card.expiration_date and card.expiration_date < fields.Date.today():
        return False, "Card đã hết hạn"
    
    # Check usage history
    if card.program_id.program_type == 'coupons':
        # Single use coupons
        used_orders = env['sale.order'].search([
            ('applied_coupon_ids.code', '=', card.code)
        ])
        if used_orders:
            return False, "Mã đã được sử dụng"
    
    return True, "Card hợp lệ"
```

---

## 🎰 **LUCKY WHEEL IMPLEMENTATION**

### **🔧 Program Setup for Lucky Wheel:**

#### **1. Tạo Programs cho Lucky Wheel:**
```python
# Program 1: Giảm 10%
program1 = env['loyalty.program'].create({
    'name': 'Lucky Wheel - Giảm 10%',
    'program_type': 'coupons',
    'trigger': 'with_code',
    'applies_on': 'current',
    'is_lucky_wheel': True,                  # Custom field
    'lucky_wheel_probability': 25,          # Display only
    'lucky_wheel_icon': '🎁',
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'minimum_amount': 100000,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 10,
        'discount_applicability': 'order',
    })],
})

# Program 2: Giảm 50k
program2 = env['loyalty.program'].create({
    'name': 'Lucky Wheel - Giảm 50k',
    'program_type': 'coupons',
    'trigger': 'with_code',
    'applies_on': 'current',
    'is_lucky_wheel': True,
    'lucky_wheel_probability': 15,
    'lucky_wheel_icon': '💎',
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'minimum_amount': 200000,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'fixed_amount',
        'discount_fixed_amount': 50000,
        'discount_applicability': 'order',
    })],
})
```

#### **2. Lucky Wheel Logic:**
```python
def get_lucky_wheel_programs():
    """Lấy programs cho Lucky Wheel"""
    today = fields.Date.today()
    return env['loyalty.program'].search([
        ('active', '=', True),
        ('is_lucky_wheel', '=', True),
        '|', ('date_to', '=', False), ('date_to', '>=', today),
        '|', ('date_from', '=', False), ('date_from', '<=', today),
    ])

def spin_lucky_wheel():
    """Quay Lucky Wheel"""
    programs = get_lucky_wheel_programs()
    if not programs:
        return None, "Không có chương trình nào"
    
    # Random chọn program
    selected_program = random.choice(programs)
    
    # Tạo hoặc lấy card chưa dùng
    card = get_or_create_unused_card(selected_program)
    
    return card.code, f"Bạn nhận được {selected_program.name}"

def get_or_create_unused_card(program):
    """Lấy card chưa dùng hoặc tạo mới"""
    # Tìm card chưa dùng
    unused_cards = env['loyalty.card'].search([
        ('program_id', '=', program.id),
        ('active', '=', True),
        ('partner_id', '=', False),
    ])
    
    for card in unused_cards:
        if not is_card_used(card.code):
            return card
    
    # Tạo card mới nếu không có
    return env['loyalty.card'].create({
        'program_id': program.id,
        'code': env['loyalty.card']._generate_code(),
        'points': 0,
        'active': True,
    })
```

---

## 🧪 **TESTING SCENARIOS**

### **📝 Test Cases:**

#### **1. Test Coupon Usage:**
```python
# Tạo order
order = env['sale.order'].create({...})

# Apply coupon
result = order._try_apply_code('LUCKY1234')

# Verify
assert result == True
assert order.reward_amount > 0
```

#### **2. Test Card Validation:**
```python
card = env['loyalty.card'].browse(card_id)
valid, message = is_card_valid(card)
assert valid == True
```

#### **3. Test Lucky Wheel:**
```python
code, message = spin_lucky_wheel()
assert code is not None
assert len(code) > 0
```

---

**🎯 Với examples này, chúng ta có thể implement Lucky Wheel chính xác với Odoo 18 Loyalty System! 💎✨**
