# 🚨 ODOO 18 COMMON ERRORS & FIXES

## ❌ **ERROR 1: attrs deprecated**

### **Error Message:**
```
<PERSON><PERSON> từ phiên bản 17.0, cá<PERSON> thuộc t<PERSON>h "attrs" và "states" không còn được sử dụng.
```

### **❌ Old Syntax (Odoo 16 and below):**
```xml
<field name="lucky_wheel_probability" attrs="{'invisible': [('is_lucky_wheel', '=', False)]}"/>
```

### **✅ New Syntax (Odoo 17+):**
```xml
<field name="lucky_wheel_probability" invisible="not is_lucky_wheel"/>
```

### **Common Conversions:**
```xml
<!-- OLD -->
attrs="{'invisible': [('field', '=', False)]}"
<!-- NEW -->
invisible="not field"

<!-- OLD -->
attrs="{'invisible': [('field', '!=', 'value')]}"
<!-- NEW -->
invisible="field != 'value'"

<!-- OLD -->
attrs="{'readonly': [('state', '=', 'done')]}"
<!-- NEW -->
readonly="state == 'done'"

<!-- OLD -->
attrs="{'required': [('type', '=', 'required')]}"
<!-- NEW -->
required="type == 'required'"
```

---

## ❌ **ERROR 2: Field access issues**

### **Error Message:**
```
"field_name" is not accessed
```

### **Fix:**
- Remove unused variables
- Use `_` prefix for intentionally unused variables
- Actually use the variable in code

---

## ❌ **ERROR 3: Model inheritance issues**

### **Error Message:**
```
Model 'model.name' does not exist
```

### **Fix:**
- Check model name spelling
- Ensure dependent modules are installed
- Verify inheritance chain

---

## ❌ **ERROR 4: XML view inheritance - XPath not found**

### **Error Message:**
```
Không thể định vị phần tử '<xpath expr="//field[@name='active']">' trong chế độ xem chính
```

### **Problem:**
- Field 'active' không tồn tại trong tree view
- XPath expression sai
- Parent view structure khác với expected

### **Fix:**
- Check parent view structure trước
- Use correct field names có trong parent view
- Use alternative xpath như `//tree` hoặc `//field[@name='name']`

### **Example Fix:**
```xml
<!-- BAD -->
<xpath expr="//field[@name='active']" position="after">

<!-- GOOD -->
<xpath expr="//field[@name='name']" position="after">
<!-- OR -->
<xpath expr="//tree" position="inside">
```

---

## ❌ **ERROR 5: Menu Parent Not Found**

### **Error Message:**
```
External ID not found in the system: loyalty.loyalty_menu_root
```

### **Problem:**
- Menu parent ID không tồn tại
- Module dependency chưa được install
- Menu structure thay đổi giữa các version Odoo

### **Fix:**
- Check menu parent ID trong Odoo admin
- Use existing menu parents
- Create menu without parent hoặc dưới Settings

### **Common Menu Parents:**
```xml
<!-- Safe options -->
parent="base.menu_administration"  <!-- Settings -->
parent="sale.sale_menu_root"       <!-- Sales -->
parent="website.menu_website_configuration"  <!-- Website -->

<!-- Remove parent completely -->
<menuitem id="menu_name" name="Menu Name" action="action_name"/>
```

---

## ❌ **ERROR 6: Model Field Not Found**

### **Error Message:**
```
'loyalty.program' object has no attribute 'portal_description'
```

### **Problem:**
- Field không tồn tại trong model
- Field có thể đã bị remove trong version mới
- Typo trong field name

### **Fix:**
- Check model definition trước khi dùng field
- Use `hasattr()` để check field exists
- Use alternative fields

### **Example Fix:**
```python
# BAD
description = program.portal_description or program.name

# GOOD
description = getattr(program, 'portal_description', program.name)
# OR
description = program.name  # Use existing field
```

---

## ✅ **BEST PRACTICES FOR ODOO 18:**

### **1. Use modern field attributes:**
```xml
<!-- Modern syntax -->
<field name="field1" invisible="condition"/>
<field name="field2" readonly="state == 'done'"/>
<field name="field3" required="type == 'required'"/>
```

### **2. Proper model inheritance:**
```python
class MyModel(models.Model):
    _inherit = 'existing.model'

    # Add fields and methods
```

### **3. Clean code practices:**
```python
# Use variables or prefix with _
def method(self):
    result = self.some_operation()  # Use result
    _unused = self.other_operation()  # Prefix unused
```

### **4. XML view best practices:**
```xml
<!-- Use specific xpath -->
<xpath expr="//field[@name='specific_field']" position="after">
    <!-- New content -->
</xpath>
```

---

## 🔧 **QUICK FIXES:**

### **attrs → modern attributes:**
```bash
# Find and replace in files
sed -i 's/attrs="{\x27invisible\x27: \[\(\x27[^x27]*\x27, \x27=\x27, False\)\]}"/invisible="not \1"/g' *.xml
```

### **Remove unused variables:**
```python
# Instead of:
card = LoyaltyCard.create({...})

# Use:
LoyaltyCard.create({...})
# OR
_card = LoyaltyCard.create({...})  # If needed for debugging
```

---

## 📋 **CHECKLIST BEFORE DEPLOY:**

- [ ] No `attrs` or `states` in XML views
- [ ] All variables are used or prefixed with `_`
- [ ] Model inheritance is correct
- [ ] XML xpath expressions are valid
- [ ] Dependencies are properly declared
- [ ] Field names match model definitions

---

**🎯 Always test in development before deploying to production!**
