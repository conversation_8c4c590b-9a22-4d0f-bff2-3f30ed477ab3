# 🛒 SHOPPING CART & CHECKOUT INTEGRATION TASKS

## 📋 PHÂN TÍCH HIỆN TRẠNG

### ✅ **ĐÃ CÓ TRONG BINHANJEWELRY:**

#### **🛒 Shopping Cart Features:**
- ✅ Cart Context với localStorage persistence
- ✅ Add/Remove/Update quantity
- ✅ Product attributes handling (size, style)
- ✅ Voucher system với auto-apply
- ✅ Gift options (recipient info, message)
- ✅ Referral code support
- ✅ Mobile responsive design

#### **💳 Checkout Features:**
- ✅ Complete form validation (Zod schema)
- ✅ Address system (Province/District/Ward)
- ✅ Payment methods (COD, Bank Transfer)
- ✅ Different recipient option
- ✅ Order notes
- ✅ Sticky order button
- ✅ Order summary với pricing breakdown
- ✅ Google Sheets integration for orders

### 🎯 **CẦN TÍCH HỢP VỚI ODOO:**

#### **🔄 Current Frontend → Odoo API Integration:**
- ❌ Cart items chưa sync với Odoo product variants
- ❌ Vouchers chưa dùng Odoo loyalty system
- ❌ Orders chưa tạo trong Odoo
- ❌ Customer info chưa lưu vào Odoo
- ❌ Address chưa dùng Odoo location system

---

## 🚀 TASK 1: SHOPPING CART API INTEGRATION

### **📦 1.1 Cart API Endpoints**

#### **🎯 Mục tiêu:** Tạo API để manage cart trong Odoo

```bash
# Cart Management APIs
POST /api/cart/add              # Add product to cart
PUT /api/cart/update            # Update cart item quantity/attributes
DELETE /api/cart/remove/{id}    # Remove item from cart
GET /api/cart                   # Get current cart
DELETE /api/cart/clear          # Clear entire cart
```

#### **📋 Chi tiết implementation:**

**A. Cart Model trong Odoo:**
```python
# Tận dụng existing models:
# - sale.order (draft state) làm cart
# - sale.order.line làm cart items
# - product.product cho variants
# - product.template.attribute.value cho attributes
```

**B. Cart Session Management:**
```python
# Options:
# 1. Session-based (guest users)
# 2. User-based (authenticated users)
# 3. Hybrid (session → user khi login)
```

**C. API Response Structure:**
```json
{
  "success": true,
  "data": {
    "cart_id": "session_abc123",
    "items": [
      {
        "id": 1,
        "product_id": 13,
        "product_name": "Nhẫn cưới Vàng 18K",
        "variant_id": 23,
        "quantity": 2,
        "unit_price": 8950000.0,
        "total_price": 17900000.0,
        "attributes": [
          {
            "attribute_name": "Chọn size",
            "value_name": "Kích thước 14",
            "value_id": 1
          }
        ],
        "images": [...]
      }
    ],
    "summary": {
      "total_items": 2,
      "subtotal": 17900000.0,
      "tax_amount": 0,
      "total_amount": 17900000.0
    }
  }
}
```

### **📝 1.2 Frontend Cart Integration**

#### **🔄 Update CartContext:**
```typescript
// Replace localStorage-only với API calls
interface CartContextType {
  cart: CartItem[];
  loading: boolean;
  addToCart: (productId: number, variantId: number, quantity: number, attributes: any) => Promise<void>;
  updateCart: (itemId: number, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
  syncWithOdoo: () => Promise<void>;
}
```

#### **🎛️ Update Product Detail Integration:**
```typescript
// Khi user chọn attributes → get variant → add to cart
const handleAddToCart = async () => {
  // 1. Get variant from attributes
  const variantResponse = await fetch(`/api/products/${productId}/variant`, {
    method: 'POST',
    body: JSON.stringify({
      attribute_values: selectedAttributes,
      uom_id: selectedUoM
    })
  });

  // 2. Add variant to cart
  const cartResponse = await fetch('/api/cart/add', {
    method: 'POST',
    body: JSON.stringify({
      product_id: productId,
      variant_id: variantResponse.data.variant_id,
      quantity: quantity,
      attributes: selectedAttributes
    })
  });
};
```

---

## 🚀 TASK 2: VOUCHER SYSTEM INTEGRATION

### **🎫 2.1 Voucher API với Odoo Loyalty**

#### **🎯 Mục tiêu:** Tích hợp voucher system với Odoo loyalty

```bash
# Voucher APIs
GET /api/vouchers                    # Get available vouchers
POST /api/vouchers/apply             # Apply voucher to cart
DELETE /api/vouchers/remove          # Remove applied voucher
GET /api/vouchers/validate/{code}    # Validate voucher code
```

#### **📋 Odoo Loyalty Integration:**
```python
# Sử dụng Odoo 18 loyalty system:
# - loyalty.program (voucher programs)
# - loyalty.card (user voucher instances)
# - loyalty.reward (voucher benefits)
# - sale.order.line (discount lines)
```

#### **🎮 Lucky Wheel Integration:**
```python
# API để tạo voucher từ lucky wheel:
POST /api/lucky-wheel/generate-voucher
{
  "prize_type": "discount_percentage",
  "prize_value": 10,
  "user_session": "session_abc123"
}

# Response:
{
  "voucher_code": "LUCKY10_ABC123",
  "discount_type": "percentage",
  "discount_value": 10,
  "expiry_date": "2024-12-31",
  "min_order_value": 0
}
```

### **🔄 2.2 Frontend Voucher Integration**

#### **📱 Update ShoppingCart Component:**
```typescript
// Replace static vouchers với API calls
const applyVoucher = async (code: string) => {
  try {
    const response = await fetch('/api/vouchers/apply', {
      method: 'POST',
      body: JSON.stringify({
        voucher_code: code,
        cart_id: cartId
      })
    });

    if (response.data.success) {
      setAppliedVoucher(response.data.voucher);
      setDiscount(response.data.discount_amount);
      // Refresh cart to show updated totals
      await refreshCart();
    }
  } catch (error) {
    toast.error('Mã giảm giá không hợp lệ');
  }
};
```

---

## 🚀 TASK 3: CHECKOUT PROCESS INTEGRATION

### **💳 3.1 Checkout API Endpoints**

#### **🎯 Mục tiêu:** Tạo complete checkout flow trong Odoo

```bash
# Checkout APIs
POST /api/checkout/validate         # Validate checkout data
POST /api/checkout/create-order     # Create order from cart
GET /api/checkout/payment-methods   # Get available payment methods
POST /api/checkout/confirm-payment  # Confirm payment
GET /api/orders/{id}               # Get order details
```

#### **📋 Order Creation Flow:**
```python
# 1. Validate cart & customer data
# 2. Create sale.order from cart
# 3. Apply vouchers/discounts
# 4. Set delivery address
# 5. Set payment method
# 6. Confirm order
# 7. Clear cart
# 8. Send confirmation email
```

### **📍 3.2 Address System Integration**

#### **🎯 Mục tiêu:** Sử dụng Odoo location system

```bash
# Address APIs
GET /api/locations/provinces        # Get provinces
GET /api/locations/districts/{id}   # Get districts by province
GET /api/locations/wards/{id}       # Get wards by district
POST /api/customers/addresses       # Save customer address
```

#### **🔄 Frontend Address Integration:**
```typescript
// Replace static data với API calls
const [provinces, setProvinces] = useState([]);
const [districts, setDistricts] = useState([]);
const [wards, setWards] = useState([]);

useEffect(() => {
  // Load provinces from API
  fetch('/api/locations/provinces')
    .then(r => r.json())
    .then(data => setProvinces(data.data));
}, []);

const handleProvinceChange = async (provinceId: string) => {
  const response = await fetch(`/api/locations/districts/${provinceId}`);
  const data = await response.json();
  setDistricts(data.data);
  setWards([]); // Clear wards
};
```

### **💰 3.3 Payment Integration**

#### **🎯 Mục tiêu:** Tích hợp payment methods với Odoo

```python
# Payment methods trong Odoo:
# - account.payment.method
# - account.journal (cash, bank)
# - payment.provider (online payments)
```

#### **📱 Frontend Payment Integration:**
```typescript
// Dynamic payment methods từ API
const [paymentMethods, setPaymentMethods] = useState([]);

useEffect(() => {
  fetch('/api/checkout/payment-methods')
    .then(r => r.json())
    .then(data => setPaymentMethods(data.data));
}, []);

// Payment methods response:
{
  "data": [
    {
      "id": "cod",
      "name": "Thanh toán khi nhận hàng",
      "icon": "truck",
      "description": "Thanh toán bằng tiền mặt khi nhận hàng"
    },
    {
      "id": "bank_transfer",
      "name": "Chuyển khoản ngân hàng",
      "icon": "credit-card",
      "bank_info": {
        "account_number": "**********",
        "bank_name": "Sacombank HCM",
        "account_holder": "Công Ty TNHH MTV Toloco Việt Nam"
      }
    }
  ]
}
```

---

## 🚀 TASK 4: ORDER MANAGEMENT

### **📋 4.1 Order APIs**

```bash
# Order Management
POST /api/orders                    # Create order
GET /api/orders/{id}               # Get order details
PUT /api/orders/{id}/status        # Update order status
GET /api/orders/track/{id}         # Track order
POST /api/orders/{id}/cancel       # Cancel order
```

### **📧 4.2 Order Confirmation**

#### **🎯 Mục tiêu:** Replace Google Sheets với Odoo order system

```python
# Order confirmation flow:
# 1. Create sale.order in Odoo
# 2. Generate order number
# 3. Send email confirmation
# 4. Update inventory
# 5. Create delivery order (if needed)
```

#### **📱 Frontend Order Success:**
```typescript
const handlePlaceOrder = async (formData: CheckoutFormValues) => {
  try {
    // Create order in Odoo
    const orderResponse = await fetch('/api/checkout/create-order', {
      method: 'POST',
      body: JSON.stringify({
        customer: formData,
        cart_id: cartId,
        payment_method: formData.paymentMethod,
        notes: formData.notes
      })
    });

    if (orderResponse.data.success) {
      const orderId = orderResponse.data.order_id;

      // Clear cart
      await clearCart();

      // Show success
      toast.success(`Đặt hàng thành công! Mã đơn hàng: ${orderId}`);

      // Navigate to order tracking
      navigate(`/orders/${orderId}`);
    }
  } catch (error) {
    toast.error('Có lỗi xảy ra khi đặt hàng');
  }
};
```

---

## 🚀 TASK 5: CUSTOMER MANAGEMENT (NO AUTH)

### **👤 5.1 Guest Customer Handling**

#### **🎯 Mục tiêu:** Handle guest customers như binhanjewelry

```python
# Guest customer flow:
# 1. Create res.partner with email/phone
# 2. Mark as guest customer
# 3. Link orders to this partner
# 4. Allow order tracking by email/phone
```

### **📱 5.2 Order Tracking cho Guest**

```bash
# Guest order tracking
GET /api/orders/track?email={email}&phone={phone}
GET /api/orders/track/{order_id}?email={email}
```

---

## 📅 IMPLEMENTATION TIMELINE

### **🗓️ Phase 1 (Week 1): Cart API**
- [ ] Cart model setup trong Odoo
- [ ] Cart CRUD APIs
- [ ] Frontend cart integration
- [ ] Product variant integration

### **🗓️ Phase 2 (Week 2): Voucher System**
- [ ] Odoo loyalty integration
- [ ] Voucher APIs
- [ ] Lucky wheel voucher generation
- [ ] Frontend voucher integration

### **🗓️ Phase 3 (Week 3): Checkout Process**
- [ ] Address system APIs
- [ ] Payment method APIs
- [ ] Order creation APIs
- [ ] Frontend checkout integration

### **🗓️ Phase 4 (Week 4): Order Management**
- [ ] Order tracking APIs
- [ ] Email notifications
- [ ] Order status management
- [ ] Guest customer system

---

## 🎯 SUCCESS CRITERIA

### **✅ Shopping Cart:**
- [ ] Add/remove products với Odoo variants
- [ ] Real-time price updates
- [ ] Persistent cart across sessions
- [ ] Mobile responsive

### **✅ Voucher System:**
- [ ] Apply/remove vouchers
- [ ] Lucky wheel integration
- [ ] Automatic discount calculation
- [ ] Voucher validation

### **✅ Checkout Process:**
- [ ] Complete form validation
- [ ] Dynamic address loading
- [ ] Payment method selection
- [ ] Order confirmation

### **✅ Order Management:**
- [ ] Order creation trong Odoo
- [ ] Order tracking cho guests
- [ ] Email confirmations
- [ ] Status updates

**🎯 GOAL: Complete ecommerce flow từ product selection → checkout → order tracking, tận dụng Odoo backend với binhanjewelry UX! 💎✨**

---

## 🚀 TASK 6: USER AUTHENTICATION SYSTEM

### **👤 6.1 Authentication Strategy**

#### **🎯 Mục tiêu:** Implement progressive authentication như binhanjewelry

```bash
# Authentication Flow:
# 1. Guest checkout (no auth required)
# 2. Optional account creation after order
# 3. Login for order history
# 4. Social login options
```

#### **📋 Authentication APIs:**
```bash
# Auth endpoints
POST /api/auth/register             # Register new user
POST /api/auth/login               # Login user
POST /api/auth/logout              # Logout user
POST /api/auth/refresh             # Refresh token
POST /api/auth/forgot-password     # Password reset
POST /api/auth/verify-email        # Email verification
GET /api/auth/profile              # Get user profile
PUT /api/auth/profile              # Update profile
```

### **🔐 6.2 Odoo User Integration**

#### **🎯 Mục tiêu:** Sử dụng Odoo res.users và res.partner

```python
# Odoo models:
# - res.users (login credentials)
# - res.partner (customer info)
# - website.visitor (session tracking)
```

#### **📱 JWT Token Strategy:**
```python
# JWT implementation:
# 1. Generate JWT token on login
# 2. Store user session in Odoo
# 3. Validate token on protected endpoints
# 4. Auto-refresh tokens
```

### **👤 6.3 User Profile Management**

#### **📋 Profile Features:**
```bash
# User profile endpoints
GET /api/users/profile              # Get profile
PUT /api/users/profile              # Update profile
GET /api/users/addresses            # Get saved addresses
POST /api/users/addresses           # Add new address
PUT /api/users/addresses/{id}       # Update address
DELETE /api/users/addresses/{id}    # Delete address
GET /api/users/orders               # Get order history
GET /api/users/wishlist             # Get wishlist
POST /api/users/wishlist            # Add to wishlist
```

### **📱 6.4 Frontend Auth Integration**

#### **🔄 Auth Context:**
```typescript
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: ProfileData) => Promise<void>;
}

// Auth flow:
// 1. Check token on app load
// 2. Auto-login if valid token
// 3. Redirect to login if needed
// 4. Merge guest cart with user cart
```

### **🛒 6.5 Cart Migration**

#### **🎯 Mục tiêu:** Merge guest cart với user cart khi login

```typescript
const handleLogin = async (email: string, password: string) => {
  // 1. Get current guest cart
  const guestCart = await getGuestCart();

  // 2. Login user
  const authResponse = await login(email, password);

  // 3. Merge carts
  if (guestCart.items.length > 0) {
    await mergeGuestCartWithUserCart(guestCart, authResponse.user.id);
  }

  // 4. Clear guest session
  clearGuestSession();
};
```

---

## 🚀 TASK 7: PROGRESSIVE ENHANCEMENT

### **📱 7.1 Guest → User Journey**

#### **🎯 Mục tiêu:** Smooth transition từ guest sang registered user

```typescript
// After successful order as guest:
const showAccountCreationPrompt = () => {
  return (
    <Dialog>
      <DialogContent>
        <h3>Tạo tài khoản để theo dõi đơn hàng?</h3>
        <p>Bạn có muốn tạo tài khoản để dễ dàng theo dõi đơn hàng và mua sắm lần sau?</p>
        <div className="flex gap-2">
          <Button onClick={createAccountFromOrder}>
            Tạo tài khoản
          </Button>
          <Button variant="outline" onClick={continueAsGuest}>
            Tiếp tục không tài khoản
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
```

### **📧 7.2 Email-based Order Tracking**

#### **🎯 Mục tiêu:** Allow order tracking bằng email cho guests

```bash
# Order tracking for guests
GET /api/orders/track-by-email?email={email}
POST /api/orders/send-tracking-link
{
  "email": "<EMAIL>",
  "order_id": "FL123456789"
}
```

### **🔗 7.3 Social Login Integration**

#### **📱 Social Auth Options:**
```typescript
// Social login providers
const socialProviders = [
  {
    id: 'google',
    name: 'Google',
    icon: GoogleIcon,
    endpoint: '/api/auth/google'
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: FacebookIcon,
    endpoint: '/api/auth/facebook'
  }
];
```

---

## 📊 TECHNICAL SPECIFICATIONS

### **🔐 Security Requirements:**
- [ ] JWT token với expiry
- [ ] Password hashing (bcrypt)
- [ ] Rate limiting cho auth endpoints
- [ ] CSRF protection
- [ ] Input validation và sanitization

### **📱 UX Requirements:**
- [ ] Guest checkout không require login
- [ ] Optional account creation
- [ ] Remember me functionality
- [ ] Auto-save form data
- [ ] Seamless cart migration

### **🎯 Performance Requirements:**
- [ ] Fast authentication checks
- [ ] Efficient session management
- [ ] Minimal API calls
- [ ] Offline capability cho cart

---

## 🗓️ EXTENDED TIMELINE

### **🗓️ Phase 5 (Week 5): Basic Auth**
- [ ] JWT authentication setup
- [ ] Login/register APIs
- [ ] Frontend auth context
- [ ] Protected routes

### **🗓️ Phase 6 (Week 6): User Profile**
- [ ] Profile management APIs
- [ ] Address book functionality
- [ ] Order history
- [ ] Wishlist features

### **🗓️ Phase 7 (Week 7): Progressive Enhancement**
- [ ] Guest to user migration
- [ ] Social login integration
- [ ] Email-based tracking
- [ ] Account creation prompts

### **🗓️ Phase 8 (Week 8): Polish & Testing**
- [ ] Security testing
- [ ] Performance optimization
- [ ] UX improvements
- [ ] Bug fixes

**🎯 COMPLETE ECOMMERCE SYSTEM: Guest checkout → Optional auth → Full user experience! 🚀💎**
