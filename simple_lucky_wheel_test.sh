#!/bin/bash

# 🎰 SIMPLE LUCKY WHEEL TEST
# Quick test for Lucky Wheel functionality

echo "🎰 Simple Lucky Wheel Test"
echo "========================="

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="simple_test_$(date +%s)"

echo "🆔 Session: $SESSION_ID"
echo ""

# Test 1: Check if API is responsive
echo "🔍 Testing API responsiveness..."
if curl -s -m 5 "$BASE_URL/products" >/dev/null 2>&1; then
    echo "✅ API is responsive"
else
    echo "❌ API is slow/unresponsive"
    echo "⏳ Try again when server is stable"
    exit 1
fi

echo ""

# Test 2: Get Lucky Wheel promotions
echo "🎯 Getting Lucky Wheel promotions..."
PROMOTIONS=$(curl -s -m 10 "$BASE_URL/lucky-wheel/promotions" 2>/dev/null)

if echo "$PROMOTIONS" | jq -e '.success' >/dev/null 2>&1; then
    COUNT=$(echo "$PROMOTIONS" | jq '.data.total_count')
    echo "✅ Found $COUNT promotions"
    
    # Show first few promotions
    echo "🏆 Sample promotions:"
    echo "$PROMOTIONS" | jq -r '.data.promotions[0:3][] | "   - \(.name) (\(.program_type)) - \(.lucky_wheel_probability)%"'
else
    echo "❌ Failed to get promotions"
    echo "📄 Response: $PROMOTIONS"
fi

echo ""

# Test 3: Test sample voucher
echo "🎟️ Testing sample voucher..."
VOUCHER_TEST=$(curl -s -m 10 "$BASE_URL/vouchers/validate/SAVE15-001" 2>/dev/null)

if echo "$VOUCHER_TEST" | jq -e '.success' >/dev/null 2>&1; then
    TYPE=$(echo "$VOUCHER_TEST" | jq -r '.data.program_type')
    DISCOUNT=$(echo "$VOUCHER_TEST" | jq -r '.data.discount_info.discount_percent')
    echo "✅ Sample voucher SAVE15-001 is valid"
    echo "   Type: $TYPE, Discount: ${DISCOUNT}%"
else
    echo "❌ Sample voucher test failed"
fi

echo ""

# Test 4: Try Lucky Wheel spin
echo "🎲 Attempting Lucky Wheel spin..."
SPIN_RESULT=$(curl -s -m 15 -X POST "$BASE_URL/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{"email": "<EMAIL>"}' 2>/dev/null)

if echo "$SPIN_RESULT" | jq -e '.success' >/dev/null 2>&1; then
    PRIZE=$(echo "$SPIN_RESULT" | jq -r '.data.prize.name')
    VOUCHER=$(echo "$SPIN_RESULT" | jq -r '.data.voucher_code // "None"')
    echo "🎉 Lucky Wheel spin successful!"
    echo "   Prize: $PRIZE"
    echo "   Voucher: $VOUCHER"
    
    # Test the voucher if received
    if [ "$VOUCHER" != "None" ] && [ "$VOUCHER" != "null" ]; then
        echo ""
        echo "🔍 Testing received voucher..."
        VOUCHER_CHECK=$(curl -s -m 10 "$BASE_URL/vouchers/validate/$VOUCHER" 2>/dev/null)
        
        if echo "$VOUCHER_CHECK" | jq -e '.success' >/dev/null 2>&1; then
            echo "✅ Received voucher is valid!"
        else
            echo "❌ Received voucher validation failed"
        fi
    fi
else
    echo "❌ Lucky Wheel spin failed"
    echo "📄 Response: $SPIN_RESULT"
fi

echo ""
echo "🎯 Simple test completed!"
echo ""
echo "📋 Next steps:"
echo "   1. If all tests pass → Run ./test_lucky_wheel_order.sh"
echo "   2. If tests fail → Check server status and try again"
echo "   3. For manual testing → See LUCKY_WHEEL_ORDER_DEMO.md"
