#!/bin/bash

# 🧪 JEWELRY ECOMMERCE - CART & CHECKOUT API TESTS
# Run this script to test all Shopping Cart and Checkout APIs

BASE_URL="https://noithat.erpcloud.vn"
SESSION_ID="test_$(date +%s)"

echo "🚀 Starting Cart & Checkout API Tests..."
echo "📋 Base URL: $BASE_URL"
echo "🔑 Session ID: $SESSION_ID"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_test() {
    echo -e "${BLUE}🧪 TEST: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

print_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  INFO: $1${NC}"
}

# Test 1: Get Empty Cart
print_test "Get Empty Cart"
curl -s -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart" | python3 -m json.tool
echo ""

# Test 2: Add Product to Cart
print_test "Add Product to Cart (Product 13, Variant 23)"
curl -s -X POST "$BASE_URL/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "product_id": 13,
    "variant_id": 23,
    "quantity": 2,
    "attributes": {"size": "14"}
  }' | python3 -m json.tool
echo ""

# Test 3: Get Cart with Items
print_test "Get Cart with Items"
CART_RESPONSE=$(curl -s -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart")
echo "$CART_RESPONSE" | python3 -m json.tool

# Extract item ID for next tests
ITEM_ID=$(echo "$CART_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data', {}).get('items'):
        print(data['data']['items'][0]['id'])
    else:
        print('0')
except:
    print('0')
")
echo ""

# Test 4: Update Cart Item Quantity
if [ "$ITEM_ID" != "0" ]; then
    print_test "Update Cart Item Quantity (Item ID: $ITEM_ID)"
    curl -s -X PUT "$BASE_URL/api/cart/update" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: $SESSION_ID" \
      -d "{
        \"item_id\": $ITEM_ID,
        \"quantity\": 3
      }" | python3 -m json.tool
    echo ""
else
    print_error "No item ID found, skipping update test"
fi

# Test 5: Get Payment Methods
print_test "Get Payment Methods"
curl -s "$BASE_URL/api/checkout/payment-methods" | python3 -m json.tool
echo ""

# Test 6: Validate Checkout
print_test "Validate Checkout"
curl -s -X POST "$BASE_URL/api/checkout/validate" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer": {
      "full_name": "Nguyen Van Test",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Test Street",
      "province": "Ho Chi Minh",
      "district": "District 1",
      "ward": "Ward 1"
    },
    "voucher_code": "WELCOME10"
  }' | python3 -m json.tool
echo ""

# Test 7: Create Order
print_test "Create Order from Cart"
ORDER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer": {
      "full_name": "Nguyen Van Test",
      "email": "<EMAIL>", 
      "phone": "0123456789",
      "address": "123 Test Street",
      "province": "Ho Chi Minh",
      "district": "District 1",
      "ward": "Ward 1"
    },
    "payment_method": "cod",
    "voucher_code": "WELCOME10"
  }')

echo "$ORDER_RESPONSE" | python3 -m json.tool

# Extract order ID for tracking tests
ORDER_ID=$(echo "$ORDER_RESPONSE" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    if data.get('success') and data.get('data', {}).get('order_id'):
        print(data['data']['order_id'])
    else:
        print('0')
except:
    print('0')
")
echo ""

# Test 8: Track Orders by Email
print_test "Track Orders by Email"
curl -s "$BASE_URL/api/orders/track?email=<EMAIL>" | python3 -m json.tool
echo ""

# Test 9: Get Specific Order
if [ "$ORDER_ID" != "0" ]; then
    print_test "Get Order Details (Order ID: $ORDER_ID)"
    curl -s "$BASE_URL/api/orders/$ORDER_ID?email=<EMAIL>" | python3 -m json.tool
    echo ""
else
    print_error "No order ID found, skipping order details test"
fi

# Test 10: Error Cases
print_test "Error Test - Invalid Product"
curl -s -X POST "$BASE_URL/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: error_test" \
  -d '{
    "product_id": 999,
    "variant_id": 999,
    "quantity": 1
  }' | python3 -m json.tool
echo ""

print_test "Error Test - Invalid Customer Data"
curl -s -X POST "$BASE_URL/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer": {
      "full_name": "",
      "email": "invalid-email",
      "phone": "123"
    }
  }' | python3 -m json.tool
echo ""

# Test 11: Clear Cart
print_test "Clear Cart"
curl -s -X DELETE "$BASE_URL/api/cart/clear" \
  -H "X-Session-ID: $SESSION_ID" | python3 -m json.tool
echo ""

# Test 12: Verify Empty Cart
print_test "Verify Cart is Empty"
curl -s -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart" | python3 -m json.tool
echo ""

echo "=================================="
print_success "All tests completed!"
echo "📊 Summary:"
echo "   - Session ID used: $SESSION_ID"
echo "   - Order ID created: $ORDER_ID"
echo "   - Test email: <EMAIL>"
echo ""
echo "🔍 Check the responses above for:"
echo "   ✅ Successful API calls (success: true)"
echo "   ❌ Error handling (success: false)"
echo "   📋 Data structure and content"
echo ""
echo "🎯 Next steps:"
echo "   1. Verify order was created in Odoo backend"
echo "   2. Test frontend integration"
echo "   3. Test with real product variants"
echo "=================================="
