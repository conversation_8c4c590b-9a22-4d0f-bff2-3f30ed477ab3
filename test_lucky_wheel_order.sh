#!/bin/bash

# 🎰 LUCKY WHEEL TO ORDER TEST
# Test complete flow: Lucky Wheel → Get Voucher → Create Order

echo "🎰 LUCKY WHEEL TO ORDER COMPLETE TEST"
echo "===================================="

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="lucky_wheel_test_$(date +%s)"
EMAIL="<EMAIL>"

echo "📧 Email: $EMAIL"
echo "🆔 Session: $SESSION_ID"
echo ""

# ========================================
# STEP 1: CHECK LUCKY WHEEL PROMOTIONS
# ========================================
echo "🎯 STEP 1: Checking Lucky Wheel Promotions"
echo "=========================================="

echo "   📋 Getting available promotions..."
PROMOTIONS_RESPONSE=$(curl -s -m 15 "$BASE_URL/lucky-wheel/promotions" 2>/dev/null)

if echo "$PROMOTIONS_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    PROMOTION_COUNT=$(echo "$PROMOTIONS_RESPONSE" | jq '.data.total_count')
    echo "   ✅ Found $PROMOTION_COUNT Lucky Wheel promotions"
    
    echo "   🏆 Available promotions:"
    echo "$PROMOTIONS_RESPONSE" | jq -r '.data.promotions[] | "      - \(.name) (\(.program_type)) - \(.lucky_wheel_probability)% - \(.lucky_wheel_icon)"' | head -8
    
    # Save promotions for reference
    echo "$PROMOTIONS_RESPONSE" > /tmp/lucky_wheel_promotions.json
    echo "   💾 Promotions saved to /tmp/lucky_wheel_promotions.json"
else
    echo "   ❌ Failed to get promotions"
    echo "   📄 Response: $PROMOTIONS_RESPONSE"
    exit 1
fi

echo ""

# ========================================
# STEP 2: SPIN LUCKY WHEEL
# ========================================
echo "🎲 STEP 2: Spinning Lucky Wheel"
echo "==============================="

echo "   🎰 Spinning the wheel..."
SPIN_RESPONSE=$(curl -s -m 15 -X POST "$BASE_URL/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "{\"email\": \"$EMAIL\"}" 2>/dev/null)

if echo "$SPIN_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "   🎉 Lucky Wheel spin successful!"
    
    # Extract prize information
    PRIZE_NAME=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.name // "Unknown"')
    PRIZE_TYPE=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.program_type // "Unknown"')
    VOUCHER_CODE=$(echo "$SPIN_RESPONSE" | jq -r '.data.voucher_code // "None"')
    PRIZE_ICON=$(echo "$SPIN_RESPONSE" | jq -r '.data.prize.lucky_wheel_icon // "🎁"')
    
    echo "   🏆 Prize: $PRIZE_NAME"
    echo "   🎯 Type: $PRIZE_TYPE"
    echo "   🎟️ Voucher Code: $VOUCHER_CODE"
    echo "   $PRIZE_ICON Icon: $PRIZE_ICON"
    
    # Save spin result
    echo "$SPIN_RESPONSE" > /tmp/lucky_wheel_spin.json
    echo "   💾 Spin result saved to /tmp/lucky_wheel_spin.json"
    
    if [ "$VOUCHER_CODE" = "None" ] || [ "$VOUCHER_CODE" = "null" ]; then
        echo "   ⚠️ No voucher code received - may be auto-apply promotion"
        VOUCHER_CODE=""
    fi
else
    echo "   ❌ Lucky Wheel spin failed"
    echo "   📄 Response: $SPIN_RESPONSE"
    
    # Use sample voucher for testing
    echo "   🔄 Using sample voucher for testing: SAVE15-001"
    VOUCHER_CODE="SAVE15-001"
    PRIZE_NAME="Sample 15% Discount"
    PRIZE_TYPE="coupons"
fi

echo ""

# ========================================
# STEP 3: VALIDATE VOUCHER (if received)
# ========================================
if [ ! -z "$VOUCHER_CODE" ]; then
    echo "🎟️ STEP 3: Validating Voucher"
    echo "============================="
    
    echo "   🔍 Validating voucher: $VOUCHER_CODE"
    VOUCHER_RESPONSE=$(curl -s -m 10 "$BASE_URL/vouchers/validate/$VOUCHER_CODE" 2>/dev/null)
    
    if echo "$VOUCHER_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
        echo "   ✅ Voucher is valid!"
        
        VOUCHER_TYPE=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.program_type // "Unknown"')
        DISCOUNT_PERCENT=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_percent // 0')
        DISCOUNT_FIXED=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.discount_info.discount_fixed // 0')
        MIN_AMOUNT=$(echo "$VOUCHER_RESPONSE" | jq -r '.data.rule_info.minimum_amount // 0')
        
        echo "   📋 Voucher Details:"
        echo "      - Type: $VOUCHER_TYPE"
        echo "      - Discount: ${DISCOUNT_PERCENT}% / ${DISCOUNT_FIXED} VND"
        echo "      - Minimum Amount: $MIN_AMOUNT VND"
        
        # Save voucher validation
        echo "$VOUCHER_RESPONSE" > /tmp/voucher_validation.json
    else
        echo "   ❌ Voucher validation failed"
        echo "   📄 Response: $VOUCHER_RESPONSE"
    fi
    
    echo ""
fi

# ========================================
# STEP 4: ADD PRODUCTS TO CART
# ========================================
echo "🛒 STEP 4: Adding Products to Cart"
echo "=================================="

echo "   📦 Getting available products..."
PRODUCTS_RESPONSE=$(curl -s -m 10 "$BASE_URL/products" 2>/dev/null)

if echo "$PRODUCTS_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    # Get first available product
    FIRST_PRODUCT_ID=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].id')
    FIRST_PRODUCT_NAME=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].name')
    FIRST_PRODUCT_PRICE=$(echo "$PRODUCTS_RESPONSE" | jq -r '.data.products[0].price')
    
    echo "   📋 Selected product: $FIRST_PRODUCT_NAME (ID: $FIRST_PRODUCT_ID)"
    echo "   💰 Price: $FIRST_PRODUCT_PRICE VND"
    
    echo "   ➕ Adding product to cart..."
    ADD_CART_RESPONSE=$(curl -s -m 10 -X POST "$BASE_URL/cart/add" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: $SESSION_ID" \
      -d "{
        \"product_id\": $FIRST_PRODUCT_ID,
        \"quantity\": 2
      }" 2>/dev/null)
    
    if echo "$ADD_CART_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
        echo "   ✅ Product added to cart successfully"
        
        CART_TOTAL=$(echo "$ADD_CART_RESPONSE" | jq -r '.data.cart.total_amount // 0')
        CART_ITEMS=$(echo "$ADD_CART_RESPONSE" | jq '.data.cart.items | length // 0')
        
        echo "   📊 Cart Summary:"
        echo "      - Items: $CART_ITEMS"
        echo "      - Total: $CART_TOTAL VND"
        
        # Save cart state
        echo "$ADD_CART_RESPONSE" > /tmp/cart_with_products.json
    else
        echo "   ❌ Failed to add product to cart"
        echo "   📄 Response: $ADD_CART_RESPONSE"
        exit 1
    fi
else
    echo "   ❌ Failed to get products"
    exit 1
fi

echo ""

# ========================================
# STEP 5: CREATE ORDER WITH VOUCHER
# ========================================
echo "💳 STEP 5: Creating Order with Lucky Wheel Voucher"
echo "=================================================="

echo "   📋 Creating order..."

# Prepare order data
ORDER_DATA="{
  \"customer_info\": {
    \"full_name\": \"Lucky Winner\",
    \"email\": \"$EMAIL\",
    \"phone\": \"0123456789\",
    \"address\": \"123 Lucky Street, Winner City\"
  },
  \"payment_method\": \"cod\",
  \"notes\": \"Order created with Lucky Wheel voucher: $VOUCHER_CODE\"
}"

# Add voucher if available
if [ ! -z "$VOUCHER_CODE" ]; then
    ORDER_DATA=$(echo "$ORDER_DATA" | jq --arg voucher "$VOUCHER_CODE" '. + {"voucher_code": $voucher}')
    echo "   🎟️ Using voucher: $VOUCHER_CODE"
fi

echo "   💳 Processing checkout..."
CHECKOUT_RESPONSE=$(curl -s -m 20 -X POST "$BASE_URL/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d "$ORDER_DATA" 2>/dev/null)

if echo "$CHECKOUT_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "   🎉 ORDER CREATED SUCCESSFULLY!"
    
    # Extract order information
    ORDER_ID=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.id // "Unknown"')
    ORDER_TOTAL=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.total_amount // 0')
    DISCOUNT_AMOUNT=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.discount_amount // 0')
    ORIGINAL_AMOUNT=$(echo "$CHECKOUT_RESPONSE" | jq -r '.data.order.amount_untaxed // 0')
    
    echo ""
    echo "   🎯 ORDER DETAILS:"
    echo "   ================"
    echo "   🆔 Order ID: $ORDER_ID"
    echo "   💰 Original Amount: $ORIGINAL_AMOUNT VND"
    echo "   🎁 Discount Applied: $DISCOUNT_AMOUNT VND"
    echo "   💳 Final Total: $ORDER_TOTAL VND"
    echo "   🎟️ Voucher Used: $VOUCHER_CODE"
    echo "   🏆 Prize Won: $PRIZE_NAME"
    
    # Calculate savings
    if [ "$DISCOUNT_AMOUNT" != "0" ] && [ "$DISCOUNT_AMOUNT" != "null" ]; then
        echo "   💸 Money Saved: $DISCOUNT_AMOUNT VND"
        
        # Calculate percentage saved
        if [ "$ORIGINAL_AMOUNT" != "0" ]; then
            SAVINGS_PERCENT=$(echo "scale=1; $DISCOUNT_AMOUNT * 100 / $ORIGINAL_AMOUNT" | bc 2>/dev/null || echo "N/A")
            echo "   📊 Savings Percentage: ${SAVINGS_PERCENT}%"
        fi
    fi
    
    # Save order details
    echo "$CHECKOUT_RESPONSE" > /tmp/lucky_wheel_order.json
    echo "   💾 Order details saved to /tmp/lucky_wheel_order.json"
    
else
    echo "   ❌ ORDER CREATION FAILED"
    echo "   📄 Response: $CHECKOUT_RESPONSE"
    
    # Try to identify the issue
    if echo "$CHECKOUT_RESPONSE" | grep -q "voucher"; then
        echo "   🔍 Issue may be related to voucher validation"
    elif echo "$CHECKOUT_RESPONSE" | grep -q "cart"; then
        echo "   🔍 Issue may be related to cart contents"
    fi
fi

echo ""

# ========================================
# STEP 6: SUMMARY REPORT
# ========================================
echo "📊 LUCKY WHEEL TO ORDER TEST SUMMARY"
echo "===================================="

echo "🎰 Lucky Wheel Results:"
echo "   🏆 Prize: $PRIZE_NAME"
echo "   🎯 Type: $PRIZE_TYPE"
echo "   🎟️ Voucher: $VOUCHER_CODE"

if [ ! -z "$ORDER_ID" ] && [ "$ORDER_ID" != "Unknown" ]; then
    echo ""
    echo "💳 Order Results:"
    echo "   ✅ Order Created: $ORDER_ID"
    echo "   💰 Total Amount: $ORDER_TOTAL VND"
    echo "   🎁 Discount: $DISCOUNT_AMOUNT VND"
    echo ""
    echo "🎉 SUCCESS: Complete flow from Lucky Wheel to Order!"
else
    echo ""
    echo "❌ Order creation failed - check logs above"
fi

echo ""
echo "📁 Generated Files:"
echo "   - /tmp/lucky_wheel_promotions.json"
echo "   - /tmp/lucky_wheel_spin.json"
echo "   - /tmp/voucher_validation.json"
echo "   - /tmp/cart_with_products.json"
echo "   - /tmp/lucky_wheel_order.json"

echo ""
echo "🎯 LUCKY WHEEL TO ORDER TEST COMPLETED!"
