#!/usr/bin/env python3
import requests
import json

# Test variant selection API
url = "https://noithat.erpcloud.vn/api/products/13/variant"

# Test data
test_cases = [
    {
        "name": "<PERSON><PERSON><PERSON> thước 14",
        "data": {
            "attribute_values": [1],
            "uom_id": 1
        }
    },
    {
        "name": "Kích thước 16", 
        "data": {
            "attribute_values": [2],
            "uom_id": 1
        }
    },
    {
        "name": "Kích thước 17",
        "data": {
            "attribute_values": [3],
            "uom_id": 1
        }
    },
    {
        "name": "K<PERSON>ch thước 16 với Uo<PERSON>",
        "data": {
            "attribute_values": [2],
            "uom_id": 2
        }
    }
]

headers = {
    'Content-Type': 'application/json'
}

for test_case in test_cases:
    print(f"\n=== Testing: {test_case['name']} ===")
    
    try:
        response = requests.post(url, 
                               headers=headers, 
                               data=json.dumps(test_case['data']),
                               timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                variant_data = data['data']
                print(f"✅ Success!")
                print(f"   Variant ID: {variant_data['variant_id']}")
                print(f"   Variant Name: {variant_data['variant_name']}")
                print(f"   Base Price: {variant_data['base_price']:,.0f}₫")
                print(f"   Extra Price: {variant_data['extra_price']:,.0f}₫")
                print(f"   Final Price: {variant_data['final_price']:,.0f}₫")
                print(f"   UoM Price: {variant_data['uom_price']:,.0f}₫")
                
                if variant_data['selected_attributes']:
                    print("   Selected Attributes:")
                    for attr in variant_data['selected_attributes']:
                        print(f"     - {attr['attribute_name']}: {attr['value_name']} (+{attr['extra_price']:,.0f}₫)")
                        
                if variant_data['uom_info']:
                    print(f"   UoM: {variant_data['uom_info']['name']}")
            else:
                print(f"❌ API Error: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

print("\n=== Test Complete ===")
