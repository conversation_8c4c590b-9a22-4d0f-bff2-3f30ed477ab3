# 🧪 INDIVIDUAL API TESTS

## 🛒 CART APIs

### 1. Get Empty Cart
```bash
curl -H "X-Session-ID: test123" "https://noithat.erpcloud.vn/api/cart"
```

### 2. Add Product to Cart
```bash
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "product_id": 13,
    "variant_id": 23,
    "quantity": 2
  }'
```

### 3. Update Cart Item
```bash
curl -X PUT "https://noithat.erpcloud.vn/api/cart/update" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "item_id": 1,
    "quantity": 3
  }'
```

### 4. Remove Cart Item
```bash
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/remove/1" \
  -H "X-Session-ID: test123"
```

### 5. Clear Cart
```bash
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/clear" \
  -H "X-Session-ID: test123"
```

## 💳 CHECKOUT APIs

### 1. Get Payment Methods
```bash
curl "https://noithat.erpcloud.vn/api/checkout/payment-methods"
```

### 2. Validate Checkout
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/validate" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "customer": {
      "full_name": "Test User",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Test Street"
    }
  }'
```

### 3. Create Order
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "customer": {
      "full_name": "Test User",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Test Street"
    },
    "payment_method": "cod"
  }'
```

## 📦 ORDER APIs

### 1. Track Orders by Email
```bash
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"
```

### 2. Get Order Details
```bash
curl "https://noithat.erpcloud.vn/api/orders/1?email=<EMAIL>"
```

### 3. Cancel Order
```bash
curl -X POST "https://noithat.erpcloud.vn/api/orders/1/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "reason": "Test cancellation"
  }'
```

## 🔄 COMPLETE FLOW TEST

### Step 1: Add Product
```bash
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: flow123" \
  -d '{"product_id": 13, "variant_id": 23, "quantity": 1}'
```

### Step 2: Check Cart
```bash
curl -H "X-Session-ID: flow123" "https://noithat.erpcloud.vn/api/cart"
```

### Step 3: Create Order
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: flow123" \
  -d '{
    "customer": {
      "full_name": "Flow Test",
      "email": "<EMAIL>",
      "phone": "0987654321",
      "address": "Flow Test Address"
    },
    "payment_method": "cod"
  }'
```

### Step 4: Track Order
```bash
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"
```

## ❌ ERROR TESTS

### Invalid Product
```bash
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: error123" \
  -d '{"product_id": 999, "variant_id": 999, "quantity": 1}'
```

### Invalid Customer Data
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test123" \
  -d '{
    "customer": {
      "full_name": "",
      "email": "invalid-email",
      "phone": "123"
    }
  }'
```

### Empty Cart Checkout
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: empty123" \
  -d '{
    "customer": {
      "full_name": "Test",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Test"
    }
  }'
```

## 🎯 EXPECTED RESPONSES

### ✅ Success Response Format:
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed"
}
```

### ❌ Error Response Format:
```json
{
  "success": false,
  "error": "Error description"
}
```

## 📋 TESTING CHECKLIST

- [ ] Cart APIs work correctly
- [ ] Checkout validation works
- [ ] Order creation successful
- [ ] Order tracking functional
- [ ] Error handling proper
- [ ] Session management works
- [ ] Product variants supported
- [ ] Voucher codes apply
- [ ] Payment methods available
- [ ] Guest checkout works
