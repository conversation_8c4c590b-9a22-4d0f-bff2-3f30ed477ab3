import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCart } from '@/contexts/CartContext';
import API from '@/services/api';

// Prize interface matching real API response
interface Prize {
  id: number;
  name: string;
  program_type: string;
  description: string;
  active: boolean;
  date_from: string | null;
  date_to: string | null;
  is_lucky_wheel: boolean;
  lucky_wheel_probability: number;
  lucky_wheel_icon: string;
  reward_info: {
    type: string;
    discount_mode: string;
    discount_percent: number;
    discount_fixed: number;
    currency: string;
  };
  rule_info: {
    minimum_amount: number;
    minimum_qty: number;
    mode: string;
  };
  trigger: string;
  applies_on: string;
}

interface SpinResult {
  prize: Prize;
  voucher_code?: string;
  message: string;
}

interface LuckyWheelAPIProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail?: string;
}

const LuckyWheelAPI: React.FC<LuckyWheelAPIProps> = ({ isOpen, onClose, userEmail }) => {
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [isSpinning, setIsSpinning] = useState(false);
  const [spinResult, setSpinResult] = useState<SpinResult | null>(null);
  const [spinsRemaining, setSpinsRemaining] = useState(10);
  const [rotation, setRotation] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { validateVoucher, setAppliedVoucher } = useCart();

  // Load available prizes on component mount
  useEffect(() => {
    if (isOpen) {
      loadPrizes();
      checkSpinsRemaining();
    }
  }, [isOpen]);

  const loadPrizes = async () => {
    try {
      const response = await API.Loyalty.getAvailablePrizes();
      if (response.success && response.data) {
        // Real API returns promotions array
        const promotions = response.data.promotions || [];
        setPrizes(promotions);
        console.log('🎰 Loaded prizes:', promotions.length);
      }
    } catch (error) {
      console.error('Failed to load prizes:', error);
      setError('Failed to load prizes');
    }
  };

  const checkSpinsRemaining = async () => {
    try {
      const response = await API.Loyalty.getSpinHistory(userEmail);
      if (response.success && response.data) {
        setSpinsRemaining(10 - response.data.spins_today);
      }
    } catch (error) {
      console.error('Failed to check spins:', error);
    }
  };

  const spinWheel = async () => {
    if (isSpinning || spinsRemaining <= 0) return;

    setIsSpinning(true);
    setError(null);
    setShowResult(false);

    try {
      // Call API to spin wheel
      const response = await API.Loyalty.spinLuckyWheel(userEmail);

      if (response.success && response.data) {
        const result = response.data as SpinResult;
        setSpinResult(result);

        // Calculate rotation for visual effect
        const prizeIndex = prizes.findIndex(p => p.id === result.prize.id);
        const segmentAngle = 360 / prizes.length;
        const targetRotation = rotation + 1440 + (segmentAngle * prizeIndex); // 4 full rotations + target

        setRotation(targetRotation);

        // Show result after animation
        setTimeout(() => {
          setShowResult(true);
          setIsSpinning(false);

          // Store voucher code and prize info
          if (result.voucher_code) {
            localStorage.setItem('lucky_wheel_voucher', result.voucher_code);
            localStorage.setItem('lucky_wheel_prize', JSON.stringify(result.prize));

            // Auto-apply voucher to cart
            applyVoucherToCart(result.voucher_code);

            console.log('🎉 Lucky Wheel Prize:', result.prize.name);
            console.log('🎟️ Voucher Code:', result.voucher_code);
          }
        }, 3000);

      } else {
        setError(response.error || 'Spin failed');
        setIsSpinning(false);
      }
    } catch (error) {
      console.error('Spin wheel error:', error);
      setError('Network error occurred');
      setIsSpinning(false);
    }
  };

  const applyVoucherToCart = async (voucherCode: string) => {
    try {
      const voucherInfo = await validateVoucher(voucherCode);
      if (voucherInfo) {
        setAppliedVoucher({
          id: voucherInfo.program_id,
          code: voucherInfo.voucher_code,
          description: voucherInfo.description,
          discount: voucherInfo.reward_info.discount_fixed || voucherInfo.reward_info.discount_percent,
          condition: voucherInfo.program_name
        });
      }
    } catch (error) {
      console.error('Failed to apply voucher:', error);
    }
  };

  const closeModal = () => {
    setShowResult(false);
    setSpinResult(null);
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={closeModal}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          className="bg-white rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">🎰 Vòng Quay May Mắn</h2>
            <p className="text-gray-600">Quay để nhận ưu đãi hấp dẫn!</p>
            <p className="text-sm text-blue-600 mt-1">
              Còn lại: {spinsRemaining} lượt quay hôm nay
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {/* Lucky Wheel */}
          <div className="relative mb-6">
            <div className="w-64 h-64 mx-auto relative">
              {/* Wheel */}
              <motion.div
                className="w-full h-full rounded-full border-4 border-gray-300 relative overflow-hidden"
                animate={{ rotate: rotation }}
                transition={{ duration: 3, ease: "easeOut" }}
              >
                {prizes.map((prize, index) => {
                  const segmentAngle = 360 / prizes.length;
                  const startAngle = index * segmentAngle;

                  // Generate colors based on index
                  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
                  const color = colors[index % colors.length];

                  return (
                    <div
                      key={prize.id}
                      className="absolute inset-0 flex items-center justify-center text-white font-bold text-xs"
                      style={{
                        background: `conic-gradient(from ${startAngle}deg, ${color} 0deg, ${color} ${segmentAngle}deg, transparent ${segmentAngle}deg)`,
                        clipPath: `polygon(50% 50%, ${50 + 50 * Math.cos((startAngle - 90) * Math.PI / 180)}% ${50 + 50 * Math.sin((startAngle - 90) * Math.PI / 180)}%, ${50 + 50 * Math.cos((startAngle + segmentAngle - 90) * Math.PI / 180)}% ${50 + 50 * Math.sin((startAngle + segmentAngle - 90) * Math.PI / 180)}%)`
                      }}
                    >
                      <div className="text-center">
                        <div className="text-lg">{prize.lucky_wheel_icon || '🎁'}</div>
                        <div className="text-xs mt-1">{prize.name.substring(0, 15)}</div>
                      </div>
                    </div>
                  );
                })}
              </motion.div>

              {/* Pointer */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1">
                <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-500"></div>
              </div>

              {/* Center Button */}
              <button
                onClick={spinWheel}
                disabled={isSpinning || spinsRemaining <= 0}
                className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full font-bold text-white shadow-lg ${
                  isSpinning || spinsRemaining <= 0
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-red-500 hover:bg-red-600 active:scale-95'
                } transition-all duration-200`}
              >
                {isSpinning ? '...' : 'QUAY'}
              </button>
            </div>
          </div>

          {/* Spin Result */}
          <AnimatePresence>
            {showResult && spinResult && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="text-center mb-6"
              >
                <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-lg">
                  <div className="text-3xl mb-2">{spinResult.prize.lucky_wheel_icon || '🎁'}</div>
                  <h3 className="text-xl font-bold mb-2">{spinResult.prize.name}</h3>
                  <p className="text-sm">{spinResult.message}</p>

                  {/* Show discount info */}
                  {spinResult.prize.reward_info && (
                    <div className="mt-2 text-sm">
                      {spinResult.prize.reward_info.discount_percent > 0 && (
                        <p>💰 Giảm {spinResult.prize.reward_info.discount_percent}%</p>
                      )}
                      {spinResult.prize.reward_info.discount_fixed > 0 && (
                        <p>💰 Giảm {spinResult.prize.reward_info.discount_fixed.toLocaleString()} VND</p>
                      )}
                    </div>
                  )}

                  {spinResult.voucher_code && (
                    <div className="mt-3 p-2 bg-white bg-opacity-20 rounded">
                      <p className="text-xs">Mã voucher của bạn:</p>
                      <p className="font-mono font-bold text-lg">{spinResult.voucher_code}</p>
                      <p className="text-xs mt-1">Sẽ được áp dụng khi thanh toán!</p>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Instructions */}
          <div className="text-center text-sm text-gray-600 mb-4">
            <p>• Mỗi ngày được quay tối đa 10 lần</p>
            <p>• Voucher sẽ được tự động áp dụng vào giỏ hàng</p>
            <p>• Voucher có thể sử dụng cho đơn hàng tiếp theo</p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {spinsRemaining > 0 && !isSpinning && (
              <button
                onClick={spinWheel}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Quay lại ({spinsRemaining} lượt)
              </button>
            )}

            <button
              onClick={closeModal}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200"
            >
              Đóng
            </button>
          </div>

          {/* Daily Limit Message */}
          {spinsRemaining <= 0 && (
            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded text-center">
              <p className="font-semibold">Bạn đã hết lượt quay hôm nay!</p>
              <p className="text-sm">Quay lại vào ngày mai để nhận thêm ưu đãi nhé! 🌟</p>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default LuckyWheelAPI;
