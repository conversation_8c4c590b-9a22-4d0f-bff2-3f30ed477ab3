
import { useState, useRef, useEffect } from 'react';
import { X, Gift, Star, Sparkles } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import API from '@/services/api';

interface Prize {
  id: number;
  name: string;
  program_type: string;
  description: string;
  active: boolean;
  date_from: string | null;
  date_to: string | null;
  is_lucky_wheel: boolean;
  lucky_wheel_probability: number;
  lucky_wheel_icon: string;
  reward_info: {
    type: string;
    discount_mode: string;
    discount_percent: number;
    discount_fixed: number;
    currency: string;
  };
  rule_info: {
    minimum_amount: number;
    minimum_qty: number;
    mode: string;
  };
  trigger: string;
  applies_on: string;
  // Legacy fields for compatibility
  value?: string;
  color?: string;
  probability?: number;
}

const LuckyWheel = () => {
  const [isSpinning, setIsSpinning] = useState(false);
  const [selectedPrize, setSelectedPrize] = useState<Prize | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [spunToday, setSpunToday] = useState(false);
  const [spinsRemaining, setSpinsRemaining] = useState(10);
  const [rotation, setRotation] = useState(0);
  const [isFromCheckoutFlow, setIsFromCheckoutFlow] = useState(false);
  const [showLuckyWheelModal, setShowLuckyWheelModal] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [loading, setLoading] = useState(true);
  const wheelRef = useRef<HTMLDivElement>(null);

  // Load prizes from API
  useEffect(() => {
    loadPrizesFromAPI();
  }, []);

  const loadPrizesFromAPI = async () => {
    try {
      setLoading(true);
      const response = await API.Loyalty.getAvailablePrizes();

      if (response.success && response.data) {
        const promotions = response.data.promotions || [];

        console.log('🎰 Raw API response:', response.data);
        console.log('🎰 Promotions array:', promotions);

        // Add legacy fields for compatibility
        const prizesWithLegacy = promotions.map((prize: any, index: number) => ({
          ...prize,
          value: prize.reward_info?.discount_percent > 0
            ? `${prize.reward_info.discount_percent}%`
            : prize.reward_info?.discount_fixed > 0
            ? `${prize.reward_info.discount_fixed.toLocaleString()}đ`
            : 'Quà tặng',
          color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'][index % 8],
          probability: prize.lucky_wheel_probability || 10
        }));

        setPrizes(prizesWithLegacy);
        console.log('🎰 Loaded prizes from API:', prizesWithLegacy.length, prizesWithLegacy);
      } else {
        console.log('🎰 API failed, using fallback prizes. Response:', response);
        // Fallback to default prizes if API fails
        const defaultPrizes: Prize[] = [
          {
            id: 1, name: 'Giảm 10%', program_type: 'coupons', description: 'Giảm 10%',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 30, lucky_wheel_icon: '🎫',
            reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 10, discount_fixed: 0, currency: 'VND' },
            rule_info: { minimum_amount: 200000, minimum_qty: 1, mode: 'with_code' },
            trigger: 'auto', applies_on: 'current',
            value: '10%', color: '#FF6B6B', probability: 30
          },
          {
            id: 2, name: 'Giảm 15%', program_type: 'coupons', description: 'Giảm 15%',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 25, lucky_wheel_icon: '🎁',
            reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 15, discount_fixed: 0, currency: 'VND' },
            rule_info: { minimum_amount: 200000, minimum_qty: 1, mode: 'with_code' },
            trigger: 'auto', applies_on: 'current',
            value: '15%', color: '#4ECDC4', probability: 25
          },
          {
            id: 3, name: 'Giảm 20%', program_type: 'coupons', description: 'Giảm 20%',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 20, lucky_wheel_icon: '💎',
            reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 20, discount_fixed: 0, currency: 'VND' },
            rule_info: { minimum_amount: 500000, minimum_qty: 1, mode: 'with_code' },
            trigger: 'auto', applies_on: 'current',
            value: '20%', color: '#45B7D1', probability: 20
          },
          {
            id: 4, name: 'Giảm 5%', program_type: 'coupons', description: 'Giảm 5%',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 15, lucky_wheel_icon: '🏷️',
            reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 5, discount_fixed: 0, currency: 'VND' },
            rule_info: { minimum_amount: 100000, minimum_qty: 1, mode: 'with_code' },
            trigger: 'auto', applies_on: 'current',
            value: '5%', color: '#96CEB4', probability: 15
          },
          {
            id: 5, name: 'Freeship', program_type: 'coupons', description: 'Miễn phí vận chuyển',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 8, lucky_wheel_icon: '🚚',
            reward_info: { type: 'shipping', discount_mode: 'fixed', discount_percent: 0, discount_fixed: 50000, currency: 'VND' },
            rule_info: { minimum_amount: 200000, minimum_qty: 1, mode: 'with_code' },
            trigger: 'auto', applies_on: 'current',
            value: 'Free', color: '#FFEAA7', probability: 8
          },
          {
            id: 6, name: 'Chúc may mắn', program_type: 'coupons', description: 'Thử lại lần sau',
            active: true, date_from: null, date_to: null, is_lucky_wheel: true,
            lucky_wheel_probability: 2, lucky_wheel_icon: '🍀',
            reward_info: { type: 'none', discount_mode: 'none', discount_percent: 0, discount_fixed: 0, currency: 'VND' },
            rule_info: { minimum_amount: 0, minimum_qty: 0, mode: 'none' },
            trigger: 'auto', applies_on: 'current',
            value: '0%', color: '#DDA0DD', probability: 2
          }
        ];
        setPrizes(defaultPrizes);
        console.log('🎰 Using fallback prizes:', defaultPrizes.length);
      }
    } catch (error) {
      console.error('Failed to load prizes:', error);
      // Set default prizes on error
      const errorPrizes: Prize[] = [
        {
          id: 1, name: 'Giảm 10%', program_type: 'coupons', description: 'Giảm 10%',
          active: true, date_from: null, date_to: null, is_lucky_wheel: true,
          lucky_wheel_probability: 40, lucky_wheel_icon: '🎫',
          reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 10, discount_fixed: 0, currency: 'VND' },
          rule_info: { minimum_amount: 200000, minimum_qty: 1, mode: 'with_code' },
          trigger: 'auto', applies_on: 'current',
          value: '10%', color: '#FF6B6B', probability: 40
        },
        {
          id: 2, name: 'Giảm 15%', program_type: 'coupons', description: 'Giảm 15%',
          active: true, date_from: null, date_to: null, is_lucky_wheel: true,
          lucky_wheel_probability: 30, lucky_wheel_icon: '🎁',
          reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 15, discount_fixed: 0, currency: 'VND' },
          rule_info: { minimum_amount: 200000, minimum_qty: 1, mode: 'with_code' },
          trigger: 'auto', applies_on: 'current',
          value: '15%', color: '#4ECDC4', probability: 30
        },
        {
          id: 3, name: 'Giảm 5%', program_type: 'coupons', description: 'Giảm 5%',
          active: true, date_from: null, date_to: null, is_lucky_wheel: true,
          lucky_wheel_probability: 20, lucky_wheel_icon: '🏷️',
          reward_info: { type: 'discount', discount_mode: 'percent', discount_percent: 5, discount_fixed: 0, currency: 'VND' },
          rule_info: { minimum_amount: 100000, minimum_qty: 1, mode: 'with_code' },
          trigger: 'auto', applies_on: 'current',
          value: '5%', color: '#45B7D1', probability: 20
        },
        {
          id: 4, name: 'Chúc may mắn', program_type: 'coupons', description: 'Thử lại lần sau',
          active: true, date_from: null, date_to: null, is_lucky_wheel: true,
          lucky_wheel_probability: 10, lucky_wheel_icon: '🍀',
          reward_info: { type: 'none', discount_mode: 'none', discount_percent: 0, discount_fixed: 0, currency: 'VND' },
          rule_info: { minimum_amount: 0, minimum_qty: 0, mode: 'none' },
          trigger: 'auto', applies_on: 'current',
          value: '0%', color: '#96CEB4', probability: 10
        }
      ];
      setPrizes(errorPrizes);
      console.log('🎰 Using error fallback prizes:', errorPrizes.length);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if user has spun today from localStorage
    const lastSpin = localStorage.getItem('lastSpinTime');
    const remainingSpins = localStorage.getItem('spinsRemaining');

    if (lastSpin) {
      const lastSpinDate = new Date(lastSpin);
      const today = new Date();

      if (lastSpinDate.toDateString() === today.toDateString()) {
        // If it's the same day, check remaining spins
        if (remainingSpins) {
          const spinsLeft = parseInt(remainingSpins, 10);
          setSpinsRemaining(spinsLeft);
          setSpunToday(spinsLeft <= 0);
        }
      } else {
        // If it's a new day, reset spins to 10
        localStorage.setItem('spinsRemaining', '10');
        setSpinsRemaining(10);
        setSpunToday(false);
      }
    } else {
      // First time spinning, set to 10 spins
      localStorage.setItem('spinsRemaining', '10');
      setSpinsRemaining(10);
    }
  }, []);

  // Listen for checkout flow events
  useEffect(() => {
    const handleCheckoutFlow = () => {
      setIsFromCheckoutFlow(true);
    };

    window.addEventListener('show-lucky-wheel-before-checkout', handleCheckoutFlow);

    return () => {
      window.removeEventListener('show-lucky-wheel-before-checkout', handleCheckoutFlow);
    };
  }, []);

  // Calculate prize based on probability (same as mobile)
  const selectPrize = (): Prize => {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (const prize of prizes) {
      cumulative += prize.probability;
      if (random <= cumulative) {
        return prize;
      }
    }

    return prizes[0]; // Fallback
  };

  const spinWheel = async () => {
    if (isSpinning || spunToday) return;

    setIsSpinning(true);
    setShowResult(false);

    try {
      // Call real API to spin wheel
      const response = await API.Loyalty.spinLuckyWheel('<EMAIL>');

      if (response.success && response.data) {
        const result = response.data;

        // Find prize in current prizes list
        const winningPrize = prizes.find(p => p.id === result.prize.id) || prizes[0];
        const prizeIndex = prizes.findIndex(p => p.id === result.prize.id);

        // Calculate rotation (multiple full rotations + prize position)
        const baseRotation = 360 * 5; // 5 full rotations
        const prizeAngle = (360 / prizes.length) * prizeIndex;
        const finalRotation = baseRotation + prizeAngle;

        setRotation(prev => prev + finalRotation);

        // Show result after animation
        setTimeout(() => {
          setSelectedPrize(winningPrize);
          setShowResult(true);
          setIsSpinning(false);

          // Save voucher to localStorage
          if (result.voucher_code) {
            localStorage.setItem('lucky_wheel_voucher', result.voucher_code);
            localStorage.setItem('lucky_wheel_prize', JSON.stringify(result.prize));

            console.log('🎉 Lucky Wheel Prize:', result.prize.name);
            console.log('🎟️ Voucher Code:', result.voucher_code);
          }

          // Update spins remaining
          const newSpinsRemaining = spinsRemaining - 1;
          setSpinsRemaining(newSpinsRemaining);
          localStorage.setItem('spinsRemaining', newSpinsRemaining.toString());

          if (newSpinsRemaining <= 0) {
            setSpunToday(true);
          }

        }, 3000);
      } else {
        throw new Error(response.error || 'Spin failed');
      }
    } catch (error) {
      console.error('Lucky Wheel API error:', error);
      setIsSpinning(false);

      // Fallback to original logic if API fails
      const winningPrize = selectPrize();
      const prizeIndex = prizes.findIndex(p => p.id === winningPrize.id);
      const baseRotation = 360 * 5;
      const prizeAngle = (360 / prizes.length) * prizeIndex;
      const finalRotation = baseRotation + prizeAngle;

      setRotation(prev => prev + finalRotation);

      setTimeout(() => {
        setSelectedPrize(winningPrize);
        setShowResult(true);
        setIsSpinning(false);
        saveVoucherToStorage(winningPrize);
      }, 3000);
    }
  };

  const closeLuckyWheelModal = () => {
    setShowLuckyWheelModal(false);
  };

  const resetWheel = () => {
    setShowResult(false);
    setSelectedPrize(null);
  };

  // Map prize names to voucher codes
  const getVoucherCode = (prizeName: string): string => {
    switch (prizeName) {
      case 'Giảm 5%': return 'GIAM5';
      case 'Giảm 10%': return 'GIAM10';
      case 'Giảm 15%': return 'GIAM15';
      case 'Giảm 20%': return 'GIAM20';
      case 'Freeship': return 'FREESHIP';
      default: return '';
    }
  };

  const handleContinueShopping = () => {
    resetWheel();
    setIsFromCheckoutFlow(false);
  };

  const handleGoToCheckout = () => {
    setIsFromCheckoutFlow(false);

    // Navigate to checkout with voucher code
    if (selectedPrize) {
      const voucherCode = getVoucherCode(selectedPrize.name);
      let checkoutUrl = '/checkout';
      if (voucherCode) {
        checkoutUrl += `?voucher=${voucherCode}`;
      }
      window.location.href = checkoutUrl;
    } else {
      // No prize selected, go to checkout directly
      window.location.href = '/checkout';
    }
  };

  // Save voucher to localStorage for later use
  const saveVoucherToStorage = (prize: Prize) => {
    if (prize.name === 'Chúc may mắn') {
      return; // No voucher for consolation prize
    }

    const voucherCode = getVoucherCode(prize.name);
    if (!voucherCode) return;

    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7); // 7 days validity

    let discount = 0;
    let type: 'percentage' | 'freeship' = 'percentage';

    switch (prize.name) {
      case 'Giảm 5%':
        discount = 5;
        break;
      case 'Giảm 10%':
        discount = 10;
        break;
      case 'Giảm 15%':
        discount = 15;
        break;
      case 'Giảm 20%':
        discount = 20;
        break;
      case 'Freeship':
        discount = 0;
        type = 'freeship';
        break;
    }

    const voucher = {
      code: voucherCode,
      discount,
      type,
      expiryDate: expiryDate.toISOString(),
      prizeName: prize.name
    };

    // Save to mobile cart context storage
    try {
      const mobileData = JSON.parse(localStorage.getItem('gao-lang-ta-mobile-data') || '{}');
      mobileData.savedVoucher = voucher;
      localStorage.setItem('gao-lang-ta-mobile-data', JSON.stringify(mobileData));
      console.log('Desktop: Voucher saved to storage:', voucher);
    } catch (error) {
      console.error('Error saving voucher:', error);
    }
  };



  return (
    <section id="lucky-wheel" className="bg-gradient-to-b from-white to-gray-50 py-4 md:py-6">
      <div className="container mx-auto px-4">
        <div className="text-center mb-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-2">Vòng quay may mắn</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Quay và nhận ngay cơ hội trúng nhiều phần quà hấp dẫn từ Binhanjewelry
          </p>
        </div>

        <div className="flex flex-col lg:flex-row items-center justify-center gap-4">
          <div className="lg:w-3/5 flex flex-col items-center">
            {/* Lucky Wheel */}
            <div className="relative w-80 h-80 mb-4">
              {/* Wheel with conic gradient background */}
              <div
                className="w-full h-full rounded-full border-8 border-gray-800 relative transition-transform duration-3000 ease-out"
                style={{
                  transform: `rotate(${rotation}deg)`,
                  transitionDuration: isSpinning ? '3000ms' : '0ms',
                  background: prizes.length > 0 ? `conic-gradient(
                    ${prizes.map((prize, index) => {
                      const segmentAngle = 360 / prizes.length;
                      const startAngle = index * segmentAngle;
                      const endAngle = (index + 1) * segmentAngle;
                      return `${prize.color} ${startAngle}deg ${endAngle}deg`;
                    }).join(', ')}
                  )` : '#ccc'
                }}
              >
                {/* Prize labels */}
                {prizes.map((prize, index) => {
                  const angle = (360 / prizes.length) * index + (360 / prizes.length) / 2;
                  const radian = (angle * Math.PI) / 180;
                  const radius = 100; // Distance from center
                  const x = Math.cos(radian) * radius;
                  const y = Math.sin(radian) * radius;

                  return (
                    <div
                      key={prize.id}
                      className="absolute text-white text-center font-bold text-sm"
                      style={{
                        left: '50%',
                        top: '50%',
                        transform: `translate(${x}px, ${y}px) translate(-50%, -50%) rotate(${angle + 90}deg)`,
                        width: '80px'
                      }}
                    >
                      <div>{prize.lucky_wheel_icon || '🎁'}</div>
                      <div className="text-xs">{prize.name.substring(0, 10)}</div>
                    </div>
                  );
                })}

                {/* Segment dividers */}
                {prizes.map((_, index) => {
                  const angle = (360 / prizes.length) * index;
                  return (
                    <div
                      key={`divider-${index}`}
                      className="absolute w-0.5 bg-white"
                      style={{
                        height: '50%',
                        left: '50%',
                        top: '0%',
                        transformOrigin: 'bottom',
                        transform: `translateX(-50%) rotate(${angle}deg)`
                      }}
                    />
                  );
                })}
              </div>

              {/* Center pointer */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white rounded-full border-4 border-gray-800 z-10"></div>

              {/* Top pointer */}
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-20">
                <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-gray-800"></div>
              </div>
            </div>

            {/* Spin Button */}
            {!showResult && (
              <>
                <button
                  onClick={spinWheel}
                  disabled={isSpinning || spunToday || loading}
                  className={`
                    w-64 py-4 rounded-2xl font-semibold text-white transition-all duration-300
                    ${isSpinning || spunToday || loading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-gradient-to-r from-premium-gold to-premium-navy hover:shadow-lg active:scale-95'
                    }
                  `}
                >
                  {loading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Đang tải...</span>
                    </div>
                  ) : isSpinning ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Đang quay...</span>
                    </div>
                  ) : spunToday ? (
                    'Đã hết lượt quay hôm nay'
                  ) : (
                    '🎰 QUAY NGAY'
                  )}
                </button>

                {/* Spins remaining indicator */}
                <p className="text-sm text-gray-600 text-center mt-3">
                  {spunToday
                    ? "Bạn đã hết lượt quay hôm nay. Quay lại vào ngày mai!"
                    : `Còn lại ${spinsRemaining} lượt quay hôm nay`
                  }
                </p>
              </>
            )}

            {/* Result */}
            {showResult && selectedPrize && (
              <div className="text-center w-full max-w-md">
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-2xl p-6 mb-6 border border-yellow-200">
                  <div className="flex items-center justify-center mb-3">
                    <Sparkles className="w-6 h-6 text-yellow-500 mr-2" />
                    <span className="text-xl font-bold text-gray-800">Chúc mừng!</span>
                    <Sparkles className="w-6 h-6 text-yellow-500 ml-2" />
                  </div>

                  <div className="text-3xl font-bold text-premium-gold mb-3">
                    {selectedPrize.name}
                  </div>

                  {selectedPrize.value !== '0%' && (
                    <p className="text-sm text-gray-600">
                      Mã giảm giá đã được áp dụng vào đơn hàng
                    </p>
                  )}
                </div>

                <div className="space-y-3">
                  <button
                    onClick={handleGoToCheckout}
                    className="w-full py-4 bg-gradient-to-r from-flokara-orange to-flokara-teal text-white font-semibold rounded-2xl hover:shadow-lg transition-all duration-300"
                  >
                    Đặt hàng ngay
                  </button>

                  <button
                    onClick={handleContinueShopping}
                    className="w-full py-4 border-2 border-flokara-teal text-flokara-teal font-semibold rounded-2xl hover:bg-flokara-teal hover:text-white transition-all duration-300"
                  >
                    Tiếp tục mua sắm
                  </button>
                </div>
              </div>
            )}
          </div>

          <div className="lg:w-2/5 lg:max-w-sm">
            <div className="bg-gradient-to-br from-white to-gray-50 p-4 rounded-2xl shadow-lg border border-gray-100">
              {/* Header */}
              <div className="text-center mb-4">
                <div className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-premium-gold to-premium-navy rounded-full mb-2">
                  <Gift className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-premium-navy">Phần thưởng hấp dẫn</h3>
                <p className="text-xs text-gray-600 mt-1">Cơ hội trúng thưởng lên đến 20%</p>
              </div>

              {/* Prize Grid */}
              <div className="grid grid-cols-2 gap-2 mb-4">
                {loading ? (
                  // Loading state
                  Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="bg-white p-2 rounded-lg border border-gray-200 animate-pulse">
                      <div className="w-6 h-6 bg-gray-300 rounded-full mx-auto mb-1"></div>
                      <div className="h-3 bg-gray-300 rounded mb-1"></div>
                      <div className="h-2 bg-gray-300 rounded"></div>
                    </div>
                  ))
                ) : (
                  prizes.map(prize => (
                    <div key={prize.id} className="group relative bg-white p-2 rounded-lg border border-gray-200 hover:border-premium-gold hover:shadow-md transition-all duration-300">
                      {/* Prize indicator */}
                      <div
                        className="w-6 h-6 rounded-full flex items-center justify-center text-white font-bold text-xs mb-1 mx-auto"
                        style={{ backgroundColor: prize.color }}
                      >
                        {prize.lucky_wheel_icon || '🎁'}
                      </div>

                      {/* Prize info */}
                      <div className="text-center">
                        <h4 className="font-semibold text-gray-800 text-xs mb-0.5">{prize.name.substring(0, 15)}</h4>
                        <p className="text-xs text-gray-500">
                          {prize.reward_info?.discount_percent > 0
                            ? `Giảm ${prize.reward_info.discount_percent}%`
                            : prize.reward_info?.discount_fixed > 0
                            ? `Giảm ${prize.reward_info.discount_fixed.toLocaleString()}đ`
                            : 'Quà tặng đặc biệt'
                          }
                        </p>
                      </div>

                      {/* Hover effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-premium-gold/5 to-premium-navy/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                  ))
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-2 mb-4">
                <div className="text-center p-2 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="text-sm font-bold text-blue-600">10</div>
                  <div className="text-xs text-blue-600">Lượt/ngày</div>
                </div>
                <div className="text-center p-2 bg-green-50 rounded-lg border border-green-100">
                  <div className="text-sm font-bold text-green-600">7</div>
                  <div className="text-xs text-green-600">Ngày hiệu lực</div>
                </div>
              </div>

              {/* Terms */}
              <div className="p-2 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                <div className="flex items-start gap-1">
                  <Star className="w-3 h-3 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <p className="text-xs text-amber-800">
                    <span className="font-semibold">Lưu ý:</span> Phần thưởng được áp dụng tự động vào đơn hàng tiếp theo.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </section>
  );
};

export default LuckyWheel;
