import { useState, useEffect } from 'react';
import { X, Gift, Star, Sparkles } from 'lucide-react';
import { useMobileCartContext } from '../../contexts/MobileCartContext';
import LuckyWheelAPI from '../LuckyWheelAPI';

interface LuckyWheelPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Prize {
  id: string;
  name: string;
  value: string;
  color: string;
  probability: number;
}

const LuckyWheelPopup = ({ isOpen, onClose }: LuckyWheelPopupProps) => {
  const { markLuckyWheelSpun, saveVoucher, goToCheckout } = useMobileCartContext();
  const [isSpinning, setIsSpinning] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [selectedPrize, setSelectedPrize] = useState<Prize | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [spinsRemaining, setSpinsRemaining] = useState(10);
  const [spunToday, setSpunToday] = useState(false);
  const [isFromCheckoutFlow, setIsFromCheckoutFlow] = useState(false);
  const [showEmailInput, setShowEmailInput] = useState(true);
  const [userEmail, setUserEmail] = useState('');

  // Prize configuration
  const prizes: Prize[] = [
    { id: '1', name: 'Giảm 10%', value: '10%', color: '#FF6B6B', probability: 30 },
    { id: '2', name: 'Giảm 5%', value: '5%', color: '#4ECDC4', probability: 25 },
    { id: '3', name: 'Freeship', value: 'Free', color: '#45B7D1', probability: 20 },
    { id: '4', name: 'Giảm 15%', value: '15%', color: '#96CEB4', probability: 15 },
    { id: '5', name: 'Chúc may mắn', value: '0%', color: '#FFEAA7', probability: 8 },
    { id: '6', name: 'Giảm 20%', value: '20%', color: '#DDA0DD', probability: 2 }
  ];

  // Calculate prize based on probability
  const selectPrize = (): Prize => {
    const random = Math.random() * 100;
    let cumulative = 0;

    for (const prize of prizes) {
      cumulative += prize.probability;
      if (random <= cumulative) {
        return prize;
      }
    }

    return prizes[0]; // Fallback
  };

  // Create voucher from prize
  const createVoucherFromPrize = (prize: Prize) => {
    if (prize.name === 'Chúc may mắn') {
      return null; // No voucher for consolation prize
    }

    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7); // 7 days validity

    let code = '';
    let discount = 0;
    let type: 'percentage' | 'freeship' = 'percentage';

    switch (prize.name) {
      case 'Giảm 5%':
        code = 'GIAM5';
        discount = 5;
        break;
      case 'Giảm 10%':
        code = 'GIAM10';
        discount = 10;
        break;
      case 'Giảm 15%':
        code = 'GIAM15';
        discount = 15;
        break;
      case 'Giảm 20%':
        code = 'GIAM20';
        discount = 20;
        break;
      case 'Freeship':
        code = 'FREESHIP';
        discount = 0;
        type = 'freeship';
        break;
      default:
        return null;
    }

    return {
      code,
      discount,
      type,
      expiryDate: expiryDate.toISOString(),
      prizeName: prize.name
    };
  };

  // Handle spin
  const handleSpin = () => {
    if (isSpinning || spunToday) return;

    // Record spin time and decrement remaining spins
    const now = new Date();
    localStorage.setItem('lastSpinTime', now.toString());

    const newSpinsRemaining = spinsRemaining - 1;
    setSpinsRemaining(newSpinsRemaining);
    localStorage.setItem('spinsRemaining', newSpinsRemaining.toString());

    // If no spins left, mark as spun today
    if (newSpinsRemaining <= 0) {
      setSpunToday(true);
    }

    setIsSpinning(true);
    setShowResult(false);

    // Select winning prize
    const winningPrize = selectPrize();
    const prizeIndex = prizes.findIndex(p => p.id === winningPrize.id);

    // Calculate rotation (multiple full rotations + prize position)
    const baseRotation = 360 * 5; // 5 full rotations
    const prizeAngle = (360 / prizes.length) * prizeIndex;
    const finalRotation = baseRotation + prizeAngle;

    setRotation(prev => prev + finalRotation);

    // Show result after animation
    setTimeout(() => {
      setSelectedPrize(winningPrize);
      setShowResult(true);
      setIsSpinning(false);

      // Create and save voucher if applicable
      const voucher = createVoucherFromPrize(winningPrize);
      if (voucher) {
        saveVoucher(voucher);
        console.log('Voucher saved:', voucher);
      }

      // Mark as spun today in mobile cart context
      markLuckyWheelSpun();

      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([100, 50, 100]);
      }

      // Auto-redirect to checkout if from checkout flow
      if (isFromCheckoutFlow) {
        setTimeout(() => {
          handleGoToCheckout();
        }, 2000); // Give user 2 seconds to see the result
      }
    }, 3000);
  };

  // Check spin limit when popup opens
  useEffect(() => {
    if (isOpen) {
      setShowResult(false);
      setSelectedPrize(null);

      // Check spin limit from localStorage
      const lastSpin = localStorage.getItem('lastSpinTime');
      const remainingSpins = localStorage.getItem('spinsRemaining');

      if (lastSpin) {
        const lastSpinDate = new Date(lastSpin);
        const today = new Date();

        if (lastSpinDate.toDateString() === today.toDateString()) {
          // Same day, check remaining spins
          if (remainingSpins) {
            const spinsLeft = parseInt(remainingSpins, 10);
            setSpinsRemaining(spinsLeft);
            setSpunToday(spinsLeft <= 0);
          }
        } else {
          // New day, reset to 10 spins
          localStorage.setItem('spinsRemaining', '10');
          setSpinsRemaining(10);
          setSpunToday(false);
        }
      } else {
        // First time, set to 10 spins
        localStorage.setItem('spinsRemaining', '10');
        setSpinsRemaining(10);
        setSpunToday(false);
      }
    }
  }, [isOpen]);

  // Listen for checkout flow events
  useEffect(() => {
    const handleCheckoutFlow = () => {
      setIsFromCheckoutFlow(true);
    };

    window.addEventListener('show-lucky-wheel-before-checkout', handleCheckoutFlow);

    return () => {
      window.removeEventListener('show-lucky-wheel-before-checkout', handleCheckoutFlow);
    };
  }, []);

  // Handle continue shopping
  const handleContinueShopping = () => {
    onClose();
    setIsFromCheckoutFlow(false);
  };

  // Map prize names to voucher codes
  const getVoucherCode = (prizeName: string): string => {
    switch (prizeName) {
      case 'Giảm 5%': return 'GIAM5';
      case 'Giảm 10%': return 'GIAM10';
      case 'Giảm 15%': return 'GIAM15';
      case 'Giảm 20%': return 'GIAM20';
      case 'Free Ship': return 'FREESHIP';
      default: return '';
    }
  };

  // Handle go to checkout
  const handleGoToCheckout = () => {
    onClose();
    setIsFromCheckoutFlow(false);

    // Direct navigation to checkout
    setTimeout(() => {
      goToCheckout();
    }, 300);
  };

  const handleEmailSubmit = () => {
    if (userEmail.trim()) {
      setShowEmailInput(false);
    }
  };

  const handleClose = () => {
    setShowEmailInput(true);
    setUserEmail('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Popup Content */}
      <div className="relative bg-white rounded-3xl p-6 max-w-sm w-full shadow-2xl">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>

        {/* Email Input */}
        {showEmailInput && (
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Gift className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              🎉 Vòng quay may mắn
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Nhập email để tham gia vòng quay!
            </p>

            <input
              type="email"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              placeholder="Nhập email của bạn"
              className="w-full p-3 border border-gray-300 rounded-lg mb-4 text-center"
            />

            <button
              onClick={handleEmailSubmit}
              disabled={!userEmail.trim()}
              className="w-full py-3 bg-gradient-to-r from-flokara-orange to-flokara-teal text-white font-semibold rounded-lg disabled:opacity-50"
            >
              Bắt đầu quay
            </button>
          </div>
        )}

        {/* Lucky Wheel API Component */}
        {!showEmailInput && (
          <LuckyWheelAPI
            isOpen={true}
            onClose={handleClose}
            userEmail={userEmail}
          />
        )}
      </div>
    </div>
  );
};

export default LuckyWheelPopup;
