
import { useState, useEffect } from 'react';
import ProductCard from './ProductCard';
import { Star, Award, Sparkles, TrendingUp } from 'lucide-react';
import API from '@/services/api';

interface Product {
  id: number;
  name: string;
  price: number;
  originalPrice?: number;
  imageUrl: string;
  category: string;
  isBestseller?: boolean;
  isNew?: boolean;
  rating?: number;
  reviewCount?: number;
  description?: string;
}

const FeaturedProducts = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'hot'>('all');
  const [isVisible, setIsVisible] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load products from API
  useEffect(() => {
    loadFeaturedProducts();
  }, []);

  // Intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('products');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  const loadFeaturedProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🛍️ Loading featured products from API...');

      // Try to get products from API
      const response = await API.Products.getAll();

      if (response.success && response.data) {
        const apiProducts = response.data.products || [];

        console.log('🛍️ API Products loaded:', apiProducts.length);

        // Transform API data to match our interface
        const transformedProducts: Product[] = apiProducts.map((product: any) => ({
          id: product.id,
          name: product.name,
          price: product.price || 0,
          originalPrice: product.original_price,
          imageUrl: product.image_url || '/placeholder.jpg',
          category: product.category_name || 'Trang sức',
          isBestseller: product.is_bestseller || false,
          isNew: product.is_new || false,
          rating: product.rating || 4.5,
          reviewCount: product.review_count || 0,
          description: product.description || ''
        }));

        setProducts(transformedProducts);
        console.log('🛍️ Featured products loaded successfully:', transformedProducts.length);

      } else {
        throw new Error(response.error || 'Failed to load products');
      }
    } catch (error) {
      console.error('Failed to load featured products:', error);
      setError('Không thể tải sản phẩm. Vui lòng thử lại sau.');

      // Fallback to empty array
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter products based on active tab
  const filteredProducts = activeTab === 'all'
    ? products.slice(0, 6) // Show first 6 products for featured
    : products.filter(product => product.isBestseller).slice(0, 6); // Show bestsellers

  return (
    <section id="products" className="relative py-6 bg-gradient-to-b from-gray-50 to-white overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-flokara-orange/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-flokara-teal/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-flokara-orange/3 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Header */}
        <div className={`text-center mb-6 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-flokara-orange/10 to-flokara-teal/10 rounded-full text-flokara-teal font-semibold text-sm border border-flokara-orange/20 backdrop-blur-sm mb-6">
            <Award className="w-4 h-4 mr-2 text-flokara-orange" />
            Trang sức được yêu thích nhất
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-flokara-teal to-flokara-orange bg-clip-text text-transparent">
            Bộ sưu tập cao cấp
          </h2>

          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Khám phá bộ sưu tập trang sức cao cấp nhất, được chế tác thủ công
            <br className="hidden md:block" />
            với kim cương thiên nhiên và vàng 18K nguyên chất.
          </p>
        </div>

        {/* Enhanced Tab Selector */}
        <div className={`flex justify-center mb-6 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="inline-flex p-1.5 bg-white rounded-2xl shadow-lg border border-gray-100 backdrop-blur-sm">
            <button
              className={`group relative px-8 py-3 rounded-xl text-sm font-semibold transition-all duration-300 ${
                activeTab === 'all'
                  ? 'bg-gradient-to-r from-flokara-teal to-flokara-orange text-white shadow-lg transform scale-105'
                  : 'bg-transparent text-gray-600 hover:text-flokara-teal hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('all')}
            >
              <Star className="w-4 h-4 mr-2 inline" />
              Bộ sưu tập nổi bật
              {activeTab === 'all' && (
                <div className="absolute inset-0 bg-gradient-to-r from-flokara-teal to-flokara-orange rounded-xl opacity-20 animate-pulse"></div>
              )}
            </button>
            <button
              className={`group relative px-8 py-3 rounded-xl text-sm font-semibold transition-all duration-300 ${
                activeTab === 'hot'
                  ? 'bg-gradient-to-r from-flokara-orange to-flokara-teal text-white shadow-lg transform scale-105'
                  : 'bg-transparent text-gray-600 hover:text-flokara-orange hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab('hot')}
            >
              <TrendingUp className="w-4 h-4 mr-2 inline" />
              Bestseller
              {activeTab === 'hot' && (
                <div className="absolute inset-0 bg-gradient-to-r from-flokara-orange to-flokara-teal rounded-xl opacity-20 animate-pulse"></div>
              )}
            </button>
          </div>
        </div>

        {/* Enhanced Product Grid */}
        <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          {loading ? (
            // Loading state
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse">
                  <div className="aspect-square bg-gray-300"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-300 rounded mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                    <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            // Error state
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Không thể tải sản phẩm</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={loadFeaturedProducts}
                className="px-6 py-2 bg-flokara-orange text-white rounded-lg hover:bg-flokara-orange/90 transition-colors"
              >
                Thử lại
              </button>
            </div>
          ) : filteredProducts.length === 0 ? (
            // Empty state
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Chưa có sản phẩm</h3>
              <p className="text-gray-600">Bộ sưu tập đang được cập nhật. Vui lòng quay lại sau.</p>
            </div>
          ) : (
            // Products grid
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
              {filteredProducts.map((product, index) => (
                <div
                  key={product.id}
                  className="transform transition-all duration-500 hover:scale-105"
                  style={{
                    animationDelay: isVisible ? `${index * 100}ms` : '0ms'
                  }}
                >
                  <ProductCard product={product} />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Enhanced CTA Section */}
        <div className={`mt-6 text-center transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="bg-gradient-to-r from-flokara-teal/5 to-flokara-orange/5 rounded-3xl p-8 border border-flokara-orange/10 backdrop-blur-sm">
            <div className="max-w-2xl mx-auto">
              <div className="flex justify-center mb-4">
                <Sparkles className="w-8 h-8 text-flokara-orange" />
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-flokara-teal mb-4">
                Khám phá thêm nhiều trang sức
              </h3>
              <p className="text-gray-600 mb-6">
                Hơn 100+ mẫu trang sức cao cấp khác đang chờ bạn khám phá
              </p>
              <button className="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-flokara-orange to-flokara-teal text-white font-semibold rounded-2xl hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                Xem toàn bộ bộ sưu tập
                <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
