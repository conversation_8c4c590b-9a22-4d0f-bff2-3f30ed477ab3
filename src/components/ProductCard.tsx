
import { Product } from '../data/products_jewelry_new';
import { JewelryProduct } from '../data/jewelry_products';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Eye, Star, Award } from 'lucide-react';
import JewelryImagePlaceholder from './JewelryImagePlaceholder';

interface ProductCardProps {
  product: Product | JewelryProduct;
}

const ProductCard = ({ product }: ProductCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Use product.images array if available, otherwise create an array with just the main image
  const productImages = product.images || [product.imageUrl];

  // Check if this is a jewelry product
  const isJewelryProduct = (prod: Product | JewelryProduct): prod is JewelryProduct => {
    return 'material' in prod;
  };

  // Get jewelry type for placeholder
  const getJewelryType = (category: string): 'ring' | 'necklace' | 'earrings' | 'bracelet' => {
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('nhẫn')) return 'ring';
    if (lowerCategory.includes('dây chuyền')) return 'necklace';
    if (lowerCategory.includes('bông tai')) return 'earrings';
    if (lowerCategory.includes('lắc tay')) return 'bracelet';
    return 'ring'; // default
  };

  // Check if image exists or use placeholder
  const shouldUsePlaceholder = (imageUrl: string) => {
    return !imageUrl || imageUrl.includes('/jewelry/') || imageUrl.includes('placeholder');
  };

  // Auto-rotate images on hover
  const startImageRotation = () => {
    if (productImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex(prev => (prev + 1) % productImages.length);
      }, 1500);

      return () => clearInterval(interval);
    }
    return undefined;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Check if user has spun today
  const hasSpunToday = () => {
    const lastSpin = localStorage.getItem('lastSpinTime');
    if (!lastSpin) return false;

    const lastSpinDate = new Date(lastSpin);
    const today = new Date();

    return lastSpinDate.toDateString() === today.toDateString();
  };

  // Handle buy now with lucky wheel flow
  const handleBuyNow = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Calculate variantId based on product ID (for API compatibility)
      // This maps product IDs to variant IDs based on our test data
      const variantIdMap: { [key: number]: number } = {
        1: 41, 2: 42, 3: 43, 4: 44, 5: 45, 6: 46, 7: 47, 8: 48
      };

      const variantId = variantIdMap[product.id] || (product.id + 40); // Fallback calculation

      // Add product to cart via API
      const cartData = {
        id: Date.now(),
        variantId: variantId,
        product: {
          id: product.id,
          name: product.name,
          price: product.price,
          imageUrl: product.images?.[0] || product.imageUrl,
          category: product.category
        },
        quantity: 1,
        attributes: {}
      };

      // Get existing cart and add locally (for UI)
      const existingCart = JSON.parse(localStorage.getItem('flokaraCart') || '[]');
      const updatedCart = [...existingCart, cartData];
      localStorage.setItem('flokaraCart', JSON.stringify(updatedCart));

      console.log('🛒 Adding to cart:', { variantId, productId: product.id, name: product.name });

      // Check if user has spun today
      if (hasSpunToday()) {
        // Already spun today, go directly to checkout
        window.location.href = '/checkout';
      } else {
        // Haven't spun today, trigger lucky wheel first
        const event = new CustomEvent('show-lucky-wheel-before-checkout');
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error('❌ Error adding to cart:', error);
      // Fallback to direct checkout
      window.location.href = '/checkout';
    }
  };

  return (
    <div
      className="group bg-white rounded-3xl shadow-lg overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-flokara-orange/10 border border-gray-100 hover:border-flokara-orange/20"
      onMouseEnter={() => {
        setIsHovered(true);
        const cleanup = startImageRotation();
        return () => cleanup && cleanup();
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        setCurrentImageIndex(0);
      }}
    >
      <div className="relative overflow-hidden h-72">
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          {/* Conditional rendering: placeholder for jewelry or real image */}
          {isJewelryProduct(product) && shouldUsePlaceholder(productImages[currentImageIndex]) ? (
            <JewelryImagePlaceholder
              type={getJewelryType(product.category)}
              className={`w-full h-full transition-all duration-700 ${isHovered ? 'scale-110' : 'scale-100'}`}
            />
          ) : (
            <img
              src={productImages[currentImageIndex]}
              alt={product.name}
              className={`w-full h-full object-cover transition-all duration-700 ${isHovered ? 'scale-110' : 'scale-100'}`}
              onError={(e) => {
                // Fallback to placeholder if image fails to load
                if (isJewelryProduct(product)) {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  // Show placeholder instead
                }
              }}
            />
          )}
          {/* Jewelry-specific badges */}
          <div className="absolute top-4 right-4 z-20 flex flex-col gap-2">
            {isJewelryProduct(product) && product.isNew && (
              <div className="relative">
                <span className="inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-bold uppercase py-2 px-3 rounded-full shadow-lg">
                  ✨ Mới
                </span>
              </div>
            )}

            {isJewelryProduct(product) && product.isBestseller && (
              <div className="relative">
                <span className="inline-flex items-center bg-gradient-to-r from-flokara-orange to-red-500 text-white text-xs font-bold uppercase py-2 px-3 rounded-full shadow-lg animate-pulse">
                  👑 Bestseller
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-flokara-orange to-red-500 rounded-full opacity-30 animate-ping"></div>
              </div>
            )}

            {!isJewelryProduct(product) && (product as Product).isHot && (
              <div className="relative">
                <span className="inline-flex items-center bg-gradient-to-r from-flokara-orange to-red-500 text-white text-xs font-bold uppercase py-2 px-3 rounded-full shadow-lg animate-pulse">
                  🔥 Hot
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-flokara-orange to-red-500 rounded-full opacity-30 animate-ping"></div>
              </div>
            )}

            {isJewelryProduct(product) && product.discount && product.discount > 0 && (
              <div className="relative">
                <span className="inline-flex items-center bg-gradient-to-r from-green-500 to-green-600 text-white text-xs font-bold uppercase py-2 px-3 rounded-full shadow-lg">
                  -{product.discount}%
                </span>
              </div>
            )}
          </div>

          {/* Enhanced Image indicators */}
          {productImages.length > 1 && (
            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-20">
              {productImages.map((_, idx) => (
                <span
                  key={idx}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    currentImageIndex === idx
                      ? 'bg-white scale-125 shadow-lg'
                      : 'bg-white/60 hover:bg-white/80'
                  }`}
                />
              ))}
            </div>
          )}
        </div>

        <div className="p-6">
          <Link to={`/product/${product.id}`}>
            <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-flokara-teal transition-colors duration-300">
              {product.name}
            </h3>
            <p className="text-gray-600 text-sm mb-6 line-clamp-2 leading-relaxed">
              {product.description}
            </p>
          </Link>

          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-flokara-orange">
                  {formatPrice(product.price)}
                </span>
                {isJewelryProduct(product) && product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-sm text-gray-400 line-through">
                    {formatPrice(product.originalPrice)}
                  </span>
                )}
              </div>

              {/* Jewelry-specific info */}
              {isJewelryProduct(product) && (
                <div className="flex flex-col gap-1 mt-1">
                  <span className="text-xs text-flokara-teal font-medium">
                    {product.material}
                  </span>
                  {product.gemstone && (
                    <span className="text-xs text-gray-500">
                      {product.gemstone}
                    </span>
                  )}
                  {product.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600">
                        {product.rating} ({product.reviewCount} đánh giá)
                      </span>
                    </div>
                  )}
                </div>
              )}

              {!isJewelryProduct(product) && (
                <span className="text-xs text-gray-500">Giá đã bao gồm VAT</span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Link to={`/product/${product.id}`} className="flex-1">
              <Button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-2xl px-4 py-3 transition-all duration-300">
                <Eye className="mr-2 h-4 w-4" />
                Xem chi tiết
              </Button>
            </Link>

            <Button
              onClick={handleBuyNow}
              className="flex-1 bg-gradient-to-r from-flokara-orange to-flokara-teal hover:from-flokara-teal hover:to-flokara-orange text-white rounded-2xl px-4 py-3 transition-all duration-300 hover:shadow-lg hover:shadow-flokara-orange/25 transform hover:-translate-y-0.5"
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              Đặt hàng ngay
            </Button>
          </div>
        </div>
    </div>
  );
};

export default ProductCard;
