// API Service Layer for Odoo Integration
// This service handles all API communications with the Odoo backend

const API_BASE_URL = 'https://noithat.erpcloud.vn/api';

// Session Management
export const SessionManager = {
  getSessionId(): string {
    let sessionId = localStorage.getItem('cart_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('cart_session_id', sessionId);
    }
    return sessionId;
  },

  clearSession(): void {
    localStorage.removeItem('cart_session_id');
  }
};

// Generic API Response Interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// API Request Helper
const apiRequest = async <T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  try {
    const sessionId = SessionManager.getSessionId();

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId,
        ...options.headers,
      },
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network error'
    };
  }
};

// Product API
export const ProductAPI = {
  async getProducts(page = 1, limit = 20, category?: string, search?: string) {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (category) params.append('category', category);
    if (search) params.append('search', search);

    return apiRequest(`/products?${params}`);
  },

  async getProduct(id: number) {
    return apiRequest(`/products/${id}`);
  },

  async getProductVariants(productId: number) {
    return apiRequest(`/products/${productId}/variants`);
  },

  async getCategories() {
    return apiRequest('/categories');
  }
};

// Cart API
export const CartAPI = {
  async getCart() {
    return apiRequest('/cart');
  },

  async addToCart(productId: number, variantId: number, quantity: number, attributes: any = {}) {
    return apiRequest('/cart/add', {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        variant_id: variantId,
        quantity,
        attributes
      })
    });
  },

  async updateCartItem(itemId: number, quantity: number) {
    return apiRequest('/cart/update', {
      method: 'PUT',
      body: JSON.stringify({
        item_id: itemId,
        quantity
      })
    });
  },

  async removeCartItem(itemId: number) {
    return apiRequest(`/cart/remove/${itemId}`, {
      method: 'DELETE'
    });
  },

  async clearCart() {
    return apiRequest('/cart/clear', {
      method: 'DELETE'
    });
  }
};

// Checkout API
export const CheckoutAPI = {
  async getPaymentMethods() {
    return apiRequest('/checkout/payment-methods');
  },

  async validateCheckout(customerData: any, voucherCode?: string) {
    const payload: any = { customer: customerData };
    if (voucherCode) payload.voucher_code = voucherCode;

    return apiRequest('/checkout/validate', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  },

  async createOrder(customerData: any, paymentMethod: string, voucherCode?: string, giftOptions?: any) {
    const payload: any = {
      customer: customerData,
      payment_method: paymentMethod
    };

    if (voucherCode) payload.voucher_code = voucherCode;
    if (giftOptions) payload.gift_options = giftOptions;

    return apiRequest('/checkout/create-order', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  }
};

// Order API
export const OrderAPI = {
  async trackOrders(email: string, phone?: string, orderNumber?: string) {
    const params = new URLSearchParams({ email });
    if (phone) params.append('phone', phone);
    if (orderNumber) params.append('order_number', orderNumber);

    return apiRequest(`/orders/track?${params}`);
  },

  async getOrder(orderId: number, email: string) {
    return apiRequest(`/orders/${orderId}?email=${email}`);
  },

  async cancelOrder(orderId: number, email: string, reason: string) {
    return apiRequest(`/orders/${orderId}/cancel`, {
      method: 'POST',
      body: JSON.stringify({
        email,
        reason
      })
    });
  }
};

// Voucher API
export const VoucherAPI = {
  async validateVoucher(code: string) {
    return apiRequest(`/vouchers/validate/${code}`);
  },

  async getAvailableVouchers(customerId?: number) {
    const params = customerId ? `?customer_id=${customerId}` : '';
    return apiRequest(`/vouchers/available${params}`);
  }
};

// Loyalty API (for lucky wheel, points, etc.)
export const LoyaltyAPI = {
  async spinLuckyWheel(email?: string) {
    const payload = email ? { email } : {};
    return apiRequest('/lucky-wheel/spin', {
      method: 'POST',
      body: JSON.stringify(payload)
    });
  },

  async getAvailablePrizes() {
    return apiRequest('/lucky-wheel/prizes');
  },

  async getSpinHistory(email?: string) {
    const params = email ? `?email=${email}` : '';
    return apiRequest(`/lucky-wheel/history${params}`);
  },

  async getCustomerPoints(email: string) {
    return apiRequest(`/loyalty/points?email=${email}`);
  },

  async redeemPoints(email: string, points: number, rewardId: number) {
    return apiRequest('/loyalty/redeem', {
      method: 'POST',
      body: JSON.stringify({
        email,
        points,
        reward_id: rewardId
      })
    });
  }
};

// Error Handler
export const handleApiError = (error: string | undefined, defaultMessage = 'An error occurred') => {
  console.error('API Error:', error);
  return error || defaultMessage;
};

// Type Definitions for API Responses
export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  attributes: ProductAttribute[];
  variants: ProductVariant[];
  in_stock: boolean;
  stock_quantity: number;
}

export interface ProductVariant {
  id: number;
  name: string;
  price: number;
  attributes: { [key: string]: string };
  in_stock: boolean;
  stock_quantity: number;
}

export interface ProductAttribute {
  id: number;
  name: string;
  values: AttributeValue[];
  display_type: 'radio' | 'select' | 'color' | 'checkbox';
}

export interface AttributeValue {
  id: number;
  name: string;
  value: string;
  extra_price?: number;
}

export interface CartItem {
  id: number;
  product_id: number;
  product_name: string;
  variant_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
  attributes: Array<{
    attribute_name: string;
    value_name: string;
    attribute_id: number;
    value_id: number;
  }>;
  images: string[];
  product_url: string;
}

export interface CartSummary {
  total_items: number;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency: string;
}

export interface Order {
  id: number;
  order_number: string;
  status: string;
  total_amount: number;
  currency: string;
  order_date: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  items: CartItem[];
  tracking_url: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  fee: number;
  bank_info?: {
    account_number: string;
    bank_name: string;
    account_holder: string;
    branch: string;
  };
}

export interface VoucherInfo {
  voucher_code: string;
  program_id: number;
  program_name: string;
  is_valid: boolean;
  active: boolean;
  reward_info: {
    discount_mode: string;
    discount_percent: number;
    discount_fixed: number;
    currency: string;
  };
  description: string;
}

// Export all APIs as a single object for convenience
export const API = {
  Product: ProductAPI,
  Cart: CartAPI,
  Checkout: CheckoutAPI,
  Order: OrderAPI,
  Voucher: VoucherAPI,
  Loyalty: LoyaltyAPI,
  Session: SessionManager,
  handleError: handleApiError
};

export default API;
