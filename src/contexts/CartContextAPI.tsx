import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { CartItem, VoucherType } from '@/types/cart';

// API Configuration
const API_BASE_URL = 'https://noithat.erpcloud.vn/api';

// Session Management
const getSessionId = (): string => {
  let sessionId = localStorage.getItem('cart_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('cart_session_id', sessionId);
  }
  return sessionId;
};

// API Response Types
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface CartApiResponse {
  cart_id: number;
  session_id: string;
  items: Array<{
    id: number;
    product_id: number;
    product_name: string;
    variant_id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
    currency: string;
    attributes: Array<{
      attribute_name: string;
      value_name: string;
      attribute_id: number;
      value_id: number;
    }>;
    images: string[];
    product_url: string;
  }>;
  summary: {
    total_items: number;
    subtotal: number;
    tax_amount: number;
    total_amount: number;
    currency: string;
  };
}

interface VoucherValidationResponse {
  voucher_code: string;
  program_id: number;
  program_name: string;
  is_valid: boolean;
  active: boolean;
  reward_info: {
    discount_mode: string;
    discount_percent: number;
    discount_fixed: number;
    currency: string;
  };
  description: string;
}

// Cart Context Interface
interface CartContextType {
  // Cart State
  cart: CartItem[];
  cartSummary: {
    totalItems: number;
    subtotal: number;
    taxAmount: number;
    totalAmount: number;
    currency: string;
  };
  isLoading: boolean;
  error: string | null;

  // Cart Actions
  addToCart: (productId: number, variantId: number, quantity: number, attributes?: any) => Promise<boolean>;
  removeFromCart: (itemId: number) => Promise<boolean>;
  updateQuantity: (itemId: number, quantity: number) => Promise<boolean>;
  clearCart: () => Promise<boolean>;
  refreshCart: () => Promise<void>;

  // Voucher Actions
  validateVoucher: (code: string) => Promise<VoucherValidationResponse | null>;
  appliedVoucher: VoucherType | null;
  setAppliedVoucher: (voucher: VoucherType | null) => void;

  // Checkout Actions
  createOrder: (customerData: any, paymentMethod: string, voucherCode?: string) => Promise<any>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

// API Helper Functions
const apiCall = async <T,>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> => {
  try {
    const sessionId = getSessionId();
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': sessionId,
        ...options.headers,
      },
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API call failed:', error);
    return { success: false, error: 'Network error' };
  }
};

// Convert API cart item to frontend cart item
const convertApiCartItem = (apiItem: any): CartItem => {
  return {
    id: apiItem.id,
    product: {
      id: apiItem.product_id,
      name: apiItem.product_name,
      price: apiItem.unit_price,
      imageUrl: apiItem.images[0] || '/placeholder-jewelry.jpg',
      category: 'Jewelry'
    },
    quantity: apiItem.quantity,
    attributes: apiItem.attributes.reduce((acc: any, attr: any) => {
      acc[attr.attribute_name] = attr.value_name;
      return acc;
    }, {})
  };
};

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cartSummary, setCartSummary] = useState({
    totalItems: 0,
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0,
    currency: 'USD'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [appliedVoucher, setAppliedVoucher] = useState<VoucherType | null>(null);

  // Load cart on component mount
  useEffect(() => {
    refreshCart();
  }, []);

  const refreshCart = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall<CartApiResponse>('/cart');
      
      if (response.success && response.data) {
        const apiCart = response.data;
        
        // Convert API items to frontend format
        const frontendItems = apiCart.items.map(convertApiCartItem);
        setCart(frontendItems);
        
        // Update cart summary
        setCartSummary({
          totalItems: apiCart.summary.total_items,
          subtotal: apiCart.summary.subtotal,
          taxAmount: apiCart.summary.tax_amount,
          totalAmount: apiCart.summary.total_amount,
          currency: apiCart.summary.currency
        });
      } else {
        setError(response.error || 'Failed to load cart');
      }
    } catch (err) {
      setError('Failed to load cart');
      console.error('Cart refresh error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addToCart = useCallback(async (
    productId: number, 
    variantId: number, 
    quantity: number, 
    attributes: any = {}
  ): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall('/cart/add', {
        method: 'POST',
        body: JSON.stringify({
          product_id: productId,
          variant_id: variantId,
          quantity,
          attributes
        })
      });

      if (response.success) {
        await refreshCart(); // Refresh cart to get updated data
        return true;
      } else {
        setError(response.error || 'Failed to add item to cart');
        return false;
      }
    } catch (err) {
      setError('Failed to add item to cart');
      console.error('Add to cart error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshCart]);

  const removeFromCart = useCallback(async (itemId: number): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall(`/cart/remove/${itemId}`, {
        method: 'DELETE'
      });

      if (response.success) {
        await refreshCart();
        return true;
      } else {
        setError(response.error || 'Failed to remove item');
        return false;
      }
    } catch (err) {
      setError('Failed to remove item');
      console.error('Remove from cart error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshCart]);

  const updateQuantity = useCallback(async (itemId: number, quantity: number): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall('/cart/update', {
        method: 'PUT',
        body: JSON.stringify({
          item_id: itemId,
          quantity
        })
      });

      if (response.success) {
        await refreshCart();
        return true;
      } else {
        setError(response.error || 'Failed to update quantity');
        return false;
      }
    } catch (err) {
      setError('Failed to update quantity');
      console.error('Update quantity error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshCart]);

  const clearCart = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiCall('/cart/clear', {
        method: 'DELETE'
      });

      if (response.success) {
        setCart([]);
        setCartSummary({
          totalItems: 0,
          subtotal: 0,
          taxAmount: 0,
          totalAmount: 0,
          currency: 'USD'
        });
        setAppliedVoucher(null);
        return true;
      } else {
        setError(response.error || 'Failed to clear cart');
        return false;
      }
    } catch (err) {
      setError('Failed to clear cart');
      console.error('Clear cart error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const validateVoucher = useCallback(async (code: string): Promise<VoucherValidationResponse | null> => {
    try {
      const response = await apiCall<VoucherValidationResponse>(`/vouchers/validate/${code}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        setError(response.error || 'Invalid voucher code');
        return null;
      }
    } catch (err) {
      setError('Failed to validate voucher');
      console.error('Voucher validation error:', err);
      return null;
    }
  }, []);

  const createOrder = useCallback(async (
    customerData: any, 
    paymentMethod: string, 
    voucherCode?: string
  ): Promise<any> => {
    setIsLoading(true);
    setError(null);

    try {
      const orderData: any = {
        customer: customerData,
        payment_method: paymentMethod
      };

      if (voucherCode) {
        orderData.voucher_code = voucherCode;
      }

      const response = await apiCall('/checkout/create-order', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      if (response.success) {
        // Clear cart after successful order
        await clearCart();
        return response.data;
      } else {
        setError(response.error || 'Failed to create order');
        return null;
      }
    } catch (err) {
      setError('Failed to create order');
      console.error('Create order error:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [clearCart]);

  const contextValue: CartContextType = {
    cart,
    cartSummary,
    isLoading,
    error,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    refreshCart,
    validateVoucher,
    appliedVoucher,
    setAppliedVoucher,
    createOrder
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
