import React, { createContext, useContext, useState, useEffect } from 'react';
import { CartItem } from '@/types/cart';
import API from '@/services/api';

interface CartContextType {
  cart: CartItem[];
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemId: number) => void;
  updateQuantity: (itemId: number, quantity: number) => void;
  clearCart: () => void;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>(() => {
    const savedCart = localStorage.getItem('flokaraCart');
    return savedCart ? JSON.parse(savedCart) : [];
  });

  useEffect(() => {
    localStorage.setItem('flokaraCart', JSON.stringify(cart));
  }, [cart]);

  const addToCart = async (item: CartItem) => {
    try {
      // Call real API to add to cart
      const response = await API.Cart.addToCart(
        item.variantId || item.id, // Use variantId if available, fallback to id
        item.quantity,
        item.attributes
      );

      if (response.success) {
        console.log('✅ Added to cart via API:', response.data);

        // Update local cart state
        setCart(prevCart => {
          const existingItem = prevCart.find(
            cartItem =>
              cartItem.id === item.id &&
              JSON.stringify(cartItem.attributes) === JSON.stringify(item.attributes)
          );

          if (existingItem) {
            return prevCart.map(cartItem =>
              cartItem.id === item.id &&
              JSON.stringify(cartItem.attributes) === JSON.stringify(item.attributes)
                ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
                : cartItem
            );
          }

          return [...prevCart, item];
        });
      } else {
        console.error('❌ Failed to add to cart:', response.error);
        throw new Error(response.error || 'Failed to add to cart');
      }
    } catch (error) {
      console.error('❌ Cart API error:', error);
      // Fallback to local cart only
      setCart(prevCart => {
        const existingItem = prevCart.find(
          cartItem =>
            cartItem.id === item.id &&
            JSON.stringify(cartItem.attributes) === JSON.stringify(item.attributes)
        );

        if (existingItem) {
          return prevCart.map(cartItem =>
            cartItem.id === item.id &&
            JSON.stringify(cartItem.attributes) === JSON.stringify(item.attributes)
              ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
              : cartItem
          );
        }

        return [...prevCart, item];
      });
    }
  };

  const removeFromCart = (itemId: number) => {
    setCart(prevCart => prevCart.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId: number, quantity: number) => {
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  const clearCart = () => {
    setCart([]);
  };

  return (
    <CartContext.Provider value={{ cart, addToCart, removeFromCart, updateQuantity, clearCart }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};