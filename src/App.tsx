import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { CartProvider } from "./contexts/CartContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import Index from "./pages/Index";
import ProductDetail from "./pages/ProductDetail";
import ProductDetailNew from "./pages/ProductDetailNew";
import CheckoutPage from "./pages/CheckoutPage";
import PaymentPolicy from "./pages/PaymentPolicy";
import ReturnPolicy from "./pages/ReturnPolicy";
import ShippingPolicy from "./pages/ShippingPolicy";
import ComplaintsPolicy from "./pages/ComplaintsPolicy";
import InspectionPolicy from "./pages/InspectionPolicy";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import AboutUs from "./pages/AboutUs";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import ScrollToTop from "./components/ScrollToTop";
import AdminChatConfig from "./pages/AdminChatConfig";
import AdminDashboard from "./pages/AdminDashboard";
import AdminProducts from "./pages/AdminProducts";
import AdminOrders from "./pages/AdminOrders";
import AdminCustomers from "./pages/AdminCustomers";
import AdminUsers from "./pages/AdminUsers";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <ThemeProvider>
        <CartProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <ScrollToTop />
          <div className="overflow-x-hidden w-full">
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/product/:productId" element={<ProductDetailNew />} />
            <Route path="/checkout" element={<CheckoutPage />} />
            <Route path="/chinh-sach-thanh-toan" element={<PaymentPolicy />} />
            <Route path="/chinh-sach-doi-tra" element={<ReturnPolicy />} />
            <Route path="/chinh-sach-van-chuyen-va-giao-nhan" element={<ShippingPolicy />} />
            <Route path="/chinh-sach-xu-ly-khieu-nai" element={<ComplaintsPolicy />} />
            <Route path="/chinh-sach-kiem-hang" element={<InspectionPolicy />} />
            <Route path="/chinh-sach-bao-mat" element={<PrivacyPolicy />} />
            <Route path="/gioi-thieu" element={<AboutUs />} />
            <Route path="/lien-he" element={<Contact />} />

            {/* Admin Routes */}
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            <Route path="/admin/products" element={<AdminProducts />} />
            <Route path="/admin/orders" element={<AdminOrders />} />
            <Route path="/admin/chat-config" element={<AdminChatConfig />} />
            <Route path="/admin/customers" element={<AdminCustomers />} />
            <Route path="/admin/users" element={<AdminUsers />} />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          </div>
          </BrowserRouter>
        </CartProvider>
      </ThemeProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
