
// Updated Cart Types for API Integration

export interface CartItem {
  id: number;
  variantId?: number; // For API integration
  product: {
    id: number;
    name: string;
    price: number;
    imageUrl: string;
    category?: string;
  };
  quantity: number;
  attributes: {
    [key: string]: string;
  };
}

// API Cart Item (from Odoo)
export interface ApiCartItem {
  id: number;
  product_id: number;
  product_name: string;
  variant_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
  attributes: Array<{
    attribute_name: string;
    value_name: string;
    attribute_id: number;
    value_id: number;
  }>;
  images: string[];
  product_url: string;
}

// Cart Summary
export interface CartSummary {
  total_items: number;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  currency: string;
}

// Voucher Types
export interface VoucherType {
  id: number;
  code: string;
  description: string;
  discount: number; // Either a percentage (if < 100) or a fixed amount
  condition: string;
  minOrderValue?: number;
  expiryDate?: string;
}

export interface VoucherValidation {
  voucher_code: string;
  program_id: number;
  program_name: string;
  is_valid: boolean;
  active: boolean;
  reward_info: {
    discount_mode: string;
    discount_percent: number;
    discount_fixed: number;
    currency: string;
  };
  description: string;
}

// Customer Data
export interface CustomerData {
  full_name: string;
  email: string;
  phone: string;
  address: string;
  province?: string;
  district?: string;
  ward?: string;
}

// Gift Options
export interface GiftOptions {
  is_gift: boolean;
  recipient_name?: string;
  recipient_gender?: 'male' | 'female';
  gift_message?: string;
}

// Order Data
export interface OrderData {
  id: number;
  order_number: string;
  status: string;
  total_amount: number;
  currency: string;
  order_date: string;
  estimated_delivery?: string;
  payment_method: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  items: ApiCartItem[];
  tracking_url: string;
}

// Payment Method
export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
  fee: number;
  bank_info?: {
    account_number: string;
    bank_name: string;
    account_holder: string;
    branch: string;
  };
}
