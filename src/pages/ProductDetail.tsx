import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ShoppingCart, ChevronLeft, ChevronRight, Maximize, Play, Loader2 } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";
import { useCart } from '../contexts/CartContext';
import API from '@/services/api';

// API Product Interface
interface ApiProduct {
  id: number;
  name: string;
  description: string;
  short_description?: string;
  price: number;
  currency: string;
  main_image?: string;
  image_variants: {
    thumbnail?: string;
    small?: string;
    medium?: string;
    large?: string;
    xlarge?: string;
  };
  images: Array<{
    id: number;
    name: string;
    sequence: number;
    thumbnail: string;
    small: string;
    medium: string;
    large: string;
    xlarge: string;
  }>;
  videos: Array<{
    id?: number;
    name: string;
    url: string;
    type: string;
    thumbnail?: string;
    sequence: number;
  }>;
  variants: Array<{
    id: number;
    name: string;
    price: number;
    sku: string;
    attributes: Array<{
      attribute_name: string;
      value_name: string;
      attribute_id: number;
      value_id: number;
    }>;
  }>;
  attributes: Array<{
    id: number;
    name: string;
    display_name: string;
    display_type: 'radio' | 'pills' | 'select' | 'color' | 'multi';
    values: Array<{
      id: number;
      name: string;
      display_name: string;
      html_color?: string;
      image_url?: string;
    }>;
  }>;
  uom_info: {
    base_uom: {
      id: number;
      name: string;
      display_name: string;
    };
    available_uoms: Array<{
      id: number;
      name: string;
      display_name: string;
      price: number;
      factor: number;
    }>;
  };
  categories: string[];
  in_stock: boolean;
  stock_quantity: number;
}

const ProductDetail = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addToCart } = useCart();

  // API State
  const [product, setProduct] = useState<ApiProduct | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Product selection state
  const [selectedAttributes, setSelectedAttributes] = useState<{[key: number]: number}>({});
  const [selectedUom, setSelectedUom] = useState<number | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [quantity, setQuantity] = useState(1);
  const [showStickyButton, setShowStickyButton] = useState(false);

  // Image gallery state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);

  // Load product from API
  useEffect(() => {
    if (productId) {
      loadProduct(parseInt(productId));
    }
  }, [productId]);

  const loadProduct = async (id: number) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🛍️ Loading product detail from API...', id);

      // Try API first, then direct fetch as fallback
      let response;
      try {
        response = await API.Product.getProduct(id);

        if (!response.success) {
          throw new Error('API returned unsuccessful response');
        }
      } catch (apiError) {
        console.log('🛍️ API.Product.getProduct failed, trying direct fetch...', apiError);

        try {
          const directResponse = await fetch(`https://noithat.erpcloud.vn/api/products/${id}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            mode: 'cors',
            credentials: 'omit',
          });

          if (directResponse.ok) {
            response = await directResponse.json();
            console.log('🛍️ Direct fetch SUCCESS! Got product:', response);
          } else {
            throw new Error(`Direct fetch failed: ${directResponse.status}`);
          }
        } catch (directError) {
          console.log('🛍️ Direct fetch also failed:', directError);
          throw directError;
        }
      }

      if (response && response.success && response.data) {
        const productData = response.data;
        setProduct(productData);

        // Set initial UOM to base UOM
        if (productData.uom_info?.base_uom) {
          setSelectedUom(productData.uom_info.base_uom.id);
          setCurrentPrice(productData.price);
        }

        console.log('🛍️ Product loaded successfully:', productData.name);

      } else {
        throw new Error('Product not found');
      }
    } catch (error) {
      console.error('Failed to load product:', error);
      setError('Không thể tải thông tin sản phẩm. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const getProductImages = () => {
    if (!product) return [];

    const images = [];

    // Add main image first
    if (product.main_image) {
      images.push(`https://noithat.erpcloud.vn${product.main_image}`);
    }

    // Add gallery images
    if (product.images && product.images.length > 0) {
      product.images.forEach(img => {
        const imageUrl = `https://noithat.erpcloud.vn${img.large || img.medium || img.small}`;
        if (!images.includes(imageUrl)) {
          images.push(imageUrl);
        }
      });
    }

    // Fallback if no images
    if (images.length === 0) {
      images.push('/placeholder.jpg');
    }

    return images;
  };

  const getProductVideos = () => {
    if (!product || !product.videos) return [];

    return product.videos.filter(video => video.url && video.url !== '').map(video => ({
      ...video,
      thumbnail: video.thumbnail || `https://img.youtube.com/vi/${video.url.split('v=')[1]}/maxresdefault.jpg`
    }));
  };

  const handleAttributeChange = (attributeId: number, valueId: number) => {
    const newSelectedAttributes = {
      ...selectedAttributes,
      [attributeId]: valueId
    };
    setSelectedAttributes(newSelectedAttributes);

    // Find matching variant
    const matchingVariant = product?.variants.find(variant => {
      return variant.attributes.every(attr =>
        newSelectedAttributes[attr.attribute_id] === attr.value_id
      );
    });

    if (matchingVariant) {
      setSelectedVariant(matchingVariant);
      updatePrice(matchingVariant.price);
    }
  };

  const handleUomChange = (uomId: number) => {
    setSelectedUom(uomId);

    const selectedUomData = product?.uom_info.available_uoms.find(uom => uom.id === uomId);
    if (selectedUomData) {
      updatePrice(selectedUomData.price);
    }
  };

  const updatePrice = (basePrice: number) => {
    setCurrentPrice(basePrice);
  };

  // Get current images and videos
  const productImages = getProductImages();
  const productVideos = getProductVideos();

  // Loading state
  if (loading) {
    return (
      <>
        <Navigation />
        <div className="container mx-auto px-4 py-12">
          <div className="bg-white rounded-3xl shadow-xl overflow-hidden p-8">
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <Loader2 className="w-12 h-12 animate-spin text-flokara-teal mx-auto mb-4" />
                <p className="text-gray-600">Đang tải thông tin sản phẩm...</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <>
        <Navigation />
        <div className="container mx-auto px-4 py-12">
          <div className="bg-white rounded-3xl shadow-xl overflow-hidden p-8">
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Không thể tải sản phẩm</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => loadProduct(parseInt(productId || '1'))}>
                Thử lại
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }
    }

    // Fallback to main image if images array is empty or undefined
    if (product.imageUrl) {
      return [product.imageUrl];
    }

    // Ultimate fallback - placeholder images
    return [
      "https://images.unsplash.com/photo-1487530811176-3780de880c2d?q=80&w=1600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1469259943454-aa100abba749?q=80&w=1600&auto=format&fit=crop",
      "https://images.unsplash.com/photo-1566576721346-d4a3b4eaeb55?q=80&w=1600&auto=format&fit=crop"
    ];
  })();



  // Calculate price based on selected size for each jewelry product
  const getPriceBySize = (productId: number, size: string) => {
    if (productId === 1) {
      // Mặt dây chuyền vàng 14K
      switch (size) {
        case 'Dây chuyền tiêu chuẩn': return 5783000;
        case 'Combo dây chuyền + hộp quà': return 5983000;
        default: return 5783000;
      }
    } else if (productId === 2) {
      // Bông tai bạc
      switch (size) {
        case 'Size tiêu chuẩn': return 695000;
        case 'Size lớn': return 795000;
        default: return 695000;
      }
    } else if (productId === 3) {
      // Nhẫn bạc - giá giống nhau cho tất cả size
      switch (size) {
        case 'Size 8': return 416000;
        case 'Size 10': return 416000;
        case 'Size 12': return 416000;
        case 'Size 14': return 416000;
        default: return 416000;
      }
    } else if (productId === 4) {
      // Mặt dây chuyền bạc
      switch (size) {
        case 'Size tiêu chuẩn': return 302000;
        case 'Size dài': return 352000;
        default: return 302000;
      }
    } else if (productId === 5) {
      // Bông tai vàng Disney
      switch (size) {
        case 'Size tiêu chuẩn': return 10000800;
        case 'Size lớn': return 11200800;
        default: return 10000800;
      }
    } else if (productId === 6) {
      // Mặt dây chuyền vàng 10K
      switch (size) {
        case 'Size tiêu chuẩn': return 4056000;
        case 'Size dài': return 4456000;
        default: return 4056000;
      }
    }
    return product.price;
  };

  const currentPrice = selectedSize ? getPriceBySize(product.id, selectedSize) : product.price;
  const totalPrice = currentPrice * quantity;

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setShowStickyButton(scrollPosition > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Set default size selection when component loads
  useEffect(() => {
    if (product.id === 1) {
      setSelectedSize('Dây chuyền tiêu chuẩn');
    } else if (product.id === 2) {
      setSelectedSize('Size tiêu chuẩn');
    } else if (product.id === 3) {
      setSelectedSize('Size 10');
    } else if (product.id === 4) {
      setSelectedSize('Size tiêu chuẩn');
    } else if (product.id === 5) {
      setSelectedSize('Size tiêu chuẩn');
    } else if (product.id === 6) {
      setSelectedSize('Size tiêu chuẩn');
    }
  }, [product.id]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  const handleBuyNow = () => {
    if (!selectedSize) {
      toast({
        title: "Vui lòng chọn size sản phẩm",
        description: "Bạn cần chọn size sản phẩm trước khi thêm vào giỏ hàng",
        variant: "destructive",
        duration: 3000
      });
      return;
    }

    const cartItem = {
      id: Date.now(), // Unique ID for cart item
      product: {
        id: product.id,
        name: product.name,
        price: currentPrice,
        imageUrl: productImages[currentImageIndex] // Use current image from productImages array
      },
      quantity: quantity,
      attributes: {
        material: selectedColor,
        size: selectedSize
      }
    };

    // Add to cart and ensure it's saved to localStorage
    addToCart(cartItem);

    // Show toast notification
    toast({
      title: "Đã thêm vào giỏ hàng",
      description: `${product.name} - ${selectedColor}, ${selectedSize} - Số lượng: ${quantity}`,
      duration: 3000
    });

    // Force a small delay to ensure localStorage is updated before navigation
    setTimeout(() => {
      navigate('/checkout');
    }, 100);
  };

  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => prev > 1 ? prev - 1 : 1);

  const handleSelectImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <>
      <Navigation />
      <div className="container mx-auto px-4 py-12">
        <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
          <div className="grid md:grid-cols-2 gap-8 p-8">
            {/* Enhanced Product Images Section - Now Taking More Space */}
            <div className="space-y-6">


              <div className="relative bg-gray-50 rounded-2xl overflow-hidden h-[500px]">
                {productImages[currentImageIndex] ? (
                  <img
                    src={productImages[currentImageIndex]}
                    alt={product.name}
                    className="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      // Fallback to placeholder if image fails to load
                      e.currentTarget.src = "https://images.unsplash.com/photo-1487530811176-3780de880c2d?q=80&w=1600&auto=format&fit=crop";
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                    <p className="text-gray-500">No image available</p>
                  </div>
                )}
                <button
                  onClick={() => setShowFullscreenImage(true)}
                  className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                >
                  <Maximize className="h-5 w-5 text-gray-700" />
                </button>

                <div className="absolute inset-0 flex items-center justify-between px-4">
                  <button
                    onClick={() => setCurrentImageIndex(prev => prev > 0 ? prev - 1 : productImages.length - 1)}
                    className="bg-white/80 rounded-full p-2 hover:bg-white transition shadow-md"
                  >
                    <ChevronLeft className="h-6 w-6" />
                  </button>
                  <button
                    onClick={() => setCurrentImageIndex(prev => prev < productImages.length - 1 ? prev + 1 : 0)}
                    className="bg-white/80 rounded-full p-2 hover:bg-white transition shadow-md"
                  >
                    <ChevronRight className="h-6 w-6" />
                  </button>
                </div>

                <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                  {productImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => handleSelectImage(index)}
                      className={`w-3 h-3 rounded-full transition-all ${
                        index === currentImageIndex ? 'bg-flokara-teal scale-125' : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-4 gap-4">
                {productImages.map((img, index) => (
                  <div
                    key={index}
                    className={`bg-gray-50 rounded-xl overflow-hidden h-24 cursor-pointer hover:opacity-80 transition-opacity ${
                      index === currentImageIndex ? 'ring-2 ring-flokara-teal' : ''
                    }`}
                    onClick={() => handleSelectImage(index)}
                  >
                    <img
                      src={img}
                      alt={`${product.name} view ${index + 1}`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = "https://images.unsplash.com/photo-1487530811176-3780de880c2d?q=80&w=1600&auto=format&fit=crop";
                      }}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              {product.isHot && (
                <span className="inline-block bg-flokara-orange text-white text-xs font-bold uppercase py-2 px-3 rounded-full mb-2">
                  Hot
                </span>
              )}

              <h1 className="text-3xl md:text-4xl font-bold text-flokara-teal">{product.name}</h1>

              <div className="space-y-2">
                <div className="flex items-center gap-4">
                  <span className="text-2xl font-bold text-flokara-orange">
                    {formatPrice(currentPrice)}
                  </span>
                  {selectedSize && (
                    <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {selectedSize}
                    </span>
                  )}
                </div>
                {quantity > 1 && (
                  <div className="flex items-center gap-2">
                    <span className="text-lg text-gray-600">
                      Tổng cộng ({quantity} sản phẩm):
                    </span>
                    <span className="text-2xl font-bold text-flokara-teal">
                      {formatPrice(totalPrice)}
                    </span>
                  </div>
                )}
                {product.id === 1 || product.id === 2 || product.id === 3 || product.id === 4 || product.id === 5 || product.id === 6 ? (
                  <div className="flex items-center gap-2">
                    <span className="text-lg text-gray-500 line-through">
                      {formatPrice(Math.round(currentPrice * 1.15))}
                    </span>
                    <span className="bg-flokara-orange/10 text-flokara-orange px-2 py-1 rounded text-sm font-medium">
                      -15%
                    </span>
                  </div>
                ) : null}
              </div>

              <p className="text-gray-700 text-lg">{product.description}</p>

              {/* Material/Type Selection */}
              {product.id === 1 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Chọn chất liệu</h3>
                  <div className="flex flex-wrap gap-3">
                    {['Vàng trắng 14K', 'Vàng vàng 14K', 'Vàng hồng 14K'].map((type) => (
                      <button
                        key={type}
                        onClick={() => setSelectedColor(type)}
                        className={`px-4 py-2 rounded-full border ${
                          selectedColor === type
                            ? 'border-flokara-orange bg-flokara-orange text-white'
                            : 'border-gray-300 hover:border-flokara-orange'
                        }`}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Material Selection for Jewelry */}
              {(product.id === 2 || product.id === 3 || product.id === 4 || product.id === 5 || product.id === 6) && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Chọn chất liệu</h3>
                  <div className="flex flex-wrap gap-3">
                    {(() => {
                      if (product.id === 2 || product.id === 3 || product.id === 4) {
                        return ['Bạc 925', 'Bạc 950', 'Bạc mạ vàng'];
                      } else if (product.id === 5) {
                        return ['Vàng trắng 14K', 'Vàng vàng 14K', 'Vàng hồng 14K'];
                      } else if (product.id === 6) {
                        return ['Vàng trắng 10K', 'Vàng vàng 10K', 'Vàng hồng 10K'];
                      }
                      return [];
                    })().map((type) => (
                      <button
                        key={type}
                        onClick={() => setSelectedColor(type)}
                        className={`px-4 py-2 rounded-full border ${
                          selectedColor === type
                            ? 'border-flokara-orange bg-flokara-orange text-white'
                            : 'border-gray-300 hover:border-flokara-orange'
                        }`}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Size/Type Selection */}
              {product.id === 1 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Chọn loại sản phẩm</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { size: 'Dây chuyền tiêu chuẩn', price: '5,783,000₫', height: 'Bao gồm mặt dây chuyền' },
                      { size: 'Combo dây chuyền + hộp quà', price: '5,983,000₫', height: 'Kèm hộp quà cao cấp' }
                    ].map((option) => (
                      <button
                        key={option.size}
                        onClick={() => setSelectedSize(option.size)}
                        className={`p-4 border rounded-lg text-left ${
                          selectedSize === option.size
                            ? 'border-flokara-orange bg-flokara-orange/5'
                            : 'border-gray-300 hover:border-flokara-orange'
                        }`}
                      >
                        <h4 className="font-semibold text-lg mb-2">{option.size}</h4>
                        <p className="text-gray-600">{option.height}</p>
                        <p className="text-flokara-orange font-bold">{option.price}</p>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Size Selection for Jewelry */}
              {(product.id === 2 || product.id === 3 || product.id === 4 || product.id === 5 || product.id === 6) && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Chọn size</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {(() => {
                      if (product.id === 2) {
                        return [
                          { size: 'Size tiêu chuẩn', price: '695,000₫', height: 'Phù hợp mọi tai' },
                          { size: 'Size lớn', price: '795,000₫', height: 'Cho tai to' }
                        ];
                      } else if (product.id === 3) {
                        return [
                          { size: 'Size 8', price: '416,000₫', height: 'Nhỏ' },
                          { size: 'Size 10', price: '416,000₫', height: 'Trung bình' },
                          { size: 'Size 12', price: '416,000₫', height: 'Lớn' },
                          { size: 'Size 14', price: '416,000₫', height: 'Rất lớn' }
                        ];
                      } else if (product.id === 4) {
                        return [
                          { size: 'Size tiêu chuẩn', price: '302,000₫', height: 'Dây chuyền 40-45cm' },
                          { size: 'Size dài', price: '352,000₫', height: 'Dây chuyền 50-55cm' }
                        ];
                      } else if (product.id === 5) {
                        return [
                          { size: 'Size tiêu chuẩn', price: '10,000,800₫', height: 'Phù hợp mọi tai' },
                          { size: 'Size lớn', price: '11,200,800₫', height: 'Cho tai to' }
                        ];
                      } else if (product.id === 6) {
                        return [
                          { size: 'Size tiêu chuẩn', price: '4,056,000₫', height: 'Dây chuyền 40-45cm' },
                          { size: 'Size dài', price: '4,456,000₫', height: 'Dây chuyền 50-55cm' }
                        ];
                      }
                      return [];
                    })().map((option) => (
                      <button
                        key={option.size}
                        onClick={() => setSelectedSize(option.size)}
                        className={`p-4 border rounded-lg text-left ${
                          selectedSize === option.size
                            ? 'border-flokara-orange bg-flokara-orange/5'
                            : 'border-gray-300 hover:border-flokara-orange'
                        }`}
                      >
                        <h4 className="font-semibold text-lg mb-2">{option.size}</h4>
                        <p className="text-gray-600">{option.height}</p>
                        <p className="text-flokara-orange font-bold">{option.price}</p>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <div className="space-y-4 pt-4 border-t border-gray-100">
                <h3 className="font-semibold text-xl text-flokara-teal">Số lượng</h3>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={decreaseQuantity}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-xl font-bold hover:bg-gray-100 transition-colors"
                  >
                    -
                  </button>
                  <span className="text-xl font-medium">{quantity}</span>
                  <button
                    onClick={increaseQuantity}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-xl font-bold hover:bg-gray-100 transition-colors"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="pt-6 space-x-4">
                <Button
                  onClick={handleBuyNow}
                  className="bg-flokara-orange hover:bg-flokara-lightOrange text-white px-8 py-7 rounded-xl text-lg font-bold transition-all duration-300 hover:shadow-lg hover:shadow-flokara-orange/20 transform hover:-translate-y-1"
                >
                  <ShoppingCart className="mr-2" size={22} />
                  Mua Ngay
                </Button>
              </div>
            </div>
          </div>

          {/* Product Additional Information with Rich Content */}
          <div className="p-8 bg-gray-50 border-t border-gray-100">
            <h2 className="text-2xl font-bold text-flokara-teal mb-4">Thông tin sản phẩm</h2>
            <div className="prose max-w-none text-gray-700">
              {product.id === 6 || product.id === 1 || product.id === 2 || product.id === 3 || product.id === 4 || product.id === 5 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold text-flokara-teal">Thông tin chi tiết</h3>
                      <div className="space-y-2">
                        <p><span className="font-medium">SKU:</span> {product.id === 1 ? 'GMTPXMW000445' : product.id === 2 ? 'SBXMXMW000076' : product.id === 3 ? 'SNXM00K000116' : product.id === 4 ? 'SMXMXMK000048' : product.id === 5 ? 'GBNPXMW000006' : 'GMSPXMW000224'}</p>
                        <p><span className="font-medium">Xuất xứ:</span> Việt Nam</p>
                        <p><span className="font-medium">Thương hiệu:</span> {product.id === 1 || product.id === 5 || product.id === 6 ? 'PNJ' : 'PNJSilver'}</p>
                        <p><span className="font-medium">Loại sản phẩm:</span> {product.id === 1 || product.id === 4 || product.id === 6 ? 'Mặt dây chuyền' : product.id === 2 || product.id === 5 ? 'Bông tai' : 'Nhẫn'}</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold text-flokara-teal">Chất liệu có sẵn</h3>
                      <div className="grid grid-cols-1 gap-4">
                        {(() => {
                          if (product.id === 1) {
                            return (
                              <>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-300"></div>
                                  <span>Vàng trắng 14K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-yellow-400"></div>
                                  <span>Vàng vàng 14K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-pink-400"></div>
                                  <span>Vàng hồng 14K</span>
                                </div>
                              </>
                            );
                          } else if (product.id === 2 || product.id === 3 || product.id === 4) {
                            return (
                              <>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-300"></div>
                                  <span>Bạc 925</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-400"></div>
                                  <span>Bạc 950</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-yellow-300"></div>
                                  <span>Bạc mạ vàng</span>
                                </div>
                              </>
                            );
                          } else if (product.id === 5) {
                            return (
                              <>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-300"></div>
                                  <span>Vàng trắng 14K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-yellow-400"></div>
                                  <span>Vàng vàng 14K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-pink-400"></div>
                                  <span>Vàng hồng 14K</span>
                                </div>
                              </>
                            );
                          } else if (product.id === 6) {
                            return (
                              <>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-gray-300"></div>
                                  <span>Vàng trắng 10K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-yellow-400"></div>
                                  <span>Vàng vàng 10K</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 rounded-full bg-pink-400"></div>
                                  <span>Vàng hồng 10K</span>
                                </div>
                              </>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6 mb-6">
                    <h3 className="text-xl font-semibold text-flokara-teal">Size có sẵn</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(() => {
                        if (product.id === 1) {
                          return [
                            { weight: 'Dây chuyền tiêu chuẩn', price: '5,783,000₫' },
                            { weight: 'Combo dây chuyền + hộp quà', price: '5,983,000₫' }
                          ];
                        } else if (product.id === 2) {
                          return [
                            { weight: 'Size tiêu chuẩn', price: '695,000₫' },
                            { weight: 'Size lớn', price: '795,000₫' }
                          ];
                        } else if (product.id === 3) {
                          return [
                            { weight: 'Size 8', price: '416,000₫' },
                            { weight: 'Size 10', price: '416,000₫' },
                            { weight: 'Size 12', price: '416,000₫' },
                            { weight: 'Size 14', price: '416,000₫' }
                          ];
                        } else if (product.id === 4) {
                          return [
                            { weight: 'Size tiêu chuẩn', price: '302,000₫' },
                            { weight: 'Size dài', price: '352,000₫' }
                          ];
                        } else if (product.id === 5) {
                          return [
                            { weight: 'Size tiêu chuẩn', price: '10,000,800₫' },
                            { weight: 'Size lớn', price: '11,200,800₫' }
                          ];
                        } else if (product.id === 6) {
                          return [
                            { weight: 'Size tiêu chuẩn', price: '4,056,000₫' },
                            { weight: 'Size dài', price: '4,456,000₫' }
                          ];
                        }
                        return [];
                      })().map((option, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <h4 className="font-semibold text-lg mb-2">{option.weight}</h4>
                          <p className="text-flokara-orange font-bold">{option.price}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-flokara-teal">Hình ảnh sản phẩm</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      {product.images && product.images.length > 0 ? (
                        product.images.map((img, index) => (
                          <img
                            key={index}
                            src={img}
                            alt={`${product.name} - Ảnh ${index + 1}`}
                            className="rounded-lg w-full h-[400px] object-cover hover:scale-105 transition-transform duration-300"
                          />
                        ))
                      ) : (
                        <div className="col-span-2 text-center py-8">
                          <p className="text-gray-500">Chưa có hình ảnh sản phẩm</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-8 space-y-4">
                    <h3 className="text-xl font-semibold text-flokara-teal">Mô tả sản phẩm</h3>
                    <div
                      className="prose max-w-none"
                      dangerouslySetInnerHTML={{ __html: product.richDescription || '' }}
                    />
                  </div>
                </>
              ) : (
                <>
                  <p className="mb-4">
                    {product.name} được chọn lọc từ những hạt gạo tốt nhất, được chế biến tỉ mỉ theo quy trình khép kín.
                    Mỗi sản phẩm đều được chăm chút đến từng chi tiết để mang đến một sản phẩm hoàn hảo.
                  </p>

                  <div className="my-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <img
                      src="/flokara_uploads/gao-mam-gaba-st25-hop-2kg_1.webp"
                      alt="Gạo ST25 cao cấp"
                      className="rounded-lg w-full h-64 object-cover"
                    />
                    <img
                      src="/flokara_uploads/gao-ong-cua-st-tim-than_1.webp"
                      alt="Gạo tím than dinh dưỡng"
                      className="rounded-lg w-full h-64 object-cover"
                    />
                  </div>

                  <p className="mb-4">
                    Gạo cao cấp cùng với chất lượng vượt trội sẽ là lựa chọn hoàn hảo dành cho gia đình, bạn bè hay đối tác
                    của bạn trong những bữa cơm hàng ngày, hay đơn giản là để thể hiện sự quan tâm đến sức khỏe.
                  </p>
                  <p>
                    Chúng tôi cam kết mang đến cho quý khách hàng những sản phẩm gạo chất lượng cao,
                    dịch vụ giao hàng nhanh chóng và trải nghiệm mua sắm tuyệt vời.
                  </p>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Fullscreen Image Viewer */}
      <Dialog open={showFullscreenImage} onOpenChange={setShowFullscreenImage}>
        <DialogContent className="max-w-screen-xl w-[95vw] h-[90vh] p-0 bg-black/95">
          <DialogClose className="absolute right-4 top-4 z-10 rounded-full bg-white/20 p-2 hover:bg-white/40 transition">
            <Maximize className="h-5 w-5 text-white" />
          </DialogClose>

          <div className="w-full h-full flex items-center justify-center">
            <Carousel className="w-full max-w-5xl">
              <CarouselContent>
                {productImages.map((img, idx) => (
                  <CarouselItem key={idx}>
                    <div className="flex items-center justify-center h-[80vh] p-2">
                      <img
                        src={img}
                        alt={`${product.name} - view ${idx + 1}`}
                        className="max-h-full max-w-full object-contain"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-4" />
              <CarouselNext className="right-4" />
            </Carousel>
          </div>
        </DialogContent>
      </Dialog>

      {/* Sticky Buy Button */}
      {showStickyButton && (
        <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 md:px-8 z-50 transition-all duration-300 transform translate-y-0">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <img src={productImages[currentImageIndex]} alt={product.name} className="w-12 h-12 object-cover rounded-lg" />
              <div>
                <p className="font-medium text-gray-800 line-clamp-1">{product.name}</p>
                <p className="font-bold text-flokara-orange">{formatPrice(currentPrice)}</p>
              </div>
            </div>
            <Button
              onClick={handleBuyNow}
              className="bg-flokara-orange hover:bg-flokara-lightOrange text-white px-8 py-5 rounded-xl text-lg font-bold transition-all duration-300 hover:shadow-lg hover:shadow-flokara-orange/20"
            >
              <ShoppingCart className="mr-2" size={18} />
              Mua Ngay
            </Button>
          </div>
        </div>
      )}

      <Footer />
    </>
  );
};

export default ProductDetail;
