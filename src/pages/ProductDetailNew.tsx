import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ShoppingCart, ChevronLeft, ChevronRight, Maximize, Play, Loader2, Star } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { useCart } from '../contexts/CartContext';
import API from '@/services/api';

// API Product Interface
interface ApiProduct {
  id: number;
  name: string;
  description: string;
  short_description?: string;
  price: number;
  currency: string;
  main_image?: string;
  image_variants: {
    thumbnail?: string;
    small?: string;
    medium?: string;
    large?: string;
    xlarge?: string;
  };
  images: Array<{
    id: number;
    name: string;
    sequence: number;
    thumbnail: string;
    small: string;
    medium: string;
    large: string;
    xlarge: string;
  }>;
  videos: Array<{
    id?: number;
    name: string;
    url: string;
    type: string;
    thumbnail?: string;
    sequence: number;
  }>;
  variants: Array<{
    id: number;
    name: string;
    price: number;
    sku: string;
    attributes: Array<{
      attribute_name: string;
      value_name: string;
      attribute_id: number;
      value_id: number;
    }>;
  }>;
  attributes: Array<{
    id: number;
    name: string;
    display_name: string;
    display_type: 'radio' | 'pills' | 'select' | 'color' | 'multi';
    values: Array<{
      id: number;
      name: string;
      display_name: string;
      html_color?: string;
      image_url?: string;
    }>;
  }>;
  uom_info: {
    base_uom: {
      id: number;
      name: string;
      display_name: string;
    };
    available_uoms: Array<{
      id: number;
      name: string;
      display_name: string;
      price: number;
      factor: number;
    }>;
  };
  categories: string[];
  in_stock: boolean;
  stock_quantity: number;
}

const ProductDetailNew = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addToCart } = useCart();

  // API State
  const [product, setProduct] = useState<ApiProduct | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Product selection state
  const [selectedAttributes, setSelectedAttributes] = useState<{[key: number]: number}>({});
  const [selectedUom, setSelectedUom] = useState<number | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(0);
  const [quantity, setQuantity] = useState(1);

  // Image gallery state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);

  // Load product from API
  useEffect(() => {
    if (productId) {
      loadProduct(parseInt(productId));
    }
  }, [productId]);

  const loadProduct = async (id: number) => {
    try {
      setLoading(true);
      setError(null);

      console.log('🛍️ Loading product detail from API...', id);

      // Try API first, then direct fetch as fallback
      let response;
      try {
        response = await API.Product.getProduct(id);

        if (!response.success) {
          throw new Error('API returned unsuccessful response');
        }
      } catch (apiError) {
        console.log('🛍️ API.Product.getProduct failed, trying direct fetch...', apiError);

        try {
          const directResponse = await fetch(`https://noithat.erpcloud.vn/api/products/${id}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            mode: 'cors',
            credentials: 'omit',
          });

          if (directResponse.ok) {
            response = await directResponse.json();
            console.log('🛍️ Direct fetch SUCCESS! Got product:', response);
          } else {
            throw new Error(`Direct fetch failed: ${directResponse.status}`);
          }
        } catch (directError) {
          console.log('🛍️ Direct fetch also failed:', directError);
          throw directError;
        }
      }

      if (response && response.success && response.data) {
        const productData = response.data;
        setProduct(productData);

        // Set initial UOM to base UOM
        if (productData.uom_info?.base_uom) {
          setSelectedUom(productData.uom_info.base_uom.id);
          setCurrentPrice(productData.price);
        }

        console.log('🛍️ Product loaded successfully:', productData.name);

      } else {
        throw new Error('Product not found');
      }
    } catch (error) {
      console.error('Failed to load product:', error);
      setError('Không thể tải thông tin sản phẩm. Vui lòng thử lại sau.');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const getProductImages = () => {
    if (!product) return [];

    const images = [];

    // Add main image first
    if (product.main_image) {
      images.push(`https://noithat.erpcloud.vn${product.main_image}`);
    }

    // Add gallery images
    if (product.images && product.images.length > 0) {
      product.images.forEach(img => {
        const imageUrl = `https://noithat.erpcloud.vn${img.large || img.medium || img.small}`;
        if (!images.includes(imageUrl)) {
          images.push(imageUrl);
        }
      });
    }

    // Fallback if no images
    if (images.length === 0) {
      images.push('/placeholder.jpg');
    }

    return images;
  };

  const getProductVideos = () => {
    if (!product || !product.videos) return [];

    return product.videos.filter(video => video.url && video.url !== '').map(video => ({
      ...video,
      thumbnail: video.thumbnail || `https://img.youtube.com/vi/${video.url.split('v=')[1]}/maxresdefault.jpg`
    }));
  };

  const handleAttributeChange = (attributeId: number, valueId: number) => {
    const newSelectedAttributes = {
      ...selectedAttributes,
      [attributeId]: valueId
    };
    setSelectedAttributes(newSelectedAttributes);

    // Find matching variant
    const matchingVariant = product?.variants.find(variant => {
      return variant.attributes.every(attr =>
        newSelectedAttributes[attr.attribute_id] === attr.value_id
      );
    });

    if (matchingVariant) {
      setSelectedVariant(matchingVariant);
      updatePrice(matchingVariant.price);
    }
  };

  const handleUomChange = (uomId: number) => {
    setSelectedUom(uomId);

    const selectedUomData = product?.uom_info.available_uoms.find(uom => uom.id === uomId);
    if (selectedUomData) {
      updatePrice(selectedUomData.price);
    }
  };

  const updatePrice = (basePrice: number) => {
    setCurrentPrice(basePrice);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get current images and videos
  const productImages = getProductImages();
  const productVideos = getProductVideos();

  // Loading state
  if (loading) {
    return (
      <>
        <Navigation />
        <div className="container mx-auto px-4 py-12">
          <div className="bg-white rounded-3xl shadow-xl overflow-hidden p-8">
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <Loader2 className="w-12 h-12 animate-spin text-flokara-teal mx-auto mb-4" />
                <p className="text-gray-600">Đang tải thông tin sản phẩm...</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <>
        <Navigation />
        <div className="container mx-auto px-4 py-12">
          <div className="bg-white rounded-3xl shadow-xl overflow-hidden p-8">
            <div className="text-center py-12">
              <div className="text-red-500 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Không thể tải sản phẩm</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => loadProduct(parseInt(productId || '1'))}>
                Thử lại
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Navigation />
      <div className="container mx-auto px-4 py-12">
        <div className="bg-white rounded-3xl shadow-xl overflow-hidden">
          <div className="grid md:grid-cols-2 gap-8 p-8">
            {/* Product Images Section */}
            <div className="space-y-6">
              {/* Main Image */}
              <div className="relative bg-gray-50 rounded-2xl overflow-hidden h-[500px]">
                {productImages[currentImageIndex] ? (
                  <img
                    src={productImages[currentImageIndex]}
                    alt={product.name}
                    className="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-500"
                    onError={(e) => {
                      e.currentTarget.src = '/placeholder.jpg';
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                    <p className="text-gray-500">Không có hình ảnh</p>
                  </div>
                )}

                <button
                  onClick={() => setShowImageModal(true)}
                  className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                >
                  <Maximize className="h-5 w-5 text-gray-700" />
                </button>

                {/* Navigation arrows */}
                {productImages.length > 1 && (
                  <div className="absolute inset-0 flex items-center justify-between px-4">
                    <button
                      onClick={() => setCurrentImageIndex(prev => prev > 0 ? prev - 1 : productImages.length - 1)}
                      className="bg-white/80 rounded-full p-2 hover:bg-white transition shadow-md"
                    >
                      <ChevronLeft className="h-6 w-6" />
                    </button>
                    <button
                      onClick={() => setCurrentImageIndex(prev => prev < productImages.length - 1 ? prev + 1 : 0)}
                      className="bg-white/80 rounded-full p-2 hover:bg-white transition shadow-md"
                    >
                      <ChevronRight className="h-6 w-6" />
                    </button>
                  </div>
                )}

                {/* Image indicators */}
                {productImages.length > 1 && (
                  <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                    {productImages.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all ${
                          index === currentImageIndex ? 'bg-flokara-teal scale-125' : 'bg-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Thumbnail Gallery */}
              {productImages.length > 1 && (
                <div className="grid grid-cols-4 gap-4">
                  {productImages.map((img, index) => (
                    <div
                      key={index}
                      className={`bg-gray-50 rounded-xl overflow-hidden h-24 cursor-pointer hover:opacity-80 transition-opacity ${
                        index === currentImageIndex ? 'ring-2 ring-flokara-teal' : ''
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <img
                        src={img}
                        alt={`${product.name} view ${index + 1}`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.jpg';
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* Videos Section */}
              {productVideos.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Video sản phẩm</h3>
                  <div className="grid grid-cols-2 gap-4">
                    {productVideos.map((video, index) => (
                      <div key={index} className="relative bg-gray-100 rounded-lg overflow-hidden aspect-video">
                        <img
                          src={video.thumbnail}
                          alt={video.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <button
                            onClick={() => window.open(video.url, '_blank')}
                            className="bg-black/50 rounded-full p-3 hover:bg-black/70 transition-colors"
                          >
                            <Play className="w-6 h-6 text-white" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Product Details Section */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-flokara-teal mb-2">{product.name}</h1>
                {product.short_description && (
                  <p className="text-gray-600 text-lg">{product.short_description}</p>
                )}
              </div>

              {/* Price Section */}
              <div className="space-y-2">
                <div className="flex items-center gap-4">
                  <span className="text-3xl font-bold text-flokara-orange">
                    {formatPrice(currentPrice)}
                  </span>
                  {selectedUom && product.uom_info.available_uoms.find(uom => uom.id === selectedUom) && (
                    <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      /{product.uom_info.available_uoms.find(uom => uom.id === selectedUom)?.display_name}
                    </span>
                  )}
                </div>

                {/* Stock status */}
                <div className="flex items-center gap-2">
                  <div className={`w-3 h-3 rounded-full ${product.in_stock ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`text-sm ${product.in_stock ? 'text-green-600' : 'text-red-600'}`}>
                    {product.in_stock ? `Còn hàng (${product.stock_quantity})` : 'Hết hàng'}
                  </span>
                </div>
              </div>

              {/* UOM Selection */}
              {product.uom_info.available_uoms.length > 1 && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-gray-900">Đơn vị tính</h3>
                  <Select value={selectedUom?.toString()} onValueChange={(value) => handleUomChange(parseInt(value))}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Chọn đơn vị tính" />
                    </SelectTrigger>
                    <SelectContent>
                      {product.uom_info.available_uoms.map((uom) => (
                        <SelectItem key={uom.id} value={uom.id.toString()}>
                          <div className="flex justify-between items-center w-full">
                            <span>{uom.display_name}</span>
                            <span className="ml-4 font-semibold text-flokara-orange">
                              {formatPrice(uom.price)}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Product Attributes */}
              {product.attributes && product.attributes.length > 0 && (
                <div className="space-y-6">
                  {product.attributes.map((attribute) => (
                    <div key={attribute.id} className="space-y-3">
                      <h3 className="text-lg font-semibold text-gray-900">{attribute.display_name}</h3>

                      {/* Radio Display */}
                      {attribute.display_type === 'radio' && (
                        <RadioGroup
                          value={selectedAttributes[attribute.id]?.toString()}
                          onValueChange={(value) => handleAttributeChange(attribute.id, parseInt(value))}
                        >
                          <div className="grid grid-cols-1 gap-3">
                            {attribute.values.map((value) => (
                              <div key={value.id} className="flex items-center space-x-2">
                                <RadioGroupItem value={value.id.toString()} id={`attr-${attribute.id}-${value.id}`} />
                                <Label htmlFor={`attr-${attribute.id}-${value.id}`} className="cursor-pointer">
                                  {value.display_name}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </RadioGroup>
                      )}

                      {/* Pills Display */}
                      {attribute.display_type === 'pills' && (
                        <div className="flex flex-wrap gap-3">
                          {attribute.values.map((value) => (
                            <button
                              key={value.id}
                              onClick={() => handleAttributeChange(attribute.id, value.id)}
                              className={`px-4 py-2 rounded-full border transition-all ${
                                selectedAttributes[attribute.id] === value.id
                                  ? 'border-flokara-orange bg-flokara-orange text-white'
                                  : 'border-gray-300 hover:border-flokara-orange'
                              }`}
                            >
                              {value.name}
                            </button>
                          ))}
                        </div>
                      )}

                      {/* Select Dropdown */}
                      {attribute.display_type === 'select' && (
                        <Select
                          value={selectedAttributes[attribute.id]?.toString()}
                          onValueChange={(value) => handleAttributeChange(attribute.id, parseInt(value))}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={`Chọn ${attribute.name.toLowerCase()}`} />
                          </SelectTrigger>
                          <SelectContent>
                            {attribute.values.map((value) => (
                              <SelectItem key={value.id} value={value.id.toString()}>
                                {value.display_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}

                      {/* Color Display */}
                      {attribute.display_type === 'color' && (
                        <div className="flex flex-wrap gap-3">
                          {attribute.values.map((value) => (
                            <button
                              key={value.id}
                              onClick={() => handleAttributeChange(attribute.id, value.id)}
                              className={`w-12 h-12 rounded-full border-2 transition-all ${
                                selectedAttributes[attribute.id] === value.id
                                  ? 'border-flokara-orange scale-110'
                                  : 'border-gray-300 hover:border-gray-400'
                              }`}
                              style={{ backgroundColor: value.html_color || '#ccc' }}
                              title={value.display_name}
                            />
                          ))}
                        </div>
                      )}

                      {/* Multi-checkbox Display */}
                      {attribute.display_type === 'multi' && (
                        <div className="space-y-3">
                          {attribute.values.map((value) => (
                            <div key={value.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`multi-${attribute.id}-${value.id}`}
                                checked={selectedAttributes[attribute.id] === value.id}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    handleAttributeChange(attribute.id, value.id);
                                  }
                                }}
                              />
                              <Label htmlFor={`multi-${attribute.id}-${value.id}`} className="cursor-pointer">
                                {value.display_name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Quantity Selection */}
              <div className="space-y-4 pt-4 border-t border-gray-100">
                <h3 className="font-semibold text-xl text-flokara-teal">Số lượng</h3>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setQuantity(prev => prev > 1 ? prev - 1 : 1)}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-xl font-bold hover:bg-gray-100 transition-colors"
                  >
                    -
                  </button>
                  <span className="text-xl font-medium min-w-[3rem] text-center">{quantity}</span>
                  <button
                    onClick={() => setQuantity(prev => prev + 1)}
                    className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center text-xl font-bold hover:bg-gray-100 transition-colors"
                  >
                    +
                  </button>
                </div>

                {/* Total Price */}
                {quantity > 1 && (
                  <div className="flex items-center gap-2">
                    <span className="text-lg text-gray-600">
                      Tổng cộng ({quantity} sản phẩm):
                    </span>
                    <span className="text-2xl font-bold text-flokara-teal">
                      {formatPrice(currentPrice * quantity)}
                    </span>
                  </div>
                )}
              </div>

              {/* Add to Cart Button */}
              <div className="pt-6 space-y-4">
                <Button
                  onClick={() => {
                    // Add to cart logic here
                    toast({
                      title: "Đã thêm vào giỏ hàng",
                      description: `${product.name} - Số lượng: ${quantity}`,
                      duration: 3000
                    });
                  }}
                  disabled={!product.in_stock}
                  className="w-full bg-flokara-orange hover:bg-flokara-lightOrange text-white px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 hover:shadow-lg hover:shadow-flokara-orange/20 transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShoppingCart className="mr-2" size={22} />
                  {product.in_stock ? 'Thêm vào giỏ hàng' : 'Hết hàng'}
                </Button>

                <Button
                  onClick={() => navigate('/checkout')}
                  disabled={!product.in_stock}
                  variant="outline"
                  className="w-full border-flokara-teal text-flokara-teal hover:bg-flokara-teal hover:text-white px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300"
                >
                  Mua ngay
                </Button>
              </div>
            </div>
          </div>

          {/* Product Description */}
          <div className="p-8 bg-gray-50 border-t border-gray-100">
            <h2 className="text-2xl font-bold text-flokara-teal mb-6">Thông tin sản phẩm</h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Description */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900">Mô tả sản phẩm</h3>
                <div className="prose max-w-none text-gray-700">
                  <p>{product.description}</p>
                </div>
              </div>

              {/* Product Info */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-gray-900">Thông tin chi tiết</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Danh mục:</span>
                    <span>{product.categories.join(', ')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Tình trạng:</span>
                    <span className={product.in_stock ? 'text-green-600' : 'text-red-600'}>
                      {product.in_stock ? 'Còn hàng' : 'Hết hàng'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Số lượng:</span>
                    <span>{product.stock_quantity}</span>
                  </div>
                  {selectedVariant && (
                    <div className="flex justify-between">
                      <span className="font-medium">SKU:</span>
                      <span>{selectedVariant.sku}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      <Dialog open={showImageModal} onOpenChange={setShowImageModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <div className="relative">
            <img
              src={productImages[currentImageIndex]}
              alt={product.name}
              className="w-full h-auto max-h-[80vh] object-contain"
            />
            <DialogClose className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md">
              ×
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      <Footer />
    </>
  );
};

export default ProductDetailNew;
