#!/bin/bash

# 🚀 Quick Test for Cart & Checkout APIs

echo "🧪 Quick API Test Starting..."

# Test 1: Get empty cart
echo "1️⃣ Testing empty cart..."
curl -H "X-Session-ID: quick123" "https://noithat.erpcloud.vn/api/cart"
echo -e "\n"

# Test 2: Add product to cart
echo "2️⃣ Adding product to cart..."
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: quick123" \
  -d '{"product_id": 13, "variant_id": 23, "quantity": 1}'
echo -e "\n"

# Test 3: Get cart with items
echo "3️⃣ Getting cart with items..."
curl -H "X-Session-ID: quick123" "https://noithat.erpcloud.vn/api/cart"
echo -e "\n"

# Test 4: Get payment methods
echo "4️⃣ Getting payment methods..."
curl "https://noithat.erpcloud.vn/api/checkout/payment-methods"
echo -e "\n"

# Test 5: Create order
echo "5️⃣ Creating order..."
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: quick123" \
  -d '{
    "customer": {
      "full_name": "Test User",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Test Address"
    },
    "payment_method": "cod"
  }'
echo -e "\n"

echo "✅ Quick test completed!"
