# 🧪 MANUAL TESTING GUIDE - Jewelry E-commerce

## 🚨 **API SERVER STATUS**
**Current Issue:** API server appears to be slow/unresponsive during automated testing.
**Recommendation:** Perform manual testing when server is stable.

---

## 🎯 **TESTING CHECKLIST**

### **📦 1. PRODUCTS API TESTING**

#### **Basic Products:**
```bash
# Test 1: Get all products
curl "https://noithat.erpcloud.vn/api/products"
# Expected: 6+ products (original + enhanced)

# Test 2: Get specific product with variants
curl "https://noithat.erpcloud.vn/api/products/9"
# Expected: <PERSON><PERSON><PERSON> chuyền with size variants (14,16,17)

# Test 3: Check product attributes
curl "https://noithat.erpcloud.vn/api/products/9" | jq '.data.attributes'
# Expected: Kích thước attribute with multiple values
```

#### **Enhanced Products (After Module Upgrade):**
```bash
# Test 4: Look for enhanced products
curl "https://noithat.erpcloud.vn/api/products" | jq '.data.products[] | select(.name | contains("Enhanced") or contains("Premium"))'
# Expected: 5 enhanced products with multiple attributes

# Test 5: Check enhanced product variants
curl "https://noithat.erpcloud.vn/api/products/[ENHANCED_ID]" | jq '.data.variants | length'
# Expected: Multiple variants (2-16 depending on product)
```

### **🏷️ 2. CATEGORIES API TESTING**

```bash
# Test 6: Get all categories
curl "https://noithat.erpcloud.vn/api/categories"
# Expected: 4 categories (Nhẫn, Dây chuyền, Bông tai, Lắc tay)

# Test 7: Check category structure
curl "https://noithat.erpcloud.vn/api/categories" | jq '.data.categories[] | {id, name, parent_id}'
# Expected: Proper category hierarchy
```

### **🎰 3. LUCKY WHEEL TESTING**

#### **Promotions API:**
```bash
# Test 8: Get Lucky Wheel promotions
curl "https://noithat.erpcloud.vn/api/lucky-wheel/promotions"
# Expected: 13 active promotions with different types

# Test 9: Check promotion details
curl "https://noithat.erpcloud.vn/api/lucky-wheel/promotions" | jq '.data.promotions[] | {name, program_type, lucky_wheel_probability, lucky_wheel_icon}'
# Expected: Variety of program types with icons and probabilities
```

#### **Spin API:**
```bash
# Test 10: Lucky Wheel spin
curl -X POST "https://noithat.erpcloud.vn/api/lucky-wheel/spin" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_session_123" \
  -d '{"email": "<EMAIL>"}'
# Expected: Random prize selection with voucher code
```

### **🛒 4. CART TESTING**

#### **Empty Cart:**
```bash
# Test 11: Get empty cart
curl "https://noithat.erpcloud.vn/api/cart/get" \
  -H "X-Session-ID: test_cart_session"
# Expected: Empty cart structure
```

#### **Add Products:**
```bash
# Test 12: Add simple product
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_cart_session" \
  -d '{
    "product_id": 7,
    "quantity": 1
  }'
# Expected: Product added successfully

# Test 13: Add product variant
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_cart_session" \
  -d '{
    "product_id": 44,
    "quantity": 2
  }'
# Expected: Variant added with correct attributes
```

#### **Cart Operations:**
```bash
# Test 14: Update cart item
curl -X PUT "https://noithat.erpcloud.vn/api/cart/update" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_cart_session" \
  -d '{
    "line_id": 1,
    "quantity": 3
  }'
# Expected: Quantity updated, total recalculated

# Test 15: Remove cart item
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/remove/1" \
  -H "X-Session-ID: test_cart_session"
# Expected: Item removed from cart
```

### **🎟️ 5. VOUCHER TESTING**

#### **Sample Voucher Codes:**
```bash
# Test 16: Validate coupon
curl "https://noithat.erpcloud.vn/api/vouchers/validate/SAVE15-001"
# Expected: Valid 15% discount coupon

# Test 17: Validate gift card
curl "https://noithat.erpcloud.vn/api/vouchers/validate/GIFT100K-001"
# Expected: Valid gift card with 100k balance

# Test 18: Validate promo code
curl "https://noithat.erpcloud.vn/api/vouchers/validate/RING50-001"
# Expected: Valid 50k fixed discount

# Test 19: Validate test voucher
curl "https://noithat.erpcloud.vn/api/vouchers/validate/TEST-COUPON-15"
# Expected: Valid test coupon
```

### **💳 6. CHECKOUT TESTING**

#### **Create Order:**
```bash
# Test 20: Checkout without voucher
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_cart_session" \
  -d '{
    "customer_info": {
      "full_name": "Test Customer",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Test Street, Test City"
    },
    "payment_method": "cod",
    "notes": "Test order"
  }'
# Expected: Order created successfully

# Test 21: Checkout with voucher
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: test_cart_session_2" \
  -d '{
    "customer_info": {
      "full_name": "VIP Customer",
      "email": "<EMAIL>",
      "phone": "**********",
      "address": "456 VIP Avenue, Luxury District"
    },
    "voucher_code": "SAVE15-001",
    "payment_method": "bank_transfer",
    "notes": "Order with 15% discount"
  }'
# Expected: Order with discount applied
```

---

## 🎯 **EXPECTED RESULTS**

### **✅ Success Criteria:**

#### **Products API:**
- **6+ products** returned
- **Product variants** with attributes
- **Enhanced products** after module upgrade
- **Proper attribute structure**

#### **Lucky Wheel API:**
- **13 active promotions** 
- **8 different program types**
- **Valid icons and probabilities**
- **Random prize selection**
- **Voucher code generation**

#### **Cart API:**
- **Empty cart** initialization
- **Add/update/remove** operations
- **Price calculations** correct
- **Session management** working

#### **Voucher API:**
- **Sample vouchers** validate correctly
- **Different discount types** recognized
- **Usage tracking** functional

#### **Checkout API:**
- **Order creation** successful
- **Customer data** saved
- **Voucher discounts** applied
- **Payment methods** accepted

### **❌ Failure Indicators:**
- **API timeouts** or no response
- **Empty product lists**
- **Missing voucher codes**
- **Cart operations fail**
- **Checkout errors**

---

## 🔧 **TROUBLESHOOTING**

### **🚨 Common Issues:**

#### **API Slow/Unresponsive:**
```bash
# Check server status
curl -I "https://noithat.erpcloud.vn/api/products"
# Should return 200 OK quickly

# Check if module is properly installed
# Go to Odoo backend → Apps → jewelry_ecommerce → Check status
```

#### **Missing Enhanced Products:**
```bash
# Upgrade module to load new data
# Odoo backend → Apps → jewelry_ecommerce → Upgrade
```

#### **Voucher Codes Not Found:**
```bash
# Check loyalty cards in backend
# Loyalty → Programs → Check sample programs
# Loyalty → Cards → Check sample cards
```

#### **Cart Session Issues:**
```bash
# Use consistent X-Session-ID header
# Clear browser cache/cookies
# Try different session ID
```

---

## 📊 **PERFORMANCE BENCHMARKS**

### **⚡ Expected Response Times:**
- **Products API:** < 500ms
- **Lucky Wheel API:** < 300ms  
- **Cart Operations:** < 200ms
- **Voucher Validation:** < 100ms
- **Checkout:** < 1000ms

### **📈 Load Testing:**
```bash
# Test concurrent requests
for i in {1..5}; do
  curl "https://noithat.erpcloud.vn/api/products" &
done
wait
# All should complete successfully
```

---

## 🎯 **NEXT STEPS**

### **When Server is Stable:**
1. **Run automated test script:** `./test_jewelry_ecommerce.sh`
2. **Check all manual tests** above
3. **Verify enhanced products** are loaded
4. **Test complete user journey**
5. **Performance optimization** if needed

### **Production Readiness:**
- **All tests pass** ✅
- **Response times** acceptable ✅  
- **Error handling** robust ✅
- **Data consistency** maintained ✅
- **Security** validated ✅

**🎯 Complete testing ensures jewelry e-commerce system is production-ready! 💎✨**
