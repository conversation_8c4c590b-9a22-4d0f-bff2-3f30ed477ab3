# 🎰 WEBSITE INTEGRATION TEST GUIDE

## ✅ **FIXED: framer-motion dependency removed**

### **🔧 Changes Made:**
- **Removed** framer-motion import
- **Replaced** motion components với regular divs
- **Added** CSS transitions for animations
- **Fixed** component structure và positioning

---

## 🚀 **WEBSITE TESTING INSTRUCTIONS**

### **📋 Prerequisites:**
1. **Start development server:** `npm run dev`
2. **API server stable:** Check `https://noithat.erpcloud.vn/api/products`
3. **Browser ready:** Chrome/Safari với developer tools

---

## 🖥️ **DESKTOP TESTING FLOW**

### **STEP 1: Navigate to Lucky Wheel**
```
1. Open website in browser
2. Click "Ưu đãi" in navigation menu
3. Scroll to Lucky Wheel section
4. Verify wheel is visible
```

### **STEP 2: Test Lucky Wheel**
```
1. Click "🎰 QUAY NGAY" button
2. Enter email in prompt: "<EMAIL>"
3. Verify Lucky Wheel modal opens
4. Click "QUAY" button in center
5. Wait for spin animation (3 seconds)
6. Verify prize result displays
7. Check voucher code is shown
8. Close modal
```

### **STEP 3: Test Product Purchase**
```
1. Browse products on homepage
2. Click "Đặt hàng ngay" on any product
3. Verify product added to cart
4. Navigate to checkout page
5. Verify Lucky Wheel voucher auto-applied
6. Fill customer information
7. Submit order
8. Verify success message với order number
```

---

## 📱 **MOBILE TESTING FLOW**

### **STEP 1: Switch to Mobile View**
```
1. Open browser developer tools (F12)
2. Toggle device toolbar (mobile view)
3. Set width to 375px (iPhone size)
4. Refresh page
5. Verify mobile layout loads
```

### **STEP 2: Test Mobile Lucky Wheel**
```
1. Scroll to empty cart section
2. Click "Lucky Wheel" button
3. Enter email: "<EMAIL>"
4. Click "Bắt đầu quay"
5. Verify wheel interface loads
6. Tap "QUAY" button
7. Wait for result
8. Verify voucher saved
```

### **STEP 3: Test Mobile Purchase**
```
1. Browse products in mobile list
2. Add products to cart
3. Click cart button at bottom
4. Proceed to checkout
5. Verify voucher applied
6. Complete order
7. Check success
```

---

## 🔍 **VERIFICATION POINTS**

### **✅ Lucky Wheel Success:**
- [ ] Modal opens without errors
- [ ] Email input works
- [ ] Wheel spins smoothly
- [ ] Prize result displays
- [ ] Voucher code generated
- [ ] Voucher saved to localStorage

### **✅ Cart Integration:**
- [ ] Products add to cart
- [ ] API calls successful (check Network tab)
- [ ] Cart totals calculate correctly
- [ ] Session maintained

### **✅ Checkout Success:**
- [ ] Voucher auto-applied
- [ ] Discount calculated correctly
- [ ] Order creation successful
- [ ] Real order number returned
- [ ] Order visible in Odoo

---

## 🐛 **TROUBLESHOOTING**

### **❌ Lucky Wheel Not Loading:**
```
Check browser console for errors:
- API timeout → Wait for server stability
- Component error → Check component imports
- Network error → Verify API endpoints
```

### **❌ Cart Not Working:**
```
Check Network tab in developer tools:
- 404 errors → API endpoints incorrect
- 500 errors → Server issues
- Timeout → API server slow
```

### **❌ Checkout Fails:**
```
Common issues:
- Missing customer info → Check form validation
- Invalid voucher → Check voucher validation
- API error → Check server logs
```

---

## 📊 **EXPECTED RESULTS**

### **🎰 Lucky Wheel:**
```json
{
  "success": true,
  "data": {
    "prize": {
      "name": "Phiếu giảm 15% - SAVE15",
      "lucky_wheel_icon": "🎫"
    },
    "voucher_code": "044a-1234-5678",
    "message": "Congratulations! You won..."
  }
}
```

### **🛒 Cart Addition:**
```json
{
  "success": true,
  "message": "Product added to cart",
  "data": {
    "cart_id": 1,
    "total_items": 1.0,
    "total_amount": 14317511.5
  }
}
```

### **💳 Order Creation:**
```json
{
  "success": true,
  "data": {
    "order_id": 3,
    "order_number": "S00003",
    "total_amount": 12169885.5,
    "items": [...],
    "customer": {...}
  }
}
```

---

## 🎯 **SUCCESS CRITERIA**

### **✅ Complete Flow Working:**
1. **Lucky Wheel** → Spin → Get voucher
2. **Product Browse** → Add to cart → API success
3. **Checkout** → Apply voucher → Create order
4. **Order Success** → Real order number → Visible in Odoo

### **✅ User Experience:**
- **Smooth animations** without framer-motion
- **Responsive design** on mobile/desktop
- **Clear feedback** for all actions
- **Error handling** for API failures

### **✅ Technical Integration:**
- **Real API calls** to Odoo backend
- **Session management** across requests
- **Voucher system** working end-to-end
- **Order persistence** in database

---

## 🚀 **READY FOR PRODUCTION**

### **When all tests pass:**
- ✅ Lucky Wheel integration complete
- ✅ Cart API working
- ✅ Checkout flow successful
- ✅ Orders created in Odoo
- ✅ Mobile/desktop responsive
- ✅ No dependency errors

**🎉 Website integration complete và ready for live testing! 🎰💎✨**
