<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- ========================================= -->
        <!-- SAMPLE LOYALTY CARDS FOR TESTING -->
        <!-- ========================================= -->
        
        <!-- Coupon Cards -->
        <record id="loyalty_card_coupon_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">SAVE15-001</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_coupon_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">SAVE15-002</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_coupon_3" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">SAVE15-003</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- Gift Cards -->
        <record id="loyalty_card_gift_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">GIFT100K-001</field>
            <field name="points">100000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_gift_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">GIFT100K-002</field>
            <field name="points">100000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_gift_3" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">GIFT50K-001</field>
            <field name="points">50000</field>
            <field name="active">True</field>
        </record>

        <!-- eWallet Cards -->
        <record id="loyalty_card_ewallet_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="code">EWALLET30K-001</field>
            <field name="points">30000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_ewallet_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="code">EWALLET50K-001</field>
            <field name="points">50000</field>
            <field name="active">True</field>
        </record>

        <!-- Promo Code Cards -->
        <record id="loyalty_card_promo_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="code">RING50-001</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_promo_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="code">RING50-002</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- Next Order Coupons -->
        <record id="loyalty_card_next_order_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="code">NEXT20-001</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_next_order_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="code">NEXT20-002</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- Buy X Get Y Cards -->
        <record id="loyalty_card_buy_x_get_y_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_buy_x_get_y_sample"/>
            <field name="code">BUY2GET1-001</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- ========================================= -->
        <!-- LUCKY WHEEL SPECIFIC CARDS -->
        <!-- ========================================= -->
        
        <!-- Lucky Wheel Coupon Cards với format đặc biệt -->
        <record id="loyalty_card_lucky_coupon_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">044a-1234-5678</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_coupon_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">044b-2345-6789</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_coupon_3" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">044c-3456-7890</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- Lucky Wheel Gift Cards -->
        <record id="loyalty_card_lucky_gift_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">044d-4567-8901</field>
            <field name="points">100000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_gift_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">044e-5678-9012</field>
            <field name="points">50000</field>
            <field name="active">True</field>
        </record>

        <!-- Lucky Wheel eWallet Cards -->
        <record id="loyalty_card_lucky_ewallet_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="code">044f-6789-0123</field>
            <field name="points">30000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_ewallet_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="code">044g-7890-1234</field>
            <field name="points">20000</field>
            <field name="active">True</field>
        </record>

        <!-- Lucky Wheel Promo Code Cards -->
        <record id="loyalty_card_lucky_promo_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="code">044h-8901-2345</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_promo_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="code">044i-9012-3456</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- Lucky Wheel Next Order Cards -->
        <record id="loyalty_card_lucky_next_1" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="code">044j-0123-4567</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_lucky_next_2" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="code">044k-1234-5678</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <!-- ========================================= -->
        <!-- TEST CARDS FOR DEVELOPMENT -->
        <!-- ========================================= -->
        
        <!-- Test Cards với codes dễ nhớ -->
        <record id="loyalty_card_test_coupon" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="code">TEST-COUPON-15</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_test_gift" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="code">TEST-GIFT-100K</field>
            <field name="points">100000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_test_ewallet" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="code">TEST-EWALLET-30K</field>
            <field name="points">30000</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_test_promo" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="code">TEST-RING50</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

        <record id="loyalty_card_test_next" model="loyalty.card">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="code">TEST-NEXT-20</field>
            <field name="points">0</field>
            <field name="active">True</field>
        </record>

    </data>
</odoo>
