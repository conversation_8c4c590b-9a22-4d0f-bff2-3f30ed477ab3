<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- ========================================= -->
        <!-- ENHANCED JEWELRY ATTRIBUTES BASED ON API ANALYSIS -->
        <!-- ========================================= -->
        
        <!-- Size Attribute (already exists as ID 1 with values 14,16,17) -->
        <!-- We'll add more size values to existing attribute -->
        <record id="product_attribute_value_size_12" model="product.attribute.value">
            <field name="name">12</field>
            <field name="attribute_id">1</field>  <!-- Existing Kích thước attribute -->
            <field name="sequence">0</field>
        </record>

        <record id="product_attribute_value_size_13" model="product.attribute.value">
            <field name="name">13</field>
            <field name="attribute_id">1</field>
            <field name="sequence">1</field>
        </record>

        <record id="product_attribute_value_size_15" model="product.attribute.value">
            <field name="name">15</field>
            <field name="attribute_id">1</field>
            <field name="sequence">4</field>
        </record>

        <record id="product_attribute_value_size_18" model="product.attribute.value">
            <field name="name">18</field>
            <field name="attribute_id">1</field>
            <field name="sequence">5</field>
        </record>

        <record id="product_attribute_value_size_19" model="product.attribute.value">
            <field name="name">19</field>
            <field name="attribute_id">1</field>
            <field name="sequence">6</field>
        </record>

        <!-- Material Attribute -->
        <record id="product_attribute_material" model="product.attribute">
            <field name="name">Chất liệu</field>
            <field name="display_type">radio</field>
            <field name="create_variant">always</field>
            <field name="sequence">2</field>
        </record>

        <record id="product_attribute_value_gold_18k" model="product.attribute.value">
            <field name="name">Vàng 18K</field>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="sequence">1</field>
        </record>

        <record id="product_attribute_value_gold_14k" model="product.attribute.value">
            <field name="name">Vàng 14K</field>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="sequence">2</field>
        </record>

        <record id="product_attribute_value_white_gold_14k" model="product.attribute.value">
            <field name="name">Vàng trắng 14K</field>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="sequence">3</field>
        </record>

        <record id="product_attribute_value_silver_925" model="product.attribute.value">
            <field name="name">Bạc 925</field>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="sequence">4</field>
        </record>

        <record id="product_attribute_value_steel" model="product.attribute.value">
            <field name="name">Thép không gỉ</field>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="sequence">5</field>
        </record>

        <!-- Gemstone Attribute -->
        <record id="product_attribute_gemstone" model="product.attribute">
            <field name="name">Đá quý</field>
            <field name="display_type">color</field>
            <field name="create_variant">dynamic</field>
            <field name="sequence">3</field>
        </record>

        <record id="product_attribute_value_diamond" model="product.attribute.value">
            <field name="name">Kim cương</field>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="html_color">#FFFFFF</field>
            <field name="sequence">1</field>
        </record>

        <record id="product_attribute_value_topaz" model="product.attribute.value">
            <field name="name">Topaz</field>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="html_color">#FFC87C</field>
            <field name="sequence">2</field>
        </record>

        <record id="product_attribute_value_sapphire" model="product.attribute.value">
            <field name="name">Sapphire</field>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="html_color">#0F52BA</field>
            <field name="sequence">3</field>
        </record>

        <record id="product_attribute_value_synthetic" model="product.attribute.value">
            <field name="name">Đá tổng hợp</field>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="html_color">#9370DB</field>
            <field name="sequence">4</field>
        </record>

        <record id="product_attribute_value_no_stone" model="product.attribute.value">
            <field name="name">Không đá</field>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="html_color">#D3D3D3</field>
            <field name="sequence">5</field>
        </record>

        <!-- ========================================= -->
        <!-- ENHANCED PRODUCT TEMPLATES WITH VARIANTS -->
        <!-- ========================================= -->
        
        <!-- Enhanced Diamond Earrings (ID 7 from API) -->
        <record id="enhanced_diamond_earrings" model="product.template">
            <field name="name">Bông tai Kim cương Vàng trắng 14K PNJ Premium</field>
            <field name="description">Bông tai kim cương vàng trắng 14K với thiết kế hiện đại, tôn lên vẻ đẹp quyến rũ. Sản phẩm cao cấp với chất lượng kim cương tuyệt hảo.</field>
            <field name="short_description">Bông tai kim cương cao cấp</field>
            <field name="default_code">DDDDW060140-ENH</field>
            <field name="list_price">15750000</field>
            <field name="standard_price">12000000</field>
            <field name="categ_id">3</field>  <!-- Bông tai category -->
            <field name="type">product</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">True</field>
            <field name="website_published">True</field>
            <field name="weight">0.008</field>
        </record>

        <!-- Add attributes to enhanced diamond earrings -->
        <record id="enhanced_earrings_material_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_diamond_earrings"/>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_white_gold_14k'), ref('product_attribute_value_gold_14k')])]</field>
        </record>

        <record id="enhanced_earrings_gemstone_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_diamond_earrings"/>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_diamond')])]</field>
        </record>

        <!-- Enhanced Gold Necklace with Size Variants (ID 9 from API) -->
        <record id="enhanced_gold_necklace" model="product.template">
            <field name="name">Dây chuyền Vàng 18K PNJ Đa kích thước</field>
            <field name="description">Dây chuyền vàng 18K thiết kế cổ điển, có nhiều kích thước phù hợp cho mọi lứa tuổi và phong cách.</field>
            <field name="short_description">Dây chuyền vàng 18K đa size</field>
            <field name="default_code">00000GC000947-ENH</field>
            <field name="list_price">12450000</field>
            <field name="standard_price">9000000</field>
            <field name="categ_id">2</field>  <!-- Dây chuyền category -->
            <field name="type">product</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">True</field>
            <field name="website_published">True</field>
            <field name="weight">0.015</field>
        </record>

        <!-- Add size attribute to enhanced necklace -->
        <record id="enhanced_necklace_size_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_gold_necklace"/>
            <field name="attribute_id">1</field>  <!-- Existing Kích thước attribute -->
            <field name="value_ids">[(6, 0, [1, 2, 3, ref('product_attribute_value_size_15'), ref('product_attribute_value_size_18')])]</field>  <!-- 14,16,17,15,18 -->
        </record>

        <record id="enhanced_necklace_material_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_gold_necklace"/>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_gold_18k')])]</field>
        </record>

        <!-- Enhanced Wedding Ring -->
        <record id="enhanced_wedding_ring" model="product.template">
            <field name="name">Nhẫn cưới Vàng 18K PNJ Đa size</field>
            <field name="description">Nhẫn cưới vàng 18K thiết kế truyền thống, biểu tượng của tình yêu vĩnh cửu. Có đầy đủ size từ 12-19.</field>
            <field name="short_description">Nhẫn cưới vàng 18K đa size</field>
            <field name="default_code">RING18K00613-ENH</field>
            <field name="list_price">8950000</field>
            <field name="standard_price">6500000</field>
            <field name="categ_id">1</field>  <!-- Nhẫn category -->
            <field name="type">product</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">True</field>
            <field name="website_published">True</field>
            <field name="weight">0.012</field>
        </record>

        <!-- Add size attribute to enhanced wedding ring -->
        <record id="enhanced_ring_size_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_wedding_ring"/>
            <field name="attribute_id">1</field>  <!-- Existing Kích thước attribute -->
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_size_12'), ref('product_attribute_value_size_13'), 1, 2, ref('product_attribute_value_size_15'), 3, ref('product_attribute_value_size_18'), ref('product_attribute_value_size_19')])]</field>  <!-- 12,13,14,16,15,17,18,19 -->
        </record>

        <record id="enhanced_ring_material_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_wedding_ring"/>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_gold_18k'), ref('product_attribute_value_white_gold_14k')])]</field>
        </record>

        <!-- Enhanced Silver Bracelet -->
        <record id="enhanced_silver_bracelet" model="product.template">
            <field name="name">Lắc tay Bạc 925 đính đá PNJ Multi-material</field>
            <field name="description">Lắc tay bạc 925 đính đá với thiết kế trẻ trung, có thể chọn chất liệu và loại đá khác nhau.</field>
            <field name="short_description">Lắc tay bạc đa chất liệu</field>
            <field name="default_code">SXMXW000168-ENH</field>
            <field name="list_price">2890000</field>
            <field name="standard_price">2000000</field>
            <field name="categ_id">4</field>  <!-- Lắc tay category -->
            <field name="type">product</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">True</field>
            <field name="website_published">True</field>
            <field name="weight">0.025</field>
        </record>

        <!-- Add attributes to enhanced silver bracelet -->
        <record id="enhanced_bracelet_material_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_silver_bracelet"/>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_silver_925'), ref('product_attribute_value_steel')])]</field>
        </record>

        <record id="enhanced_bracelet_gemstone_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_silver_bracelet"/>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_synthetic'), ref('product_attribute_value_topaz')])]</field>
        </record>

        <!-- Enhanced Luxury Watch -->
        <record id="enhanced_luxury_watch" model="product.template">
            <field name="name">Đồng hồ Nam PNJ Watch Automatic Premium</field>
            <field name="description">Đồng hồ nam automatic cao cấp với mặt kính sapphire chống xước, thiết kế sang trọng. Có thể chọn chất liệu vỏ khác nhau.</field>
            <field name="short_description">Đồng hồ automatic cao cấp</field>
            <field name="default_code">PNJ01234-ENH</field>
            <field name="list_price">25900000</field>
            <field name="standard_price">18000000</field>
            <field name="categ_id">1</field>  <!-- Using Nhẫn category as placeholder -->
            <field name="type">product</field>
            <field name="sale_ok">True</field>
            <field name="purchase_ok">True</field>
            <field name="website_published">True</field>
            <field name="weight">0.150</field>
        </record>

        <!-- Add attributes to enhanced watch -->
        <record id="enhanced_watch_material_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_luxury_watch"/>
            <field name="attribute_id" ref="product_attribute_material"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_steel'), ref('product_attribute_value_gold_18k')])]</field>
        </record>

        <record id="enhanced_watch_gemstone_line" model="product.template.attribute.line">
            <field name="product_tmpl_id" ref="enhanced_luxury_watch"/>
            <field name="attribute_id" ref="product_attribute_gemstone"/>
            <field name="value_ids">[(6, 0, [ref('product_attribute_value_sapphire')])]</field>
        </record>

    </data>
</odoo>
