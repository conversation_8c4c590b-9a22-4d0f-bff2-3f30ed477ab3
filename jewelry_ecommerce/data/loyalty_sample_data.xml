<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- ========================================= -->
        <!-- 1. COUPONS - Phiếu giảm giá -->
        <!-- ========================================= -->
        <record id="loyalty_program_coupon_sample" model="loyalty.program">
            <field name="name">Phiếu giảm 15% - SAVE15</field>
            <field name="program_type">coupons</field>
            <field name="trigger">with_code</field>
            <field name="applies_on">current</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">20</field>
            <field name="lucky_wheel_icon">🎫</field>
            <field name="portal_visible">False</field>
            <field name="portal_point_name">Coupon point(s)</field>
        </record>

        <record id="loyalty_rule_coupon_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="mode">with_code</field>
            <field name="code">SAVE15</field>
            <field name="minimum_amount">200000</field>
            <field name="minimum_qty">1</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">order</field>
        </record>

        <record id="loyalty_reward_coupon_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_coupon_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">15</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">1</field>
        </record>

        <!-- ========================================= -->
        <!-- 2. PROMOTIONS - Khuyến mãi tự động -->
        <!-- ========================================= -->
        <record id="loyalty_program_promotion_sample" model="loyalty.program">
            <field name="name">Jewelry Sale - Tự động giảm 10%</field>
            <field name="program_type">promotion</field>
            <field name="trigger">auto</field>
            <field name="applies_on">current</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">15</field>
            <field name="lucky_wheel_icon">🔥</field>
            <field name="portal_visible">True</field>
        </record>

        <record id="loyalty_rule_promotion_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_promotion_sample"/>
            <field name="mode">auto</field>
            <field name="minimum_amount">500000</field>
            <field name="minimum_qty">1</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">order</field>
        </record>

        <record id="loyalty_reward_promotion_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_promotion_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">10</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">1</field>
        </record>

        <!-- ========================================= -->
        <!-- 3. GIFT CARD - Thẻ quà tặng -->
        <!-- ========================================= -->
        <record id="loyalty_program_gift_card_sample" model="loyalty.program">
            <field name="name">Thẻ quà tặng 100k</field>
            <field name="program_type">gift_card</field>
            <field name="trigger">auto</field>
            <field name="applies_on">future</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">10</field>
            <field name="lucky_wheel_icon">🎁</field>
            <field name="portal_visible">True</field>
            <field name="portal_point_name">Gift Card Balance</field>
        </record>

        <record id="loyalty_rule_gift_card_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">money</field>
            <field name="reward_point_split">True</field>
        </record>

        <record id="loyalty_reward_gift_card_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_gift_card_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">per_point</field>
            <field name="discount">1</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">100000</field>
        </record>

        <!-- ========================================= -->
        <!-- 4. LOYALTY - Thẻ khách hàng thân thiết -->
        <!-- ========================================= -->
        <record id="loyalty_program_loyalty_sample" model="loyalty.program">
            <field name="name">Khách hàng thân thiết</field>
            <field name="program_type">loyalty</field>
            <field name="trigger">auto</field>
            <field name="applies_on">both</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">5</field>
            <field name="lucky_wheel_icon">⭐</field>
            <field name="portal_visible">True</field>
            <field name="portal_point_name">Loyalty Points</field>
        </record>

        <record id="loyalty_rule_loyalty_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_loyalty_sample"/>
            <field name="mode">auto</field>
            <field name="minimum_amount">50000</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">money</field>
        </record>

        <record id="loyalty_reward_loyalty_sample_1" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_loyalty_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">5</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">100000</field>
        </record>

        <record id="loyalty_reward_loyalty_sample_2" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_loyalty_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">10</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">200000</field>
        </record>

        <!-- ========================================= -->
        <!-- 5. EWALLET - Ví điện tử -->
        <!-- ========================================= -->
        <record id="loyalty_program_ewallet_sample" model="loyalty.program">
            <field name="name">eWallet - Ví điện tử</field>
            <field name="program_type">ewallet</field>
            <field name="trigger">auto</field>
            <field name="applies_on">future</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">10</field>
            <field name="lucky_wheel_icon">💳</field>
            <field name="portal_visible">True</field>
            <field name="portal_point_name">eWallet Balance</field>
        </record>

        <record id="loyalty_rule_ewallet_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">money</field>
            <field name="reward_point_split">True</field>
        </record>

        <record id="loyalty_reward_ewallet_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_ewallet_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">per_point</field>
            <field name="discount">1</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">30000</field>
        </record>

        <!-- ========================================= -->
        <!-- 6. PROMO CODE - Mã giảm giá sản phẩm -->
        <!-- ========================================= -->
        <record id="loyalty_program_promo_code_sample" model="loyalty.program">
            <field name="name">RING50 - Giảm 50k cho nhẫn</field>
            <field name="program_type">promo_code</field>
            <field name="trigger">with_code</field>
            <field name="applies_on">current</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">20</field>
            <field name="lucky_wheel_icon">🏷️</field>
            <field name="portal_visible">False</field>
        </record>

        <record id="loyalty_rule_promo_code_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="mode">with_code</field>
            <field name="code">RING50</field>
            <field name="minimum_qty">1</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">order</field>
        </record>

        <record id="loyalty_reward_promo_code_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_promo_code_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">fixed_amount</field>
            <field name="discount_fixed_amount">50000</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">1</field>
        </record>

        <!-- ========================================= -->
        <!-- 7. BUY X GET Y - Mua X Tặng Y -->
        <!-- ========================================= -->
        <record id="loyalty_program_buy_x_get_y_sample" model="loyalty.program">
            <field name="name">Mua 2 Tặng 1</field>
            <field name="program_type">buy_x_get_y</field>
            <field name="trigger">auto</field>
            <field name="applies_on">current</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">100</field>
            <field name="lucky_wheel_icon">🛒</field>
            <field name="portal_visible">True</field>
        </record>

        <record id="loyalty_rule_buy_x_get_y_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_buy_x_get_y_sample"/>
            <field name="mode">auto</field>
            <field name="minimum_qty">2</field>
            <field name="minimum_amount">100000</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">unit</field>
        </record>

        <record id="loyalty_reward_buy_x_get_y_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_buy_x_get_y_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">15</field>
            <field name="discount_applicability">cheapest</field>
            <field name="required_points">2</field>
        </record>

        <!-- ========================================= -->
        <!-- 8. NEXT ORDER COUPONS - Phiếu giảm giá đơn tiếp theo -->
        <!-- ========================================= -->
        <record id="loyalty_program_next_order_sample" model="loyalty.program">
            <field name="name">Mua ngay - Giảm lần sau 20%</field>
            <field name="program_type">next_order_coupons</field>
            <field name="trigger">auto</field>
            <field name="applies_on">future</field>
            <field name="active">True</field>
            <field name="is_lucky_wheel">True</field>
            <field name="lucky_wheel_probability">15</field>
            <field name="lucky_wheel_icon">📅</field>
            <field name="portal_visible">True</field>
            <field name="portal_point_name">Next Order Coupon</field>
        </record>

        <record id="loyalty_rule_next_order_sample" model="loyalty.rule">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="mode">auto</field>
            <field name="minimum_amount">300000</field>
            <field name="reward_point_amount">1</field>
            <field name="reward_point_mode">order</field>
        </record>

        <record id="loyalty_reward_next_order_sample" model="loyalty.reward">
            <field name="program_id" ref="loyalty_program_next_order_sample"/>
            <field name="reward_type">discount</field>
            <field name="discount_mode">percent</field>
            <field name="discount">20</field>
            <field name="discount_applicability">order</field>
            <field name="required_points">1</field>
        </record>

    </data>
</odoo>
