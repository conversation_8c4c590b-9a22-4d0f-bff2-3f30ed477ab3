# -*- coding: utf-8 -*-
from odoo import models, fields, api

class LoyaltyProgram(models.Model):
    _inherit = 'loyalty.program'

    is_lucky_wheel = fields.Bo<PERSON>an(
        string='Lucky Wheel',
        default=False,
        help='Include this program in Lucky Wheel prizes'
    )

    lucky_wheel_probability = fields.Integer(
        string='Lucky Wheel Probability (%)',
        default=10,
        help='Probability of winning this prize in Lucky Wheel (1-100)'
    )

    lucky_wheel_icon = fields.Selection([
        ('🎁', '🎁 Gift'),
        ('💎', '💎 Diamond'),
        ('🔥', '🔥 Fire'),
        ('⭐', '⭐ Star'),
        ('🏆', '🏆 Trophy'),
        ('🎫', '🎫 Ticket'),
        ('💳', '💳 Card'),
        ('🌟', '🌟 Sparkle'),
    ], string='Lucky Wheel Icon', default='🎁')

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # Cart and session management fields
    session_id = fields.Char(
        string='Session ID',
        help='Session ID for guest cart management',
        index=True
    )

    is_guest_cart = fields.<PERSON><PERSON>an(
        string='Is Guest Cart',
        default=True,
        help='Indicates if this is a guest shopping cart'
    )

    guest_email = fields.Char(
        string='Guest Email',
        help='Email for guest checkout'
    )

    guest_phone = fields.Char(
        string='Guest Phone',
        help='Phone for guest checkout'
    )

    delivery_notes = fields.Text(
        string='Delivery Notes',
        help='Special delivery instructions'
    )

    # Gift options
    gift_message = fields.Text(
        string='Gift Message',
        help='Gift message for the order'
    )

    is_gift = fields.Boolean(
        string='Is Gift',
        default=False,
        help='Indicates if this order is a gift'
    )

    recipient_name = fields.Char(
        string='Gift Recipient Name',
        help='Name of the gift recipient'
    )

    recipient_gender = fields.Selection([
        ('male', 'Male'),
        ('female', 'Female')
    ], string='Recipient Gender', help='Gender of the gift recipient')

    @api.model
    def create_guest_cart(self, session_id):
        """Create a new guest cart"""
        partner = self.env.ref('base.public_partner')
        cart = self.create({
            'partner_id': partner.id,
            'session_id': session_id,
            'is_guest_cart': True,
            'state': 'draft',
        })
        return cart

    def convert_to_order(self, customer_data):
        """Convert guest cart to actual order"""
        # Create or find customer
        partner = self._get_or_create_customer(customer_data)

        # Update order with customer info
        self.write({
            'partner_id': partner.id,
            'is_guest_cart': False,
            'guest_email': customer_data.get('email'),
            'guest_phone': customer_data.get('phone'),
            'delivery_notes': customer_data.get('notes'),
            'gift_message': customer_data.get('gift_message'),
            'is_gift': customer_data.get('is_gift', False),
            'recipient_name': customer_data.get('recipient_name'),
            'recipient_gender': customer_data.get('recipient_gender'),
        })

        # Set delivery address
        if customer_data.get('address'):
            self._set_delivery_address(customer_data)

        return self

    def _get_or_create_customer(self, customer_data):
        """Get existing customer or create new one"""
        Partner = self.env['res.partner']

        email = customer_data.get('email')
        phone = customer_data.get('phone')

        # Try to find existing customer by email
        partner = None
        if email:
            partner = Partner.search([('email', '=', email)], limit=1)

        # If not found by email, try by phone
        if not partner and phone:
            partner = Partner.search([('phone', '=', phone)], limit=1)

        # Create new customer if not found
        if not partner:
            partner = Partner.create({
                'name': customer_data.get('full_name', 'Guest Customer'),
                'email': email,
                'phone': phone,
                'is_company': False,
                'customer_rank': 1,
                'supplier_rank': 0,
            })

        return partner

    def _set_delivery_address(self, customer_data):
        """Set delivery address for the order"""
        # Create delivery address
        address_data = {
            'name': customer_data.get('recipient_name') or customer_data.get('full_name'),
            'street': customer_data.get('address'),
            'phone': customer_data.get('phone'),
            'email': customer_data.get('email'),
            'parent_id': self.partner_id.id,
            'type': 'delivery',
        }

        # Add location data if available
        if customer_data.get('province'):
            # In a real implementation, you would map province/district/ward
            # to actual res.country.state and other location models
            address_data['state_id'] = self._get_state_id(customer_data.get('province'))

        delivery_partner = self.env['res.partner'].create(address_data)
        self.partner_shipping_id = delivery_partner.id

    def _get_state_id(self, province_name):
        """Get state ID from province name (simplified)"""
        # This is a simplified version - in production you would have
        # proper mapping of provinces to res.country.state
        state = self.env['res.country.state'].search([
            ('name', 'ilike', province_name)
        ], limit=1)
        return state.id if state else False
