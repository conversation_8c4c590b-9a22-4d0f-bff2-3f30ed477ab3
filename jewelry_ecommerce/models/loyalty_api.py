# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from datetime import datetime


class LoyaltyProgram(models.Model):
    _inherit = 'loyalty.program'

    # Minimal jewelry fields for API
    jewelry_program_type = fields.Selection([
        ('jewelry_discount', 'Giảm giá trang sức'),
        ('jewelry_cashback', 'Hoàn tiền trang sức'),
        ('jewelry_points', '<PERSON><PERSON>ch điểm trang sức'),
        ('jewelry_gift', 'Quà tặng trang sức'),
    ], string='Loại chương trình trang sức')

    def get_api_data(self):
        """Format loyalty program data for API"""
        self.ensure_one()
        return {
            'id': self.id,
            'name': self.name,
            'program_type': self.program_type,
            'jewelry_program_type': self.jewelry_program_type,
            'trigger': self.trigger,
            'applies_on': self.applies_on,
            'date_from': self.date_from.isoformat() if self.date_from else None,
            'date_to': self.date_to.isoformat() if self.date_to else None,
            'is_active': self.active,
        }


class LoyaltyCard(models.Model):
    _inherit = 'loyalty.card'

    def get_api_data(self):
        """Format loyalty card/coupon data for API"""
        self.ensure_one()
        return {
            'id': self.id,
            'code': self.code,
            'program_name': self.program_id.name,
            'program_type': self.program_id.program_type,
            'jewelry_program_type': self.program_id.jewelry_program_type,
            'points': self.points,
            'partner_id': self.partner_id.id if self.partner_id else None,
            'expiration_date': getattr(self, 'expiration_date', None).isoformat() if getattr(self, 'expiration_date', None) else None,
            'is_active': self.active,
        }

    @api.model
    def validate_coupon_api(self, code, order_amount=0, partner_id=None):
        """
        Validate coupon code for API
        Frontend gọi endpoint này để validate voucher
        """
        if not code:
            return {
                'success': False,
                'message': _('Vui lòng nhập mã voucher'),
                'discount_amount': 0
            }

        # Find coupon
        coupon = self.search([('code', '=', code.upper())], limit=1)
        if not coupon:
            return {
                'success': False,
                'message': _('Mã voucher không tồn tại'),
                'discount_amount': 0
            }

        # Check if active
        if not coupon.active:
            return {
                'success': False,
                'message': _('Mã voucher đã hết hạn hoặc đã được sử dụng'),
                'discount_amount': 0
            }

        # Check program validity
        program = coupon.program_id
        if not program.active:
            return {
                'success': False,
                'message': _('Chương trình khuyến mãi đã kết thúc'),
                'discount_amount': 0
            }

        # Check date validity
        today = fields.Date.today()
        if program.date_from and today < program.date_from:
            return {
                'success': False,
                'message': _('Chương trình khuyến mãi chưa bắt đầu'),
                'discount_amount': 0
            }

        if program.date_to and today > program.date_to:
            return {
                'success': False,
                'message': _('Chương trình khuyến mãi đã kết thúc'),
                'discount_amount': 0
            }

        # Check partner restriction
        if coupon.partner_id and partner_id and coupon.partner_id.id != partner_id:
            return {
                'success': False,
                'message': _('Mã voucher này không dành cho bạn'),
                'discount_amount': 0
            }

        # Calculate discount (simplified)
        discount_amount = self._calculate_discount_amount(program, order_amount)

        return {
            'success': True,
            'message': _('Mã voucher hợp lệ'),
            'discount_amount': discount_amount,
            'coupon_data': coupon.get_api_data(),
            'program_data': program.get_api_data()
        }

    def _calculate_discount_amount(self, program, order_amount):
        """Calculate discount amount (simplified logic)"""
        # This is a simplified calculation
        # In real implementation, you'd use Odoo's loyalty calculation engine
        
        if program.program_type == 'discount':
            # Fixed discount or percentage
            if hasattr(program, 'discount_fixed_amount'):
                return min(program.discount_fixed_amount, order_amount)
            elif hasattr(program, 'discount_percentage'):
                return order_amount * (program.discount_percentage / 100)
        
        return 0

    @api.model
    def get_available_coupons_api(self, partner_id=None):
        """
        Get available coupons for customer
        Frontend gọi endpoint này để lấy voucher khả dụng
        """
        domain = [
            ('active', '=', True),
            ('program_id.active', '=', True),
        ]

        # Filter by partner if provided
        if partner_id:
            domain.append(('partner_id', 'in', [False, partner_id]))
        else:
            domain.append(('partner_id', '=', False))

        # Filter by date
        today = fields.Date.today()
        domain.extend([
            '|', ('program_id.date_from', '=', False), ('program_id.date_from', '<=', today),
            '|', ('program_id.date_to', '=', False), ('program_id.date_to', '>=', today),
        ])

        coupons = self.search(domain, order='create_date desc')
        
        return [coupon.get_api_data() for coupon in coupons]

    @api.model
    def generate_lucky_wheel_coupon_api(self, partner_id=None):
        """
        Generate coupon from lucky wheel
        Frontend gọi endpoint này khi quay lucky wheel
        """
        # Find jewelry discount program
        program = self.env['loyalty.program'].search([
            ('jewelry_program_type', '=', 'jewelry_discount'),
            ('active', '=', True),
        ], limit=1)

        if not program:
            return {
                'success': False,
                'message': _('Không có chương trình khuyến mãi nào khả dụng')
            }

        # Generate random discount (5%, 10%, 15%, 20%)
        import random
        discount_rates = [5, 10, 15, 20]
        discount_rate = random.choice(discount_rates)

        # Generate coupon code
        code = self._generate_coupon_code()

        # Create coupon
        coupon = self.create({
            'code': code,
            'program_id': program.id,
            'partner_id': partner_id,
            'points': 0,  # For discount coupons
        })

        return {
            'success': True,
            'message': _(f'Chúc mừng! Bạn nhận được voucher giảm {discount_rate}%'),
            'coupon_data': coupon.get_api_data(),
            'discount_rate': discount_rate
        }

    def _generate_coupon_code(self):
        """Generate unique coupon code"""
        import random
        import string
        
        while True:
            code = 'JEWELRY' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            existing = self.search([('code', '=', code)], limit=1)
            if not existing:
                return code
