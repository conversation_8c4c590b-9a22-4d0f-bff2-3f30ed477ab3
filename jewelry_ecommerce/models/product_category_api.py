# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductPublicCategory(models.Model):
    _inherit = 'product.public.category'

    # Minimal jewelry fields for API
    jewelry_category_type = fields.Selection([
        ('material', '<PERSON> chất liệu'),
        ('type', '<PERSON> lo<PERSON> trang sức'),
        ('collection', '<PERSON> bộ sưu tập'),
        ('occasion', '<PERSON>'),
        ('gender', '<PERSON>'),
        ('price_range', '<PERSON> mứ<PERSON> gi<PERSON>'),
    ], string='<PERSON><PERSON><PERSON> danh mục trang sức')

    is_featured = fields.Boolean(string='Danh mục nổi bật', default=False)
    sort_order = fields.Integer(string='Thứ tự sắp xếp', default=10)

    def get_api_data(self, include_children=True, include_products_count=True):
        """
        Format category data for API response
        Frontend sẽ nhận được data chuẩn từ endpoint này
        """
        self.ensure_one()
        
        data = {
            'id': self.id,
            'name': self.name,
            'slug': self._generate_slug(),
            'parent_id': self.parent_id.id if self.parent_id else None,
            'parent_name': self.parent_id.name if self.parent_id else None,
            
            # Images
            'image_url': f'/web/image/product.public.category/{self.id}/image' if self.image else None,
            
            # Jewelry specific
            'jewelry_category_type': self.jewelry_category_type,
            'is_featured': self.is_featured,
            'sort_order': self.sort_order,
            
            # SEO
            'meta_title': self.name,
            'meta_description': f"Danh mục {self.name}",
        }
        
        # Include children categories
        if include_children:
            data['children'] = [
                child.get_api_data(include_children=False, include_products_count=False)
                for child in self.child_id.filtered('website_published')
            ]
            data['has_children'] = len(data['children']) > 0
        
        # Include products count
        if include_products_count:
            data['products_count'] = self._get_products_count()
            
        return data

    def _get_products_count(self):
        """Get number of published products in this category"""
        return self.env['product.template'].search_count([
            ('public_categ_ids', 'in', self.id),
            ('sale_ok', '=', True),
            ('website_published', '=', True)
        ])

    def _generate_slug(self):
        """Generate URL-friendly slug"""
        import re
        if not self.name:
            return str(self.id)
        slug = re.sub(r'[^\w\s-]', '', self.name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return f"{slug}-{self.id}".strip('-')

    @api.model
    def get_categories_tree_api(self, include_products_count=True):
        """
        Get complete categories tree for API
        Frontend gọi endpoint này để lấy cây danh mục
        """
        # Get root categories (no parent)
        root_categories = self.search([
            ('parent_id', '=', False),
            ('website_published', '=', True)
        ], order='sort_order, name')
        
        return [
            category.get_api_data(
                include_children=True, 
                include_products_count=include_products_count
            )
            for category in root_categories
        ]

    @api.model
    def get_featured_categories_api(self, limit=6):
        """
        Get featured categories for homepage
        Frontend gọi endpoint này để lấy danh mục nổi bật
        """
        featured_categories = self.search([
            ('is_featured', '=', True),
            ('website_published', '=', True)
        ], limit=limit, order='sort_order, name')
        
        return [
            category.get_api_data(include_children=False, include_products_count=True)
            for category in featured_categories
        ]
