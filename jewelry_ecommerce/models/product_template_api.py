# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    # Minimal jewelry fields for API filtering
    jewelry_category = fields.Selection([
        ('ring', 'Nhẫn'),
        ('necklace', 'Dây chuyền'),
        ('earring', 'Bông tai'),
        ('bracelet', 'Lắc tay'),
        ('pendant', 'Mặt dây chuyền'),
        ('watch', 'Đồng hồ'),
        ('set', 'Bộ trang sức'),
    ], string='Loại trang sức')

    main_material = fields.Char(string='Chất liệu chính')
    main_gemstone = fields.Char(string='Đá quý chính')
    
    # API helper fields
    is_bestseller = fields.Boolean(string='Bán chạy', default=False)
    is_new_arrival = fields.Boolean(string='Hàng mới', default=False)
    is_limited_edition = fields.Boolean(string='Phiên bản giới hạn', default=False)

    def get_api_data(self, include_variants=True, include_attributes=True):
        """
        Format product data for API response
        Frontend sẽ nhận được data chuẩn từ endpoint này
        """
        self.ensure_one()
        
        # Base product data
        data = {
            'id': self.id,
            'name': self.name,
            'description': self.description_sale or '',
            'price': self.list_price,
            'currency': self.currency_id.name,
            'sku': self.default_code or '',
            'barcode': self.barcode or '',
            
            # Images
            'image_url': f'/web/image/product.template/{self.id}/image_1920' if self.image_1920 else None,
            'image_urls': self._get_image_urls(),
            
            # Categories
            'categories': [cat.name for cat in self.public_categ_ids],
            'category_ids': self.public_categ_ids.ids,
            
            # Jewelry specific
            'jewelry_category': self.jewelry_category,
            'main_material': self.main_material,
            'main_gemstone': self.main_gemstone,
            
            # Marketing flags
            'is_bestseller': self.is_bestseller,
            'is_new_arrival': self.is_new_arrival,
            'is_limited_edition': self.is_limited_edition,
            
            # Stock & availability
            'in_stock': self.qty_available > 0,
            'stock_quantity': self.qty_available,
            'can_be_sold': self.sale_ok,
            
            # SEO
            'slug': self._generate_slug(),
            'meta_title': self.name,
            'meta_description': self.description_sale or self.name,
        }
        
        # Include variants if requested
        if include_variants and self.product_variant_count > 1:
            data['variants'] = self._get_variants_api_data()
            data['has_variants'] = True
        else:
            data['has_variants'] = False
            
        # Include attributes if requested  
        if include_attributes:
            data['attributes'] = self._get_attributes_api_data()
            
        return data

    def _get_image_urls(self):
        """Get all product images for API"""
        urls = []
        if self.image_1920:
            urls.append(f'/web/image/product.template/{self.id}/image_1920')
        if self.image_1024:
            urls.append(f'/web/image/product.template/{self.id}/image_1024')
        if self.image_512:
            urls.append(f'/web/image/product.template/{self.id}/image_512')
        return urls

    def _get_variants_api_data(self):
        """Get product variants data for API"""
        variants = []
        for variant in self.product_variant_ids:
            if variant.active:
                variants.append({
                    'id': variant.id,
                    'name': variant.name,
                    'price': variant.lst_price,
                    'sku': variant.default_code or '',
                    'barcode': variant.barcode or '',
                    'in_stock': variant.qty_available > 0,
                    'stock_quantity': variant.qty_available,
                    'attributes': [
                        {
                            'name': ptav.attribute_id.name,
                            'value': ptav.name,
                            'price_extra': ptav.price_extra,
                        }
                        for ptav in variant.product_template_attribute_value_ids
                    ]
                })
        return variants

    def _get_attributes_api_data(self):
        """Get product attributes for API (for variant selection)"""
        attributes = []
        for line in self.attribute_line_ids:
            attr_data = {
                'id': line.attribute_id.id,
                'name': line.attribute_id.name,
                'display_type': line.attribute_id.display_type,
                'values': []
            }
            
            for value in line.value_ids:
                attr_data['values'].append({
                    'id': value.id,
                    'name': value.name,
                    'html_color': getattr(value, 'html_color', ''),
                    'image_url': f'/web/image/product.attribute.value/{value.id}/image' if getattr(value, 'image', False) else None,
                })
                
            attributes.append(attr_data)
        return attributes

    def _generate_slug(self):
        """Generate URL-friendly slug"""
        import re
        if not self.name:
            return str(self.id)
        slug = re.sub(r'[^\w\s-]', '', self.name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return f"{slug}-{self.id}".strip('-')

    @api.model
    def search_products_api(self, domain=None, limit=20, offset=0, order='name'):
        """
        Search products with API-friendly response
        Frontend gọi endpoint này để lấy danh sách sản phẩm
        """
        if domain is None:
            domain = [('sale_ok', '=', True), ('website_published', '=', True)]
            
        # Search products
        products = self.search(domain, limit=limit, offset=offset, order=order)
        total_count = self.search_count(domain)
        
        # Format response
        return {
            'products': [product.get_api_data(include_variants=False, include_attributes=False) for product in products],
            'total_count': total_count,
            'limit': limit,
            'offset': offset,
            'has_more': (offset + limit) < total_count
        }
