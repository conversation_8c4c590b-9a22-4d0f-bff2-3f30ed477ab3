# -*- coding: utf-8 -*-
from odoo import models, fields, api

class LuckyWheelSpin(models.Model):
    _name = 'lucky.wheel.spin'
    _description = 'Lucky Wheel Spin Records'
    _order = 'spin_datetime desc'
    
    # User identification
    email = fields.Char(
        string='Email',
        help='User email if provided'
    )
    
    session_id = fields.Char(
        string='Session ID',
        help='Session ID for guest users'
    )
    
    # Prize information
    prize_name = fields.Char(
        string='Prize Name',
        required=True,
        help='Name of the prize won'
    )
    
    prize_type = fields.Selection([
        ('discount', 'Discount'),
        ('free_shipping', 'Free Shipping'),
        ('no_prize', 'No Prize'),
        ('gift', 'Gift'),
        ('points', 'Loyalty Points')
    ], string='Prize Type', required=True)
    
    prize_value = fields.Float(
        string='Prize Value',
        help='Value of the prize (percentage for discount, amount for gift)'
    )
    
    voucher_code = fields.Char(
        string='Voucher Code',
        help='Generated voucher code if applicable'
    )
    
    # Timing
    spin_date = fields.Date(
        string='Spin Date',
        default=fields.Date.today,
        required=True,
        index=True
    )
    
    spin_datetime = fields.Datetime(
        string='Spin Date Time',
        default=fields.Datetime.now,
        required=True
    )
    
    # Status
    is_used = fields.Boolean(
        string='Voucher Used',
        default=False,
        help='Whether the voucher has been used'
    )
    
    used_order_id = fields.Many2one(
        'sale.order',
        string='Used in Order',
        help='Order where the voucher was used'
    )
    
    used_datetime = fields.Datetime(
        string='Used Date Time',
        help='When the voucher was used'
    )
    
    # Additional info
    ip_address = fields.Char(
        string='IP Address',
        help='IP address of the user'
    )
    
    user_agent = fields.Text(
        string='User Agent',
        help='Browser user agent'
    )
    
    @api.model
    def create(self, vals):
        """Override create to add IP and user agent"""
        if hasattr(self.env, 'request') and self.env.request:
            vals['ip_address'] = self.env.request.httprequest.remote_addr
            vals['user_agent'] = self.env.request.httprequest.user_agent.string
        return super().create(vals)
    
    def mark_voucher_used(self, order_id):
        """Mark voucher as used"""
        self.write({
            'is_used': True,
            'used_order_id': order_id,
            'used_datetime': fields.Datetime.now()
        })
    
    @api.model
    def get_daily_spins(self, email=None, session_id=None, date=None):
        """Get number of spins for a specific day"""
        if not date:
            date = fields.Date.today()
        
        domain = [('spin_date', '=', date)]
        
        if email:
            domain.append(('email', '=', email))
        elif session_id:
            domain.append(('session_id', '=', session_id))
        else:
            return 0
        
        return self.search_count(domain)
    
    @api.model
    def can_spin_today(self, email=None, session_id=None, max_spins=10):
        """Check if user can spin today"""
        daily_spins = self.get_daily_spins(email, session_id)
        return daily_spins < max_spins
    
    @api.model
    def get_user_history(self, email=None, session_id=None, limit=50):
        """Get user's spin history"""
        domain = []
        
        if email:
            domain.append(('email', '=', email))
        elif session_id:
            domain.append(('session_id', '=', session_id))
        else:
            return self.browse()
        
        return self.search(domain, limit=limit)
    
    @api.model
    def cleanup_old_records(self, days=90):
        """Cleanup old spin records (run via cron)"""
        cutoff_date = fields.Date.today() - timedelta(days=days)
        old_records = self.search([('spin_date', '<', cutoff_date)])
        old_records.unlink()
        return len(old_records)
    
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"{record.prize_name} - {record.spin_date}"
            if record.email:
                name += f" ({record.email})"
            elif record.session_id:
                name += f" (Session: {record.session_id[:8]}...)"
            result.append((record.id, name))
        return result
