# -*- coding: utf-8 -*-
from odoo import http, fields
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class OrdersAPI(http.Controller):

    @http.route('/api/orders/<int:order_id>', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_order(self, order_id, **kwargs):
        """Get order details"""
        try:
            email = kwargs.get('email')

            # Find order
            order = request.env['sale.order'].sudo().browse(order_id)

            if not order.exists():
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Order not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Verify access (for guest orders, check email)
            if order.is_guest_cart and email:
                if order.guest_email != email:
                    return request.make_response(
                        json.dumps({'success': False, 'error': 'Access denied'}),
                        status=403,
                        headers=[('Content-Type', 'application/json; charset=utf-8')]
                    )

            order_data = self._format_order_data(order)

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': order_data
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting order: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/orders/track', methods=['GET'], auth='public', csrf=False, cors='*')
    def track_order(self, **kwargs):
        """Track order by email and phone"""
        try:
            email = kwargs.get('email')
            phone = kwargs.get('phone')
            order_number = kwargs.get('order_number')

            if not email and not phone:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Email or phone is required'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Build search domain
            domain = []
            if order_number:
                domain.append(('name', '=', order_number))

            if email:
                domain.append(('guest_email', '=', email))
            elif phone:
                domain.append(('guest_phone', '=', phone))

            # Find orders
            orders = request.env['sale.order'].sudo().search(domain, order='create_date desc')

            if not orders:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'No orders found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            orders_data = []
            for order in orders:
                orders_data.append(self._format_order_summary(order))

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'orders': orders_data,
                        'total_orders': len(orders_data)
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error tracking order: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/orders/<int:order_id>/status', methods=['PUT'], auth='user', csrf=False, cors='*')
    def update_order_status(self, order_id, **kwargs):
        """Update order status (admin only)"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            new_status = data.get('status')

            if not new_status:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Status is required'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Find order
            order = request.env['sale.order'].browse(order_id)

            if not order.exists():
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Order not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Update status based on new_status
            if new_status == 'confirmed':
                order.action_confirm()
            elif new_status == 'cancelled':
                order.action_cancel()
            elif new_status == 'delivered':
                # Mark as delivered (this would typically involve delivery orders)
                order.message_post(body="Order marked as delivered")

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': f'Order status updated to {new_status}',
                    'data': {
                        'order_id': order.id,
                        'status': order.state
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error updating order status: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/orders/<int:order_id>/cancel', methods=['POST'], auth='public', csrf=False, cors='*')
    def cancel_order(self, order_id, **kwargs):
        """Cancel order (guest can cancel within time limit)"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            email = data.get('email')
            reason = data.get('reason', 'Customer request')

            # Find order
            order = request.env['sale.order'].sudo().browse(order_id)

            if not order.exists():
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Order not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Verify access for guest orders
            if order.is_guest_cart and email:
                if order.guest_email != email:
                    return request.make_response(
                        json.dumps({'success': False, 'error': 'Access denied'}),
                        status=403,
                        headers=[('Content-Type', 'application/json; charset=utf-8')]
                    )

            # Check if order can be cancelled
            if order.state not in ['draft', 'sent', 'sale']:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Order cannot be cancelled'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Cancel order
            order.action_cancel()
            order.message_post(body=f"Order cancelled by customer. Reason: {reason}")

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': 'Order cancelled successfully',
                    'data': {
                        'order_id': order.id,
                        'status': order.state
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error cancelling order: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    def _format_order_data(self, order):
        """Format complete order data"""
        # Get order status display
        status_mapping = {
            'draft': 'Nháp',
            'sent': 'Đã gửi báo giá',
            'sale': 'Đã xác nhận',
            'done': 'Hoàn thành',
            'cancel': 'Đã hủy'
        }

        # Get delivery info
        delivery_partner = order.partner_shipping_id or order.partner_id

        order_data = {
            'id': order.id,
            'order_number': order.name,
            'status': order.state,
            'status_display': status_mapping.get(order.state, order.state),
            'order_date': order.date_order.strftime('%Y-%m-%d %H:%M:%S') if order.date_order else None,
            'total_amount': order.amount_total,
            'currency': order.currency_id.name,
            'customer': {
                'name': order.partner_id.name,
                'email': order.guest_email or order.partner_id.email,
                'phone': order.guest_phone or order.partner_id.phone
            },
            'delivery_address': {
                'name': delivery_partner.name,
                'street': delivery_partner.street,
                'phone': delivery_partner.phone,
                'email': delivery_partner.email
            },
            'items': self._format_order_items(order.order_line),
            'totals': {
                'subtotal': order.amount_untaxed,
                'tax_amount': order.amount_tax,
                'total_amount': order.amount_total
            },
            'notes': order.note,
            'gift_info': {
                'is_gift': order.is_gift,
                'recipient_name': order.recipient_name,
                'recipient_gender': order.recipient_gender,
                'gift_message': order.gift_message
            },
            'tracking': {
                'can_cancel': order.state in ['draft', 'sent', 'sale'],
                'estimated_delivery': self._get_estimated_delivery_date(order)
            }
        }

        return order_data

    def _format_order_summary(self, order):
        """Format order summary for listing"""
        status_mapping = {
            'draft': 'Nháp',
            'sent': 'Đã gửi báo giá',
            'sale': 'Đã xác nhận',
            'done': 'Hoàn thành',
            'cancel': 'Đã hủy'
        }

        return {
            'id': order.id,
            'order_number': order.name,
            'status': order.state,
            'status_display': status_mapping.get(order.state, order.state),
            'order_date': order.date_order.strftime('%Y-%m-%d') if order.date_order else None,
            'total_amount': order.amount_total,
            'currency': order.currency_id.name,
            'item_count': len(order.order_line.filtered(lambda l: not l.display_type)),
            'tracking_url': f'/orders/{order.id}?email={order.guest_email}'
        }

    def _format_order_items(self, order_lines):
        """Format order items"""
        items = []
        for line in order_lines:
            if line.display_type:  # Skip section/note lines
                continue

            # Get product images
            images = []
            if line.product_id.image_1920:
                base_url = f'/web/image/product.product/{line.product_id.id}'
                images = [{
                    'thumbnail': f'{base_url}/image_128',
                    'medium': f'{base_url}/image_512'
                }]

            item = {
                'id': line.id,
                'product_name': line.product_id.display_name,
                'quantity': int(line.product_uom_qty),
                'unit_price': line.price_unit,
                'total_price': line.price_subtotal,
                'currency': line.currency_id.name,
                'images': images
            }
            items.append(item)
        return items

    def _get_estimated_delivery_date(self, order):
        """Get estimated delivery date"""
        from datetime import datetime, timedelta
        if order.date_order:
            estimated_date = order.date_order + timedelta(days=3)
            return estimated_date.strftime('%Y-%m-%d')
        return None
