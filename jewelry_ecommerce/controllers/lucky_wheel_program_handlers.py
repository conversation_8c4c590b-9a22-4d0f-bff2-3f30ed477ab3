# -*- coding: utf-8 -*-
import logging
from odoo.http import request

_logger = logging.getLogger(__name__)

class LuckyWheelProgramHandlers:
    """Handlers for all 8 Odoo loyalty program types in Lucky Wheel"""
    
    def _create_coupon_prize(self, program, base_prize):
        """🎫 Coupons - Phiếu giảm giá"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        if reward.reward_type == 'discount':
            if reward.discount_mode == 'percent':
                prize_name = f"Phiếu giảm {int(reward.discount)}%"
                value = reward.discount
            elif reward.discount_mode == 'fixed_amount':
                prize_name = f"Phiếu giảm {int(reward.discount_fixed_amount):,}đ"
                value = reward.discount_fixed_amount
            else:
                prize_name = "Phiếu giảm giá"
                value = 0
        else:
            prize_name = "Phiếu quà tặng"
            value = 0
            
        return {
            **base_prize,
            'name': prize_name,
            'type': 'coupon',
            'value': value,
            'usage_instructions': 'Nhập mã khi checkout, chỉ dùng 1 lần',
            'needs_code': True,
        }
    
    def _create_gift_card_prize(self, program, base_prize):
        """🎁 Gift Card - Thẻ quà tặng"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        value = reward.required_points if reward.discount_mode == 'per_point' else 50000
        
        return {
            **base_prize,
            'name': f"Thẻ quà tặng {int(value):,}đ",
            'type': 'gift_card',
            'value': value,
            'usage_instructions': 'Dùng cho đơn hàng bất kỳ, trừ dần giá trị',
            'needs_code': True,
        }
    
    def _create_loyalty_prize(self, program, base_prize):
        """⭐ Loyalty - Thẻ khách hàng thân thiết"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        points = reward.required_points or 100
        
        return {
            **base_prize,
            'name': f"{int(points)} điểm thưởng",
            'type': 'loyalty_points',
            'value': points,
            'usage_instructions': 'Tích lũy điểm, dùng khi đủ điểm đổi thưởng',
            'needs_code': True,
        }
    
    def _create_promotion_prize(self, program, base_prize):
        """🔥 Promotion - Khuyến mãi"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        if reward.reward_type == 'discount':
            if reward.discount_mode == 'percent':
                prize_name = f"Khuyến mãi {int(reward.discount)}%"
                value = reward.discount
            elif reward.discount_mode == 'fixed_amount':
                prize_name = f"Khuyến mãi {int(reward.discount_fixed_amount):,}đ"
                value = reward.discount_fixed_amount
            else:
                prize_name = "Khuyến mãi đặc biệt"
                value = 0
        else:
            prize_name = "Khuyến mãi sản phẩm"
            value = 0
            
        # Get minimum amount from rules
        min_amount = 0
        if program.rule_ids:
            min_amount = program.rule_ids[0].minimum_amount or 0
            
        usage_text = f"Tự động giảm khi đơn hàng từ {int(min_amount):,}đ" if min_amount > 0 else "Tự động giảm khi đủ điều kiện"
        
        return {
            **base_prize,
            'name': prize_name,
            'type': 'promotion',
            'value': value,
            'usage_instructions': usage_text,
            'needs_code': False,  # Auto-apply
        }
    
    def _create_ewallet_prize(self, program, base_prize):
        """💳 eWallet - Ví điện tử"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        value = reward.required_points if reward.discount_mode == 'per_point' else 30000
        
        return {
            **base_prize,
            'name': f"eWallet {int(value):,}đ",
            'type': 'ewallet',
            'value': value,
            'usage_instructions': 'Nạp vào ví điện tử, dùng dần trong các đơn hàng',
            'needs_code': True,
        }
    
    def _create_promo_code_prize(self, program, base_prize):
        """🏷️ Promo Code - Mã giảm giá"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        if reward.reward_type == 'discount':
            if reward.discount_mode == 'percent':
                prize_name = f"Mã giảm {int(reward.discount)}%"
                value = reward.discount
            elif reward.discount_mode == 'fixed_amount':
                prize_name = f"Mã giảm {int(reward.discount_fixed_amount):,}đ"
                value = reward.discount_fixed_amount
            else:
                prize_name = "Mã giảm giá"
                value = 0
        else:
            prize_name = "Mã quà tặng"
            value = 0
            
        # Get product restrictions
        product_info = ""
        if program.rule_ids and program.rule_ids[0].product_category_id:
            product_info = f" cho {program.rule_ids[0].product_category_id.name}"
        elif program.rule_ids and program.rule_ids[0].product_tag_id:
            product_info = f" cho {program.rule_ids[0].product_tag_id.name}"
            
        return {
            **base_prize,
            'name': prize_name + product_info,
            'type': 'promo_code',
            'value': value,
            'usage_instructions': f'Nhập mã khi mua{product_info}',
            'needs_code': True,
        }
    
    def _create_buy_x_get_y_prize(self, program, base_prize):
        """🛒 Buy X Get Y - Mua X Tặng Y"""
        if not program.rule_ids or not program.reward_ids:
            return None
            
        rule = program.rule_ids[0]
        reward = program.reward_ids[0]
        
        min_qty = rule.minimum_qty or 2
        if reward.reward_type == 'product':
            gift_qty = reward.reward_product_qty or 1
            prize_name = f"Mua {min_qty} Tặng {gift_qty}"
            usage_text = f"Tự động tặng {gift_qty} sản phẩm khi mua {min_qty}"
        else:
            prize_name = f"Mua {min_qty} Giảm giá"
            usage_text = f"Tự động giảm giá khi mua {min_qty} sản phẩm"
            
        return {
            **base_prize,
            'name': prize_name,
            'type': 'buy_x_get_y',
            'value': min_qty,
            'usage_instructions': usage_text,
            'needs_code': False,  # Auto-apply
        }
    
    def _create_next_order_prize(self, program, base_prize):
        """📅 Next Order Coupons - Phiếu giảm giá đơn hàng tiếp theo"""
        if not program.reward_ids:
            return None
            
        reward = program.reward_ids[0]
        if reward.reward_type == 'discount':
            if reward.discount_mode == 'percent':
                prize_name = f"Giảm {int(reward.discount)}% đơn tiếp theo"
                value = reward.discount
            elif reward.discount_mode == 'fixed_amount':
                prize_name = f"Giảm {int(reward.discount_fixed_amount):,}đ đơn tiếp theo"
                value = reward.discount_fixed_amount
            else:
                prize_name = "Ưu đãi đơn tiếp theo"
                value = 0
        else:
            prize_name = "Quà tặng đơn tiếp theo"
            value = 0
            
        # Get minimum amount for current order
        min_amount = 0
        if program.rule_ids:
            min_amount = program.rule_ids[0].minimum_amount or 0
            
        current_order_text = f" (đơn hiện tại từ {int(min_amount):,}đ)" if min_amount > 0 else ""
        
        return {
            **base_prize,
            'name': prize_name,
            'type': 'next_order_coupon',
            'value': value,
            'usage_instructions': f'Nhận coupon cho lần mua sau{current_order_text}',
            'needs_code': True,
        }
