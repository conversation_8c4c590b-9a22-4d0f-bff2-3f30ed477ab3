# -*- coding: utf-8 -*-
from odoo import http, fields
from odoo.http import request
import json
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class CheckoutAPI(http.Controller):

    @http.route('/api/checkout/validate', methods=['POST'], auth='public', csrf=False, cors='*')
    def validate_checkout(self, **kwargs):
        """Validate checkout data before creating order"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            # Get cart
            cart = self._get_cart(session_id)
            if not cart or not cart.order_line:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Cart is empty'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Validate customer data
            customer_data = data.get('customer', {})
            validation_errors = self._validate_customer_data(customer_data)

            if validation_errors:
                return request.make_response(
                    json.dumps({'success': False, 'errors': validation_errors}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Calculate totals
            totals = self._calculate_checkout_totals(cart, data.get('voucher_code'))

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'cart_valid': True,
                        'customer_valid': True,
                        'totals': totals,
                        'estimated_delivery': self._get_estimated_delivery()
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error validating checkout: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/checkout/create-order', methods=['POST'], auth='public', csrf=False, cors='*')
    def create_order(self, **kwargs):
        """Create order from cart"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            # Get cart
            cart = self._get_cart(session_id)
            if not cart or not cart.order_line:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Cart is empty'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Validate customer data
            customer_data = data.get('customer', {})
            validation_errors = self._validate_customer_data(customer_data)

            if validation_errors:
                return request.make_response(
                    json.dumps({'success': False, 'errors': validation_errors}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Convert cart to order
            order = cart.convert_to_order(customer_data)

            # Apply voucher if provided using Odoo's native loyalty system
            voucher_code = data.get('voucher_code')
            voucher_applied = False
            if voucher_code:
                voucher_applied = self._apply_voucher_to_order(order, voucher_code)
                if not voucher_applied:
                    _logger.warning(f"Failed to apply voucher: {voucher_code}")

            # Set payment method
            payment_method = data.get('payment_method', 'cod')
            self._set_payment_method(order, payment_method)

            # Confirm order - Odoo will auto-generate order number
            order.action_confirm()

            # Send confirmation email (optional)
            try:
                self._send_order_confirmation(order)
            except Exception as e:
                _logger.warning(f"Failed to send confirmation email: {str(e)}")

            # Prepare response
            order_data = {
                'order_id': order.id,
                'order_number': order.name,
                'total_amount': order.amount_total,
                'currency': order.currency_id.name,
                'status': order.state,
                'estimated_delivery': self._get_estimated_delivery(),
                'payment_method': payment_method,
                'customer': {
                    'name': customer_data.get('full_name'),
                    'email': customer_data.get('email'),
                    'phone': customer_data.get('phone')
                },
                'items': self._format_order_items(order.order_line),
                'tracking_url': f'/orders/track/{order.id}?email={customer_data.get("email")}'
            }

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': 'Order created successfully',
                    'data': order_data
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error creating order: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/checkout/payment-methods', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_payment_methods(self, **kwargs):
        """Get available payment methods"""
        try:
            payment_methods = [
                {
                    'id': 'cod',
                    'name': 'Thanh toán khi nhận hàng',
                    'description': 'Thanh toán bằng tiền mặt khi nhận hàng',
                    'icon': 'truck',
                    'enabled': True,
                    'fee': 0
                },
                {
                    'id': 'bank_transfer',
                    'name': 'Chuyển khoản ngân hàng',
                    'description': 'Chuyển khoản qua ngân hàng',
                    'icon': 'credit-card',
                    'enabled': True,
                    'fee': 0,
                    'bank_info': {
                        'account_number': '**********',
                        'bank_name': 'Sacombank HCM',
                        'account_holder': 'Công Ty TNHH MTV Toloco Việt Nam',
                        'branch': 'Chi nhánh TP.HCM'
                    }
                }
            ]

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': payment_methods
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting payment methods: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    def _get_cart(self, session_id):
        """Get cart by session ID"""
        return request.env['sale.order'].sudo().search([
            ('state', '=', 'draft'),
            ('session_id', '=', session_id)
        ], limit=1)

    def _validate_customer_data(self, customer_data):
        """Validate customer data"""
        errors = []

        required_fields = ['full_name', 'email', 'phone', 'address']
        for field in required_fields:
            if not customer_data.get(field):
                errors.append(f'{field} is required')

        # Validate email format
        email = customer_data.get('email')
        if email and '@' not in email:
            errors.append('Invalid email format')

        # Validate phone format
        phone = customer_data.get('phone')
        if phone and len(phone) < 10:
            errors.append('Phone number must be at least 10 digits')

        return errors

    def _calculate_checkout_totals(self, cart, voucher_code=None):
        """Calculate checkout totals"""
        subtotal = cart.amount_untaxed
        tax_amount = cart.amount_tax
        shipping_fee = 0  # Free shipping
        discount_amount = 0

        # Apply voucher discount if provided
        if voucher_code:
            discount_amount = self._calculate_voucher_discount(cart, voucher_code)

        total_amount = subtotal + tax_amount + shipping_fee - discount_amount

        return {
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'shipping_fee': shipping_fee,
            'discount_amount': discount_amount,
            'total_amount': total_amount,
            'currency': cart.currency_id.name,
            'savings': discount_amount
        }

    def _calculate_voucher_discount(self, cart, voucher_code):
        """Calculate voucher discount amount"""
        # This is a simplified version - in production you would
        # integrate with Odoo loyalty system
        voucher_discounts = {
            'WELCOME10': 0.10,  # 10%
            'FREESHIP': 30000,  # Fixed amount
            'SUMMER25': 0.25,   # 25%
            'GIAM5': 0.05,      # 5%
            'GIAM10': 0.10,     # 10%
            'GIAM15': 0.15,     # 15%
            'GIAM20': 0.20,     # 20%
        }

        discount = voucher_discounts.get(voucher_code.upper(), 0)

        if discount < 1:  # Percentage discount
            return cart.amount_untaxed * discount
        else:  # Fixed amount discount
            return min(discount, cart.amount_untaxed)

    def _apply_voucher_to_order(self, order, voucher_code):
        """Apply voucher discount to order using Odoo's native loyalty system"""
        try:
            _logger.info(f"Applying voucher code: {voucher_code} to order: {order.id}")

            # Use Odoo's native method to apply voucher code
            status = order._try_apply_code(voucher_code)

            if 'error' in status:
                _logger.warning(f"Voucher application failed: {status['error']}")
                return False

            if 'not_found' in status:
                _logger.warning(f"Voucher code not found: {voucher_code}")
                return False

            # If status contains rewards, apply them automatically
            if status:
                _logger.info(f"Voucher code {voucher_code} applied successfully")

                # Auto-apply rewards if there's only one reward
                for coupon, rewards in status.items():
                    if len(rewards) == 1:
                        # Apply the reward automatically
                        reward_status = order._apply_program_reward(rewards, coupon)
                        if 'error' in reward_status:
                            _logger.error(f"Error applying reward: {reward_status['error']}")
                            return False
                        else:
                            _logger.info(f"Reward applied successfully for voucher: {voucher_code}")

                # Update programs and rewards to recalculate totals
                order._update_programs_and_rewards()
                return True
            else:
                _logger.warning(f"No rewards available for voucher: {voucher_code}")
                return False

        except Exception as e:
            _logger.error(f"Exception applying voucher {voucher_code}: {str(e)}")
            return False

    def _set_payment_method(self, order, payment_method):
        """Set payment method for order"""
        # Store payment method in order notes or custom field
        if not order.note:
            order.note = f'Payment Method: {payment_method}'
        else:
            order.note += f'\nPayment Method: {payment_method}'

    # Removed: Odoo auto-generates order numbers via sequences

    def _get_estimated_delivery(self):
        """Get estimated delivery date"""
        from datetime import datetime, timedelta
        estimated_date = datetime.now() + timedelta(days=3)
        return estimated_date.strftime('%Y-%m-%d')

    def _send_order_confirmation(self, order):
        """Send order confirmation email"""
        # This would integrate with Odoo email system
        # For now, just log the action
        _logger.info(f"Order confirmation email should be sent for order {order.name}")

    def _format_order_items(self, order_lines):
        """Format order items for response"""
        items = []
        for line in order_lines:
            if line.display_type:  # Skip discount lines, etc.
                continue

            item = {
                'id': line.id,
                'product_name': line.product_id.display_name,
                'quantity': int(line.product_uom_qty),
                'unit_price': line.price_unit,
                'total_price': line.price_subtotal,
                'currency': line.currency_id.name
            }
            items.append(item)
        return items
