# -*- coding: utf-8 -*-
from odoo import http, fields
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class CartAPI(http.Controller):

    @http.route('/api/cart', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_cart(self, **kwargs):
        """Get current cart items"""
        try:
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            # Find or create cart (draft sale.order)
            cart = self._get_or_create_cart(session_id)

            cart_data = {
                'cart_id': cart.id,
                'session_id': session_id,
                'items': self._format_cart_items(cart.order_line),
                'summary': {
                    'total_items': sum(line.product_uom_qty for line in cart.order_line),
                    'subtotal': cart.amount_untaxed,
                    'tax_amount': cart.amount_tax,
                    'total_amount': cart.amount_total,
                    'currency': cart.currency_id.name
                }
            }

            return request.make_response(
                json.dumps({'success': True, 'data': cart_data}, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/cart/add', methods=['POST'], auth='public', csrf=False, cors='*')
    def add_to_cart(self, **kwargs):
        """Add product to cart"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            product_id = data.get('product_id')
            variant_id = data.get('variant_id')
            quantity = data.get('quantity', 1)
            attributes = data.get('attributes', {})

            # Validate input
            if not variant_id:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'variant_id is required'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Get or create cart
            cart = self._get_or_create_cart(session_id)

            # Get product variant
            product = request.env['product.product'].sudo().browse(variant_id)
            if not product.exists():
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Product variant not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Check if item already exists in cart
            existing_line = cart.order_line.filtered(
                lambda l: l.product_id.id == variant_id
            )

            if existing_line:
                # Update quantity
                existing_line.product_uom_qty += quantity
                action = 'updated'
            else:
                # Create new line using Odoo's standard method
                order_line_vals = {
                    'order_id': cart.id,
                    'product_id': variant_id,
                    'product_uom_qty': quantity,
                }
                # Let Odoo compute price_unit, name, etc. automatically
                cart.order_line.create(order_line_vals)
                action = 'added'

            # Odoo will auto-compute totals when order lines change

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': f'Product {action} to cart',
                    'data': {
                        'cart_id': cart.id,
                        'total_items': sum(line.product_uom_qty for line in cart.order_line),
                        'total_amount': cart.amount_total
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error adding to cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/cart/update', methods=['PUT'], auth='public', csrf=False, cors='*')
    def update_cart_item(self, **kwargs):
        """Update cart item quantity"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            item_id = data.get('item_id')
            quantity = data.get('quantity', 1)

            if not item_id:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'item_id is required'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Get cart
            cart = self._get_or_create_cart(session_id)

            # Find cart line
            cart_line = cart.order_line.filtered(lambda l: l.id == item_id)
            if not cart_line:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Cart item not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            if quantity <= 0:
                # Remove item if quantity is 0 or negative
                cart_line.unlink()
                action = 'removed'
            else:
                # Update quantity
                cart_line.product_uom_qty = quantity
                action = 'updated'

            # Odoo will auto-compute totals when order lines change

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': f'Cart item {action}',
                    'data': {
                        'cart_id': cart.id,
                        'total_items': sum(line.product_uom_qty for line in cart.order_line),
                        'total_amount': cart.amount_total
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error updating cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/cart/remove/<int:item_id>', methods=['DELETE'], auth='public', csrf=False, cors='*')
    def remove_cart_item(self, item_id, **kwargs):
        """Remove item from cart"""
        try:
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            # Get cart
            cart = self._get_or_create_cart(session_id)

            # Find and remove cart line
            cart_line = cart.order_line.filtered(lambda l: l.id == item_id)
            if not cart_line:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Cart item not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            cart_line.unlink()

            # Odoo will auto-compute totals when order lines change

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': 'Item removed from cart',
                    'data': {
                        'cart_id': cart.id,
                        'total_items': sum(line.product_uom_qty for line in cart.order_line),
                        'total_amount': cart.amount_total
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error removing cart item: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/cart/clear', methods=['DELETE'], auth='public', csrf=False, cors='*')
    def clear_cart(self, **kwargs):
        """Clear entire cart"""
        try:
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')

            # Get cart
            cart = self._get_or_create_cart(session_id)

            # Remove all cart lines
            cart.order_line.unlink()

            return request.make_response(
                json.dumps({
                    'success': True,
                    'message': 'Cart cleared',
                    'data': {
                        'cart_id': cart.id,
                        'total_items': 0,
                        'total_amount': 0
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error clearing cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    def _get_or_create_cart(self, session_id):
        """Get existing cart or create new one"""
        SaleOrder = request.env['sale.order'].sudo()

        # Try to find existing cart
        cart = SaleOrder.search([
            ('state', '=', 'draft'),
            ('session_id', '=', session_id)
        ], limit=1)

        if not cart:
            # Create new cart
            partner = request.env.ref('base.public_partner')
            cart = SaleOrder.create({
                'partner_id': partner.id,
                'session_id': session_id,
                'is_guest_cart': True,
                'state': 'draft',
                'company_id': request.env.company.id,
            })

        return cart

    def _format_cart_items(self, order_lines):
        """Format cart items for frontend"""
        items = []
        for line in order_lines:
            # Get product images
            images = []
            if line.product_id.image_1920:
                base_url = f'/web/image/product.product/{line.product_id.id}'
                images = [{
                    'id': f'product_{line.product_id.id}',
                    'name': line.product_id.display_name,
                    'thumbnail': f'{base_url}/image_128',
                    'small': f'{base_url}/image_256',
                    'medium': f'{base_url}/image_512',
                    'large': f'{base_url}/image_1024',
                    'xlarge': f'{base_url}/image_1920'
                }]

            # Get product attributes
            attributes = []
            for ptav in line.product_id.product_template_attribute_value_ids:
                attributes.append({
                    'attribute_name': ptav.attribute_id.name,
                    'value_name': ptav.product_attribute_value_id.name,
                    'attribute_id': ptav.attribute_id.id,
                    'value_id': ptav.product_attribute_value_id.id,
                })

            item = {
                'id': line.id,
                'product_id': line.product_id.product_tmpl_id.id,
                'product_name': line.product_id.display_name,
                'variant_id': line.product_id.id,
                'quantity': int(line.product_uom_qty),
                'unit_price': line.price_unit,
                'total_price': line.price_subtotal,
                'currency': line.currency_id.name,
                'attributes': attributes,
                'images': images,
                'product_url': f'/products/{line.product_id.product_tmpl_id.id}'
            }
            items.append(item)
        return items
