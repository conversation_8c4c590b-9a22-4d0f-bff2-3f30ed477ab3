# -*- coding: utf-8 -*-
import json
import random
import logging
from datetime import datetime, timedelta
from odoo import http, fields
from odoo.http import request
from .lucky_wheel_program_handlers import LuckyWheelProgramHandlers

_logger = logging.getLogger(__name__)

class LuckyWheelAPI(http.Controller, LuckyWheelProgramHandlers):

    @http.route('/api/lucky-wheel/spin', methods=['POST'], auth='public', csrf=False, cors='*')
    def spin_wheel(self, **kwargs):
        """Spin the lucky wheel and generate prizes"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8')) if request.httprequest.data else {}
            email = data.get('email')
            session_id = request.httprequest.headers.get('X-Session-ID')

            # Check daily spin limit
            if not self._can_spin_today(email, session_id):
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'error': 'Daily spin limit reached. Come back tomorrow!',
                        'next_spin_available': self._get_next_spin_time()
                    }),
                    status=429,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            # Generate random prize
            prize_result = self._generate_prize()

            # Create voucher code if won a promotion
            voucher_code = None
            if prize_result['type'] != 'no_prize' and prize_result.get('program_id'):
                voucher_code = self._create_voucher_from_program(prize_result['program_id'])

            # Record the spin
            self._record_spin(email, session_id, prize_result, voucher_code)

            response_data = {
                'success': True,
                'data': {
                    'prize': prize_result,
                    'voucher_code': voucher_code,
                    'spin_time': datetime.now().isoformat(),
                    'spins_remaining': self._get_remaining_spins(email, session_id)
                }
            }

            return request.make_response(
                json.dumps(response_data, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error in lucky wheel spin: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/lucky-wheel/prizes', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_available_prizes(self, **_kwargs):
        """Get list of available prizes from Odoo loyalty programs"""
        try:
            prizes = self._get_prize_list()

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'prizes': prizes,
                        'total_prizes': len(prizes),
                        'message': 'Prizes loaded from active Odoo loyalty programs'
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting prizes: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/lucky-wheel/promotions', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_lucky_wheel_promotions(self, **_kwargs):
        """Get all active promotions available for Lucky Wheel"""
        try:
            LoyaltyProgram = request.env['loyalty.program'].sudo()

            # Lấy tất cả loyalty programs có Lucky Wheel = True và còn hiệu lực
            # Hỗ trợ TẤT CẢ 8 loại program của Odoo
            today = fields.Date.today()
            domain = [
                ('active', '=', True),
                ('is_lucky_wheel', '=', True),
                ('program_type', 'in', [
                    'coupons',              # Phiếu giảm giá
                    'gift_card',            # Thẻ quà tặng
                    'loyalty',              # Thẻ khách hàng thân thiết
                    'promotion',            # Khuyến mãi
                    'ewallet',              # Ví điện tử
                    'promo_code',           # Mã giảm giá
                    'buy_x_get_y',          # Mua X Tặng Y
                    'next_order_coupons'    # Phiếu giảm giá đơn hàng tiếp theo
                ]),
                '|', ('date_to', '=', False), ('date_to', '>=', today),
                '|', ('date_from', '=', False), ('date_from', '<=', today),
            ]

            programs = LoyaltyProgram.search(domain)

            promotions = []
            for program in programs:
                # Lấy thông tin reward
                reward_info = {}
                if program.reward_ids:
                    reward = program.reward_ids[0]  # Lấy reward đầu tiên
                    reward_info = {
                        'type': reward.reward_type,
                        'discount_mode': reward.discount_mode if reward.reward_type == 'discount' else None,
                        'discount_percent': reward.discount if reward.discount_mode == 'percent' else 0,
                        'discount_fixed': reward.discount_fixed_amount if reward.discount_mode == 'fixed_amount' else 0,
                        'currency': reward.currency_id.name if reward.currency_id else 'USD'
                    }

                # Lấy thông tin rule (điều kiện)
                rule_info = {}
                if program.rule_ids:
                    rule = program.rule_ids[0]  # Lấy rule đầu tiên
                    rule_info = {
                        'minimum_amount': rule.minimum_amount,
                        'minimum_qty': rule.minimum_qty,
                        'mode': rule.mode
                    }

                promotion_data = {
                    'id': program.id,
                    'name': program.name,
                    'program_type': program.program_type,
                    'description': program.name,  # Sử dụng name thay vì portal_description
                    'active': program.active,
                    'date_from': program.date_from.isoformat() if program.date_from else None,
                    'date_to': program.date_to.isoformat() if program.date_to else None,
                    'is_lucky_wheel': program.is_lucky_wheel,
                    'lucky_wheel_probability': program.lucky_wheel_probability,
                    'lucky_wheel_icon': program.lucky_wheel_icon,
                    'reward_info': reward_info,
                    'rule_info': rule_info,
                    'trigger': program.trigger,
                    'applies_on': program.applies_on
                }

                promotions.append(promotion_data)

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'promotions': promotions,
                        'total_count': len(promotions),
                        'filter_date': today.isoformat(),
                        'message': f'Found {len(promotions)} active Lucky Wheel promotions'
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting Lucky Wheel promotions: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    @http.route('/api/lucky-wheel/history', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_spin_history(self, **kwargs):
        """Get user's spin history"""
        try:
            email = request.params.get('email')
            session_id = request.httprequest.headers.get('X-Session-ID')

            if not email and not session_id:
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Email or session required'}),
                    status=400,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )

            history = self._get_spin_history(email, session_id)

            return request.make_response(
                json.dumps({
                    'success': True,
                    'data': {
                        'history': history,
                        'total_spins': len(history),
                        'spins_today': self._get_spins_today(email, session_id),
                        'can_spin_today': self._can_spin_today(email, session_id)
                    }
                }, ensure_ascii=False, indent=2),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

        except Exception as e:
            _logger.error(f"Error getting spin history: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )

    def _get_prize_list(self):
        """Get prizes from active Odoo loyalty programs with Lucky Wheel enabled"""
        try:
            LoyaltyProgram = request.env['loyalty.program'].sudo()

            # Lấy các loyalty programs được đánh dấu cho Lucky Wheel và còn hiệu lực
            today = fields.Date.today()
            domain = [
                ('active', '=', True),
                ('is_lucky_wheel', '=', True),  # Chỉ lấy programs được check Lucky Wheel
                '|', ('date_to', '=', False), ('date_to', '>=', today),  # Chưa hết hạn
                '|', ('date_from', '=', False), ('date_from', '<=', today),  # Đã bắt đầu
            ]

            active_programs = LoyaltyProgram.search(domain)

            prizes = []

            # Convert loyalty programs thành prizes - Hỗ trợ TẤT CẢ 8 loại
            for program in active_programs:
                prize_data = self._create_prize_from_program(program)
                if prize_data:
                    prizes.append(prize_data)

            # Thêm "no prize" option (50% chance để balance)
            prizes.append({
                'id': 0,
                'name': 'Chúc bạn may mắn lần sau',
                'type': 'no_prize',
                'value': 0,
                'program_id': None,
                'display_probability': 50,  # Chỉ để hiển thị
                'description': 'Hãy thử lại vào ngày mai!',
                'color': '#95A5A6',
                'icon': '🍀'
            })

            _logger.info(f"Loaded {len(prizes)} prizes from {len(active_programs)} loyalty programs")
            return prizes

        except Exception as e:
            _logger.error(f"Error loading prizes from loyalty programs: {str(e)}")
            # Fallback to default prizes if error
            return self._get_fallback_prizes()

    def _get_program_color(self, program_type):
        """Get color based on program type"""
        colors = {
            'coupons': '#FFD700',      # Gold
            'gift_card': '#FF6B6B',    # Red
            'promotion': '#4ECDC4',    # Teal
            'promo_code': '#45B7D1',   # Blue
            'loyalty': '#9B59B6',      # Purple
            'ewallet': '#2ECC71',      # Green
        }
        return colors.get(program_type, '#FFD700')

    def _get_program_icon(self, program_type):
        """Get icon based on program type"""
        icons = {
            'coupons': '🎫',
            'gift_card': '🎁',
            'promotion': '🔥',
            'promo_code': '💎',
            'loyalty': '⭐',
            'ewallet': '💳',
        }
        return icons.get(program_type, '🎁')

    def _get_fallback_prizes(self):
        """Fallback prizes if can't load from loyalty programs"""
        return [
            {
                'id': 0,
                'name': 'Chúc bạn may mắn lần sau',
                'type': 'no_prize',
                'value': 0,
                'probability': 100,
                'description': 'Hãy thử lại vào ngày mai!',
                'color': '#95A5A6',
                'icon': '🍀'
            }
        ]

    def _generate_prize(self):
        """Generate random prize - equal chance for all prizes"""
        prizes = self._get_prize_list()

        if not prizes:
            # Fallback if no prizes
            return {
                'id': 0,
                'name': 'Chúc bạn may mắn lần sau',
                'type': 'no_prize',
                'value': 0,
                'description': 'Hãy thử lại vào ngày mai!',
                'color': '#95A5A6',
                'icon': '🍀'
            }

        # Random đều giữa tất cả prizes (bao gồm no_prize)
        selected_prize = random.choice(prizes)

        return {
            'id': selected_prize['id'],
            'name': selected_prize['name'],
            'type': selected_prize['type'],
            'value': selected_prize['value'],
            'program_id': selected_prize.get('program_id'),
            'description': selected_prize['description'],
            'color': selected_prize['color'],
            'icon': selected_prize['icon']
        }

    def _create_voucher_from_program(self, program_id):
        """Get or create voucher code from existing loyalty program"""
        try:
            # Get the loyalty program
            LoyaltyProgram = request.env['loyalty.program'].sudo()
            program = LoyaltyProgram.browse(program_id)

            if not program.exists():
                _logger.error(f"Loyalty program {program_id} not found")
                return None

            # Check if program has existing unused loyalty cards
            LoyaltyCard = request.env['loyalty.card'].sudo()

            # Tìm cards chưa được sử dụng (chưa có order nào dùng)
            existing_cards = LoyaltyCard.search([
                ('program_id', '=', program.id),
                ('active', '=', True),
                ('partner_id', '=', False),  # Chưa assign cho partner nào
            ], limit=10)  # Lấy nhiều hơn để check

            # Filter cards chưa được sử dụng trong orders
            unused_card = None
            if existing_cards:
                for card in existing_cards:
                    # Check xem card này đã được dùng trong order nào chưa
                    if not self._is_voucher_used(card.code):
                        unused_card = card
                        break

            if unused_card:
                # Use existing unused card
                voucher_code = unused_card.code
                _logger.info(f"Using existing unused voucher: {voucher_code} from program: {program.name}")
                return voucher_code
            else:
                # Create new loyalty card
                voucher_code = f"LUCKY{random.randint(1000, 9999)}{datetime.now().strftime('%m%d')}"

                LoyaltyCard.create({
                    'program_id': program.id,
                    'code': voucher_code,
                    'partner_id': request.env.ref('base.public_partner').id,
                    'points': 0,  # Initialize points
                })

                _logger.info(f"Created new voucher: {voucher_code} from program: {program.name}")
                return voucher_code

        except Exception as e:
            _logger.error(f"Error getting voucher from program {program_id}: {str(e)}")
            return None

    def _create_prize_from_program(self, program):
        """Create prize data from loyalty program - Support ALL 8 program types"""
        try:
            display_probability = program.lucky_wheel_probability or 10
            base_prize = {
                'id': program.id,
                'program_id': program.id,
                'program_type': program.program_type,
                'display_probability': display_probability,
                'description': program.name,
                'color': self._get_program_color(program.program_type),
                'icon': program.lucky_wheel_icon or self._get_program_icon(program.program_type),
                'trigger': program.trigger,
                'applies_on': program.applies_on,
            }

            # Handle theo từng program type
            if program.program_type == 'coupons':
                return self._create_coupon_prize(program, base_prize)
            elif program.program_type == 'gift_card':
                return self._create_gift_card_prize(program, base_prize)
            elif program.program_type == 'loyalty':
                return self._create_loyalty_prize(program, base_prize)
            elif program.program_type == 'promotion':
                return self._create_promotion_prize(program, base_prize)
            elif program.program_type == 'ewallet':
                return self._create_ewallet_prize(program, base_prize)
            elif program.program_type == 'promo_code':
                return self._create_promo_code_prize(program, base_prize)
            elif program.program_type == 'buy_x_get_y':
                return self._create_buy_x_get_y_prize(program, base_prize)
            elif program.program_type == 'next_order_coupons':
                return self._create_next_order_prize(program, base_prize)
            else:
                _logger.warning(f"Unknown program type: {program.program_type}")
                return None

        except Exception as e:
            _logger.error(f"Error creating prize from program {program.id}: {str(e)}")
            return None

    def _is_voucher_used(self, voucher_code):
        """Check if voucher code has been used in any order"""
        try:
            # Check trong sale.order xem có order nào dùng voucher này chưa
            SaleOrder = request.env['sale.order'].sudo()

            # Tìm orders có applied_coupon_ids chứa voucher này
            orders_with_voucher = SaleOrder.search([
                ('applied_coupon_ids.code', '=', voucher_code)
            ], limit=1)

            if orders_with_voucher:
                _logger.info(f"Voucher {voucher_code} already used in order {orders_with_voucher[0].name}")
                return True

            # Check trong loyalty.history xem có history nào của voucher này
            LoyaltyHistory = request.env['loyalty.history'].sudo()
            history = LoyaltyHistory.search([
                ('card_id.code', '=', voucher_code),
                ('used', '>', 0)  # Đã được sử dụng
            ], limit=1)

            if history:
                _logger.info(f"Voucher {voucher_code} has usage history")
                return True

            return False

        except Exception as e:
            _logger.error(f"Error checking voucher usage {voucher_code}: {str(e)}")
            # Nếu có lỗi, coi như chưa dùng để tránh block voucher
            return False

    def _can_spin_today(self, email, session_id):
        """Check if user can spin today (max 10 spins per day)"""
        spins_today = self._get_spins_today(email, session_id)
        return spins_today < 10  # Allow 10 spins per day

    def _get_spins_today(self, email, session_id):
        """Get number of spins today"""
        try:
            # Use custom model to track spins (we'll create this)
            SpinRecord = request.env['lucky.wheel.spin'].sudo()

            today = fields.Date.today()
            domain = [('spin_date', '=', today)]

            if email:
                domain.append(('email', '=', email))
            elif session_id:
                domain.append(('session_id', '=', session_id))

            return SpinRecord.search_count(domain)

        except Exception as e:
            _logger.error(f"Error getting spins today: {str(e)}")
            return 0

    def _get_remaining_spins(self, email, session_id):
        """Get remaining spins for today"""
        spins_today = self._get_spins_today(email, session_id)
        return max(0, 10 - spins_today)

    def _record_spin(self, email, session_id, prize_result, voucher_code):
        """Record the spin in database"""
        try:
            # Create spin record (we'll need to create this model)
            SpinRecord = request.env['lucky.wheel.spin'].sudo()

            SpinRecord.create({
                'email': email,
                'session_id': session_id,
                'prize_name': prize_result['name'],
                'prize_type': prize_result['type'],
                'prize_value': prize_result['value'],
                'voucher_code': voucher_code,
                'spin_date': fields.Date.today(),
                'spin_datetime': fields.Datetime.now(),
            })

            _logger.info(f"Recorded spin for {email or session_id}: {prize_result['name']}")

        except Exception as e:
            _logger.error(f"Error recording spin: {str(e)}")

    def _get_spin_history(self, email, session_id):
        """Get user's spin history"""
        try:
            SpinRecord = request.env['lucky.wheel.spin'].sudo()

            domain = []
            if email:
                domain.append(('email', '=', email))
            elif session_id:
                domain.append(('session_id', '=', session_id))

            records = SpinRecord.search(domain, order='spin_datetime desc', limit=50)

            history = []
            for record in records:
                history.append({
                    'id': record.id,
                    'prize_name': record.prize_name,
                    'prize_type': record.prize_type,
                    'prize_value': record.prize_value,
                    'voucher_code': record.voucher_code,
                    'spin_date': record.spin_date.isoformat() if record.spin_date else None,
                    'spin_datetime': record.spin_datetime.isoformat() if record.spin_datetime else None,
                })

            return history

        except Exception as e:
            _logger.error(f"Error getting spin history: {str(e)}")
            return []

    def _get_next_spin_time(self):
        """Get next available spin time (tomorrow)"""
        tomorrow = datetime.now() + timedelta(days=1)
        tomorrow_start = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        return tomorrow_start.isoformat()
