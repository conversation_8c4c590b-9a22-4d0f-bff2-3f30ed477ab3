<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jewelry Mega Menu Demo - PNJ Style</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 0;
            position: relative;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .top-bar {
            background: #000;
            padding: 8px 0;
            font-size: 13px;
            text-align: center;
        }

        .top-bar a {
            color: #ccc;
            text-decoration: none;
            margin: 0 15px;
        }

        .main-header {
            padding: 15px 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 700;
            color: #d4af37;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 28px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            padding: 10px 40px 10px 15px;
            border-radius: 25px;
            color: white;
            width: 300px;
            font-size: 14px;
        }

        .search-box input::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .search-box button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #d4af37;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            color: white;
            cursor: pointer;
        }

        .header-icon {
            color: white;
            font-size: 20px;
            text-decoration: none;
            position: relative;
            transition: color 0.3s;
        }

        .header-icon:hover {
            color: #d4af37;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Mega Menu */
        .mega-menu {
            background: white;
            border-top: 1px solid #eee;
            position: relative;
            z-index: 1000;
        }

        .menu-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .menu-nav {
            display: flex;
            align-items: center;
        }

        .menu-item {
            position: relative;
        }

        .menu-link {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            text-decoration: none;
            color: #333;
            font-weight: 500;
            font-size: 15px;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .menu-link:hover {
            color: #d4af37;
            border-bottom-color: #d4af37;
        }

        .menu-link i {
            margin-left: 8px;
            font-size: 12px;
            transition: transform 0.3s;
        }

        .menu-item:hover .menu-link i {
            transform: rotate(180deg);
        }

        /* Mega Dropdown */
        .mega-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 900px;
            background: white;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            border-radius: 0 0 12px 12px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .menu-item:hover .mega-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-content {
            padding: 30px;
        }

        .dropdown-grid {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 30px;
        }

        /* Category Showcase */
        .category-showcase {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .category-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .category-title {
            font-family: 'Playfair Display', serif;
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .category-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .view-all-btn {
            background: #d4af37;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .view-all-btn:hover {
            background: #b8941f;
            transform: translateY(-2px);
        }

        /* Menu Content */
        .menu-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        /* Subcategories */
        .subcategories h4 {
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #d4af37;
        }

        .subcategory-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .subcategory-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #555;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s;
            gap: 12px;
        }

        .subcategory-item:hover {
            background: #f8f9fa;
            color: #d4af37;
            transform: translateX(5px);
        }

        .subcategory-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #d4af37, #b8941f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        /* Featured Products */
        .featured-products h4 {
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #d4af37;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .product-card {
            display: block;
            text-decoration: none;
            background: white;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s;
            border: 1px solid #eee;
            position: relative;
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
            transition: left 0.5s;
        }

        .product-card:hover::before {
            left: 100%;
        }

        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #d4af37;
        }

        .product-image {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .product-name {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            color: #d4af37;
            font-weight: 600;
            font-size: 14px;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .hero h1 {
            font-family: 'Playfair Display', serif;
            font-size: 48px;
            margin-bottom: 20px;
            color: #d4af37;
        }

        .hero p {
            font-size: 18px;
            color: #ccc;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .search-box input {
                width: 250px;
            }

            .menu-nav {
                flex-direction: column;
            }

            .mega-dropdown {
                width: 100vw;
                left: -20px;
            }

            .dropdown-grid {
                grid-template-columns: 1fr;
            }

            .menu-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <a href="tel:1900545457">Hotline: 1900 54 54 57</a>
                <a href="#">Tìm cửa hàng</a>
                <a href="#">Chăm sóc khách hàng</a>
            </div>
        </div>
        
        <div class="main-header">
            <div class="container">
                <div class="header-content">
                    <a href="#" class="logo">
                        <i class="fas fa-gem"></i>
                        PNJ Jewelry
                    </a>
                    
                    <div class="search-box">
                        <input type="text" placeholder="Tìm kiếm trang sức...">
                        <button type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="header-actions">
                        <a href="#" class="header-icon">
                            <i class="fas fa-heart"></i>
                        </a>
                        <a href="#" class="header-icon">
                            <i class="fas fa-user"></i>
                        </a>
                        <a href="#" class="header-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count">3</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mega Menu -->
    <nav class="mega-menu">
        <div class="menu-container">
            <div class="menu-nav" id="megaMenu">
                <!-- Menu items will be loaded here -->
                <div class="menu-item">
                    <a href="#" class="menu-link">
                        <span>Loading...</span>
                        <i class="fas fa-spinner fa-spin"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Jewelry Mega Menu Demo</h1>
            <p>Trải nghiệm menu đa cấp hiện đại với hình ảnh, mô tả và sản phẩm nổi bật cho từng danh mục trang sức.</p>
        </div>
    </section>

    <script>
        // Load mega menu data from API
        async function loadMegaMenu() {
            try {
                const response = await fetch('/api/categories/mega-menu');
                const data = await response.json();
                
                if (data.success) {
                    renderMegaMenu(data.data.mega_menu);
                } else {
                    console.error('Failed to load mega menu:', data.error);
                }
            } catch (error) {
                console.error('Error loading mega menu:', error);
            }
        }

        function renderMegaMenu(categories) {
            const menuNav = document.getElementById('megaMenu');
            menuNav.innerHTML = '';

            categories.forEach(category => {
                const menuItem = createMenuItem(category);
                menuNav.appendChild(menuItem);
            });
        }

        function createMenuItem(category) {
            const menuItem = document.createElement('div');
            menuItem.className = 'menu-item';

            menuItem.innerHTML = `
                <a href="/category/${category.slug}" class="menu-link">
                    <span>${category.name}</span>
                    <i class="fas fa-chevron-down"></i>
                </a>
                <div class="mega-dropdown">
                    <div class="dropdown-content">
                        <div class="dropdown-grid">
                            <div class="category-showcase">
                                <img src="${category.image_url || '/static/placeholder.jpg'}" 
                                     alt="${category.name}" class="category-image">
                                <h3 class="category-title">${category.name}</h3>
                                <p class="category-desc">${category.website_description || 'Khám phá bộ sưu tập ' + category.name.toLowerCase() + ' cao cấp'}</p>
                                <a href="/category/${category.slug}" class="view-all-btn">
                                    Xem tất cả
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                            <div class="menu-content">
                                ${category.children.length > 0 ? createSubcategories(category.children) : ''}
                                ${createFeaturedProducts(category.featured_products)}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            return menuItem;
        }

        function createSubcategories(children) {
            if (children.length === 0) return '';

            const subcategoriesHtml = children.map(child => `
                <a href="/category/${child.slug}" class="subcategory-item">
                    <div class="subcategory-icon">
                        <i class="fas fa-ring"></i>
                    </div>
                    <span>${child.name}</span>
                </a>
            `).join('');

            return `
                <div class="subcategories">
                    <h4>Danh mục con</h4>
                    <div class="subcategory-list">
                        ${subcategoriesHtml}
                    </div>
                </div>
            `;
        }

        function createFeaturedProducts(products) {
            if (products.length === 0) return '';

            const productsHtml = products.map(product => `
                <a href="/product/${product.slug}" class="product-card">
                    <img src="${product.image_url || '/static/placeholder.jpg'}" 
                         alt="${product.name}" class="product-image">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${formatPrice(product.price)}</div>
                </a>
            `).join('');

            return `
                <div class="featured-products">
                    <h4>Sản phẩm nổi bật</h4>
                    <div class="products-grid">
                        ${productsHtml}
                    </div>
                </div>
            `;
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(price);
        }

        // Load menu when page loads
        document.addEventListener('DOMContentLoaded', loadMegaMenu);
    </script>
</body>
</html>
