import React from 'react';
import { Link } from 'react-router-dom';
import { useMegaMenu, formatPrice, getFeaturedProducts } from '../hooks/useMegaMenu';

const HomePage = () => {
  const { menuData, loading } = useMegaMenu();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  const featuredProducts = getFeaturedProducts(menuData, 8);

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-24 text-center">
          <h1 className="text-5xl font-bold mb-6 text-amber-400">
            Jewelry Mega Menu Demo
          </h1>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Trải nghiệm menu đa cấp hiện đại với hình ảnh, mô tả và sản phẩm nổi bật 
            cho từng danh mục trang sức cao cấp.
          </p>
          <div className="flex justify-center space-x-4">
            <Link
              to="/category/nhan"
              className="bg-amber-500 hover:bg-amber-600 text-white px-8 py-3 rounded-full font-semibold transition-colors"
            >
              Khám phá ngay
            </Link>
            <Link
              to="/search"
              className="border border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-full font-semibold transition-colors"
            >
              Tìm kiếm
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Showcase */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Danh mục sản phẩm
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Hover vào menu phía trên để xem mega menu với đầy đủ thông tin danh mục, 
              sản phẩm nổi bật và hình ảnh chất lượng cao.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {menuData.map((category) => (
              <Link
                key={category.id}
                to={`/category/${category.slug}`}
                className="group bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="aspect-w-16 aspect-h-12">
                  <img
                    src={category.image_url || '/placeholder.jpg'}
                    alt={category.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-amber-600">
                    {category.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {category.website_description || `Khám phá bộ sưu tập ${category.name.toLowerCase()}`}
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-amber-600 font-semibold">
                      {category.stats.total_products} sản phẩm
                    </span>
                    <span className="text-gray-400 text-sm">
                      →
                    </span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Sản phẩm nổi bật
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Những sản phẩm được yêu thích nhất từ các danh mục khác nhau
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <Link
                key={product.id}
                to={`/product/${product.slug}`}
                className="group bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="aspect-w-1 aspect-h-1">
                  <img
                    src={product.image_url || '/placeholder.jpg'}
                    alt={product.name}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-amber-600">
                    {product.name}
                  </h3>
                  <p className="text-amber-600 font-bold text-lg">
                    {formatPrice(product.price)}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Tính năng Mega Menu
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🖼️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Hình ảnh đa kích thước
              </h3>
              <p className="text-gray-600">
                Responsive images với 5 kích thước khác nhau (128px → 1920px) 
                tối ưu cho mọi thiết bị
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏗️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Cấu trúc phân cấp
              </h3>
              <p className="text-gray-600">
                Menu đa cấp với danh mục cha, danh mục con và sản phẩm nổi bật 
                được tổ chức khoa học
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Hiệu suất cao
              </h3>
              <p className="text-gray-600">
                API tối ưu với caching, lazy loading và data structure 
                được thiết kế cho performance
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-amber-500 to-amber-600">
        <div className="max-w-4xl mx-auto text-center px-4">
          <h2 className="text-3xl font-bold text-white mb-4">
            Sẵn sàng tích hợp vào dự án của bạn?
          </h2>
          <p className="text-amber-100 text-lg mb-8">
            Mega Menu API đã sẵn sàng với đầy đủ documentation và sample code
          </p>
          <div className="flex justify-center space-x-4">
            <a
              href="/api/categories/mega-menu"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-amber-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
            >
              Xem API Response
            </a>
            <Link
              to="/category/nhan"
              className="border border-white text-white hover:bg-white hover:text-amber-600 px-8 py-3 rounded-full font-semibold transition-colors"
            >
              Demo Category
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
