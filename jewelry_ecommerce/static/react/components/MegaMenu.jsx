import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, HeartIcon, UserIcon, ShoppingCartIcon, SearchIcon } from '@heroicons/react/24/outline';
import { Link } from 'react-router-dom';

const MegaMenu = () => {
  const [menuData, setMenuData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeMenu, setActiveMenu] = useState(null);

  useEffect(() => {
    loadMegaMenuData();
  }, []);

  const loadMegaMenuData = async () => {
    try {
      const response = await fetch('/api/categories/mega-menu');
      const data = await response.json();
      
      if (data.success) {
        setMenuData(data.data.mega_menu);
      }
    } catch (error) {
      console.error('Error loading mega menu:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleMenuEnter = (categoryId) => {
    setActiveMenu(categoryId);
  };

  const handleMenuLeave = () => {
    setActiveMenu(null);
  };

  if (loading) {
    return (
      <nav className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600"></div>
            <span className="ml-2 text-gray-600">Đang tải menu...</span>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <>
      {/* Header */}
      <header className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        {/* Top Bar */}
        <div className="bg-black py-2">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex justify-center space-x-6 text-sm">
              <a href="tel:1900545457" className="text-gray-300 hover:text-white">
                Hotline: 1900 54 54 57
              </a>
              <a href="#" className="text-gray-300 hover:text-white">
                Tìm cửa hàng
              </a>
              <a href="#" className="text-gray-300 hover:text-white">
                Chăm sóc khách hàng
              </a>
            </div>
          </div>
        </div>

        {/* Main Header */}
        <div className="py-4">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">💎</span>
                </div>
                <span className="text-2xl font-bold text-amber-400">PNJ Jewelry</span>
              </Link>

              {/* Search */}
              <div className="flex-1 max-w-lg mx-8">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Tìm kiếm trang sức..."
                    className="w-full bg-white/10 border border-white/20 rounded-full py-2 pl-4 pr-12 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-amber-400"
                  />
                  <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-amber-500 hover:bg-amber-600 rounded-full p-2">
                    <SearchIcon className="w-4 h-4 text-white" />
                  </button>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-6">
                <button className="text-white hover:text-amber-400 transition-colors">
                  <HeartIcon className="w-6 h-6" />
                </button>
                <button className="text-white hover:text-amber-400 transition-colors">
                  <UserIcon className="w-6 h-6" />
                </button>
                <button className="text-white hover:text-amber-400 transition-colors relative">
                  <ShoppingCartIcon className="w-6 h-6" />
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    3
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Mega Menu Navigation */}
      <nav className="bg-white border-t border-gray-200 relative z-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex">
            {menuData.map((category) => (
              <div
                key={category.id}
                className="relative"
                onMouseEnter={() => handleMenuEnter(category.id)}
                onMouseLeave={handleMenuLeave}
              >
                {/* Menu Link */}
                <Link
                  to={`/category/${category.slug}`}
                  className={`flex items-center px-6 py-4 text-gray-700 hover:text-amber-600 font-medium transition-colors border-b-2 border-transparent hover:border-amber-600 ${
                    activeMenu === category.id ? 'text-amber-600 border-amber-600' : ''
                  }`}
                >
                  <span>{category.name}</span>
                  <ChevronDownIcon 
                    className={`w-4 h-4 ml-2 transition-transform ${
                      activeMenu === category.id ? 'rotate-180' : ''
                    }`} 
                  />
                </Link>

                {/* Mega Dropdown */}
                {activeMenu === category.id && (
                  <div className="absolute top-full left-0 w-[900px] bg-white shadow-2xl rounded-b-lg border border-gray-200 z-50">
                    <div className="p-8">
                      <div className="grid grid-cols-3 gap-8">
                        {/* Category Showcase */}
                        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 text-center">
                          <img
                            src={category.image_url || '/placeholder.jpg'}
                            alt={category.name}
                            className="w-full h-32 object-cover rounded-lg mb-4"
                          />
                          <h3 className="text-xl font-bold text-gray-900 mb-2">
                            {category.name}
                          </h3>
                          <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                            {category.website_description || `Khám phá bộ sưu tập ${category.name.toLowerCase()} cao cấp`}
                          </p>
                          <Link
                            to={`/category/${category.slug}`}
                            className="inline-flex items-center bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-full text-sm font-medium transition-colors"
                          >
                            Xem tất cả
                            <span className="ml-2">→</span>
                          </Link>
                        </div>

                        {/* Subcategories */}
                        {category.children.length > 0 && (
                          <div>
                            <h4 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b-2 border-amber-500">
                              Danh mục con
                            </h4>
                            <div className="space-y-3">
                              {category.children.map((child) => (
                                <Link
                                  key={child.id}
                                  to={`/category/${child.slug}`}
                                  className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                                >
                                  <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-amber-600 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-white text-sm">💍</span>
                                  </div>
                                  <span className="text-gray-700 group-hover:text-amber-600 font-medium">
                                    {child.name}
                                  </span>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Featured Products */}
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b-2 border-amber-500">
                            Sản phẩm nổi bật
                          </h4>
                          <div className="grid grid-cols-2 gap-3">
                            {category.featured_products.slice(0, 4).map((product) => (
                              <Link
                                key={product.id}
                                to={`/product/${product.slug}`}
                                className="block bg-white border border-gray-200 rounded-lg p-3 hover:shadow-lg hover:border-amber-300 transition-all group"
                              >
                                <img
                                  src={product.image_url || '/placeholder.jpg'}
                                  alt={product.name}
                                  className="w-full h-16 object-cover rounded mb-2"
                                />
                                <h5 className="text-xs font-medium text-gray-900 mb-1 line-clamp-2 group-hover:text-amber-600">
                                  {product.name}
                                </h5>
                                <p className="text-amber-600 font-semibold text-sm">
                                  {formatPrice(product.price)}
                                </p>
                              </Link>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </nav>
    </>
  );
};

export default MegaMenu;
