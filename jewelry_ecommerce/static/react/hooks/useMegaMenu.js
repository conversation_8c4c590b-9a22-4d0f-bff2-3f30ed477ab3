import { useState, useEffect, useCallback } from 'react';

const API_BASE_URL = process.env.REACT_APP_API_URL || '';

export const useMegaMenu = () => {
  const [menuData, setMenuData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeMenu, setActiveMenu] = useState(null);

  // Load mega menu data from API
  const loadMegaMenuData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/api/categories/mega-menu`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setMenuData(data.data.mega_menu);
      } else {
        throw new Error(data.error || 'Failed to load mega menu data');
      }
    } catch (err) {
      console.error('Error loading mega menu:', err);
      setError(err.message);
      
      // Fallback to mock data for development
      if (process.env.NODE_ENV === 'development') {
        setMenuData(getMockMenuData());
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle menu hover states
  const handleMenuEnter = useCallback((categoryId) => {
    setActiveMenu(categoryId);
  }, []);

  const handleMenuLeave = useCallback(() => {
    setActiveMenu(null);
  }, []);

  // Refresh menu data
  const refreshMenu = useCallback(() => {
    loadMegaMenuData();
  }, [loadMegaMenuData]);

  // Load data on mount
  useEffect(() => {
    loadMegaMenuData();
  }, [loadMegaMenuData]);

  return {
    menuData,
    loading,
    error,
    activeMenu,
    handleMenuEnter,
    handleMenuLeave,
    refreshMenu
  };
};

// Mock data for development/fallback
const getMockMenuData = () => [
  {
    id: 1,
    name: 'Nhẫn',
    slug: 'nhan',
    sequence: 10,
    image_url: '/images/categories/rings.jpg',
    image_variants: {
      thumbnail: '/images/categories/rings-thumb.jpg',
      small: '/images/categories/rings-small.jpg',
      medium: '/images/categories/rings-medium.jpg',
      large: '/images/categories/rings-large.jpg',
      xlarge: '/images/categories/rings-xlarge.jpg'
    },
    website_description: 'Bộ sưu tập nhẫn cưới, nhẫn đính hôn và nhẫn thời trang cao cấp',
    children: [
      {
        id: 11,
        name: 'Nhẫn cầu hôn',
        slug: 'nhan-cau-hon',
        parent_id: 1,
        sequence: 1
      },
      {
        id: 12,
        name: 'Nhẫn cưới',
        slug: 'nhan-cuoi',
        parent_id: 1,
        sequence: 2
      },
      {
        id: 13,
        name: 'Nhẫn thời trang',
        slug: 'nhan-thoi-trang',
        parent_id: 1,
        sequence: 3
      }
    ],
    featured_products: [
      {
        id: 101,
        name: 'Nhẫn cưới vàng 18K PNJ',
        slug: 'nhan-cuoi-vang-18k-pnj',
        price: 8950000,
        image_url: '/images/products/ring-1.jpg'
      },
      {
        id: 102,
        name: 'Nhẫn đính hôn kim cương',
        slug: 'nhan-dinh-hon-kim-cuong',
        price: 25000000,
        image_url: '/images/products/ring-2.jpg'
      }
    ],
    stats: {
      total_products: 45,
      subcategories_count: 3,
      has_featured_products: true
    }
  },
  {
    id: 2,
    name: 'Dây chuyền',
    slug: 'day-chuyen',
    sequence: 20,
    image_url: '/images/categories/necklaces.jpg',
    image_variants: {
      thumbnail: '/images/categories/necklaces-thumb.jpg',
      small: '/images/categories/necklaces-small.jpg',
      medium: '/images/categories/necklaces-medium.jpg',
      large: '/images/categories/necklaces-large.jpg',
      xlarge: '/images/categories/necklaces-xlarge.jpg'
    },
    website_description: 'Dây chuyền và mặt dây chuyền vàng, bạc đính đá quý',
    children: [
      {
        id: 21,
        name: 'Dây chuyền vàng',
        slug: 'day-chuyen-vang',
        parent_id: 2,
        sequence: 1
      },
      {
        id: 22,
        name: 'Dây chuyền bạc',
        slug: 'day-chuyen-bac',
        parent_id: 2,
        sequence: 2
      }
    ],
    featured_products: [
      {
        id: 201,
        name: 'Dây chuyền vàng 18K PNJ',
        slug: 'day-chuyen-vang-18k-pnj',
        price: 12450000,
        image_url: '/images/products/necklace-1.jpg'
      },
      {
        id: 202,
        name: 'Mặt dây chuyền đính đá Topaz',
        slug: 'mat-day-chuyen-topaz',
        price: 5783000,
        image_url: '/images/products/pendant-1.jpg'
      }
    ],
    stats: {
      total_products: 32,
      subcategories_count: 2,
      has_featured_products: true
    }
  },
  {
    id: 3,
    name: 'Bông tai',
    slug: 'bong-tai',
    sequence: 30,
    image_url: '/images/categories/earrings.jpg',
    image_variants: {
      thumbnail: '/images/categories/earrings-thumb.jpg',
      small: '/images/categories/earrings-small.jpg',
      medium: '/images/categories/earrings-medium.jpg',
      large: '/images/categories/earrings-large.jpg',
      xlarge: '/images/categories/earrings-xlarge.jpg'
    },
    website_description: 'Bông tai kim cương, vàng và đá quý cao cấp',
    children: [],
    featured_products: [
      {
        id: 301,
        name: 'Bông tai kim cương vàng trắng 14K',
        slug: 'bong-tai-kim-cuong-vang-trang',
        price: 15750000,
        image_url: '/images/products/earring-1.jpg'
      }
    ],
    stats: {
      total_products: 28,
      subcategories_count: 0,
      has_featured_products: true
    }
  },
  {
    id: 4,
    name: 'Lắc tay',
    slug: 'lac-tay',
    sequence: 40,
    image_url: '/images/categories/bracelets.jpg',
    image_variants: {
      thumbnail: '/images/categories/bracelets-thumb.jpg',
      small: '/images/categories/bracelets-small.jpg',
      medium: '/images/categories/bracelets-medium.jpg',
      large: '/images/categories/bracelets-large.jpg',
      xlarge: '/images/categories/bracelets-xlarge.jpg'
    },
    website_description: 'Lắc tay vàng, bạc và đá quý thời trang',
    children: [],
    featured_products: [
      {
        id: 401,
        name: 'Lắc tay bạc đính đá PNJ Silver',
        slug: 'lac-tay-bac-dinh-da',
        price: 2890000,
        image_url: '/images/products/bracelet-1.jpg'
      }
    ],
    stats: {
      total_products: 18,
      subcategories_count: 0,
      has_featured_products: true
    }
  }
];

// Utility functions
export const formatPrice = (price) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
};

export const getCategoryById = (menuData, categoryId) => {
  return menuData.find(category => category.id === categoryId);
};

export const getSubcategoryById = (menuData, subcategoryId) => {
  for (const category of menuData) {
    const subcategory = category.children.find(child => child.id === subcategoryId);
    if (subcategory) {
      return { ...subcategory, parent: category };
    }
  }
  return null;
};

export const getTotalProductsCount = (menuData) => {
  return menuData.reduce((total, category) => total + category.stats.total_products, 0);
};

export const getFeaturedProducts = (menuData, limit = 8) => {
  const allProducts = menuData.flatMap(category => category.featured_products);
  return allProducts.slice(0, limit);
};
