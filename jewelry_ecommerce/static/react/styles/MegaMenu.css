/* MegaMenu Styles */
.mega-menu-container {
  position: relative;
  z-index: 1000;
}

/* Header Styles */
.header-gradient {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.top-bar {
  background: #000;
  border-bottom: 1px solid #333;
}

.logo-icon {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.search-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.search-button {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.search-button:hover {
  transform: scale(1.05);
}

.header-icon {
  transition: all 0.3s ease;
}

.header-icon:hover {
  color: #d4af37;
  transform: translateY(-1px);
}

.cart-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Navigation Styles */
.nav-container {
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.menu-link {
  position: relative;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.menu-link:hover,
.menu-link.active {
  color: #d4af37;
  border-bottom-color: #d4af37;
}

.menu-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.05), rgba(184, 148, 31, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-link:hover::before {
  opacity: 1;
}

.chevron-icon {
  transition: transform 0.3s ease;
}

.chevron-icon.rotated {
  transform: rotate(180deg);
}

/* Mega Dropdown Styles */
.mega-dropdown {
  background: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0 0 12px 12px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Category Showcase */
.category-showcase {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  position: relative;
  overflow: hidden;
}

.category-showcase::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.category-image {
  border: 2px solid #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.category-showcase:hover .category-image {
  transform: scale(1.05);
}

.view-all-button {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
}

.view-all-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(212, 175, 55, 0.4);
}

/* Subcategory Styles */
.subcategory-title {
  color: #1f2937;
  border-bottom: 2px solid #d4af37;
  position: relative;
}

.subcategory-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background: #b8941f;
}

.subcategory-item {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.subcategory-item:hover {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  transform: translateX(5px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subcategory-icon {
  background: linear-gradient(135deg, #d4af37, #b8941f);
  box-shadow: 0 2px 4px rgba(212, 175, 55, 0.3);
  transition: transform 0.3s ease;
}

.subcategory-item:hover .subcategory-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Featured Products */
.featured-title {
  color: #1f2937;
  border-bottom: 2px solid #d4af37;
  position: relative;
}

.featured-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 30px;
  height: 2px;
  background: #b8941f;
}

.product-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
  transition: left 0.5s ease;
}

.product-card:hover::before {
  left: 100%;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #d4af37;
}

.product-image {
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.1);
}

.product-name {
  transition: color 0.3s ease;
}

.product-card:hover .product-name {
  color: #d4af37;
}

.product-price {
  color: #d4af37;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(212, 175, 55, 0.1);
}

/* Loading Styles */
.loading-spinner {
  border-color: #d4af37;
  border-top-color: transparent;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .mega-dropdown {
    width: 100vw;
    left: -2rem;
    border-radius: 0;
  }
  
  .dropdown-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-container {
    order: 3;
    width: 100%;
  }
  
  .menu-navigation {
    flex-direction: column;
  }
  
  .menu-link {
    width: 100%;
    justify-content: space-between;
    padding: 1rem;
  }
  
  .mega-dropdown {
    position: static;
    width: 100%;
    box-shadow: none;
    border: none;
    border-top: 1px solid #e5e7eb;
  }
  
  .category-showcase {
    text-align: left;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
}

/* Accessibility */
.menu-link:focus,
.subcategory-item:focus,
.product-card:focus {
  outline: 2px solid #d4af37;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .mega-menu-container {
    display: none;
  }
}
