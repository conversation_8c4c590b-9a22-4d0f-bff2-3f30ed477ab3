# -*- coding: utf-8 -*-
{
    'name': 'Jewelry eCommerce',
    'version': '********.0',
    'category': 'Sales/eCommerce',
    'summary': 'Complete jewelry ecommerce solution with REST API',
    'description': """
Jewelry eCommerce Module
========================

This module provides a complete jewelry ecommerce solution including:

Features:
---------
* Advanced product management for jewelry
* Flexible attribute system (material, size, color, gemstone)
* Dynamic product variants
* Brand and collection management
* Voucher and discount system
* Complete REST API for frontend integration
* Mobile-optimized interface

Technical Features:
------------------
* Product templates with jewelry-specific fields
* Advanced attribute configuration
* Variant generation engine
* Price calculation by attributes
* Image management system
* SEO optimization
* Performance optimized APIs

API Endpoints:
-------------
* /api/products - Product catalog
* /api/categories - Category hierarchy
* /api/vouchers - Discount system
* /api/brands - Brand management
* /api/search - Advanced search

Perfect for jewelry stores, online retailers, and ecommerce platforms.
    """,
    'author': 'Binhanjewelry Team',
    'website': 'https://binhanjewelry.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'product',
        'sale',
        'website',
        'website_sale',
        'stock',
        'loyalty',
        'website_sale_loyalty',
    ],
    'data': [
        # Views
        'views/loyalty_program_views.xml',
        # Loyalty sample data
        'data/loyalty_sample_data.xml',
        'data/loyalty_cards_sample.xml',
        # Enhanced jewelry data based on API analysis
        'data/enhanced_jewelry_data.xml',
        # Sample data for testing API
        'data/sample_data.xml',
        'data/sample_images.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'price': 0.00,
    'currency': 'USD',
    'support': '<EMAIL>',
    'external_dependencies': {
        'python': [],
    },
}
