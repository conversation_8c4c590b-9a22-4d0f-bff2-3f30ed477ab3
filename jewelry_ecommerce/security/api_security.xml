<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- API Access Group -->
        <record id="group_jewelry_api_user" model="res.groups">
            <field name="name">Jewelry API User</field>
            <field name="category_id" ref="base.module_category_technical"/>
            <field name="comment">Users who can access Jewelry API endpoints</field>
        </record>
        
        <!-- Public API Access (for website visitors) -->
        <record id="group_jewelry_api_public" model="res.groups">
            <field name="name">Jewelry API Public</field>
            <field name="category_id" ref="base.module_category_technical"/>
            <field name="comment">Public access to read-only Jewelry API endpoints</field>
        </record>
        
    </data>
</odoo>
