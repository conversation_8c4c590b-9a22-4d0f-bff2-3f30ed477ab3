# 🚀 JEWELRY ECOMMERCE - IMPLEMENTATION GUIDE

## 📋 OVERVIEW

Dựa trên phân tích code binhanjewe<PERSON>ry hiện tại, chúng ta sẽ tích hợp Shopping Cart và Checkout Process với Odoo backend, giữ nguyên UX/UI đã hoàn thiện.

---

## 🎯 IMPLEMENTATION STRATEGY

### **🔄 Approach: Progressive Integration**
1. **Keep existing frontend** - Giữ nguyên React components đã hoàn thiện
2. **Replace data layer** - Thay localStorage/static data bằng Odoo APIs  
3. **Enhance functionality** - Thêm features từ Odoo (variants, loyalty, etc.)
4. **Maintain UX** - Đảm bảo user experience không thay đổi

### **🏗️ Architecture:**
```
Frontend (React + Tailwind)
    ↓ API Calls
Odoo 18 Community + jewelry_ecommerce module
    ↓ Data Storage
PostgreSQL Database
```

---

## 🚀 PHASE 1: CART API INTEGRATION

### **📦 1.1 Create Cart Controller**

```python
# jewelry_ecommerce/controllers/api_cart.py
from odoo import http, fields
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class CartAPI(http.Controller):
    
    @http.route('/api/cart', methods=['GET'], auth='public', csrf=False, cors='*')
    def get_cart(self, **kwargs):
        """Get current cart items"""
        try:
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')
            
            # Find or create cart (draft sale.order)
            cart = self._get_or_create_cart(session_id)
            
            cart_data = {
                'cart_id': cart.id,
                'session_id': session_id,
                'items': self._format_cart_items(cart.order_line),
                'summary': {
                    'total_items': sum(line.product_uom_qty for line in cart.order_line),
                    'subtotal': cart.amount_untaxed,
                    'tax_amount': cart.amount_tax,
                    'total_amount': cart.amount_total
                }
            }
            
            return request.make_response(
                json.dumps({'success': True, 'data': cart_data}, ensure_ascii=False),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
            
        except Exception as e:
            _logger.error(f"Error getting cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
    
    @http.route('/api/cart/add', methods=['POST'], auth='public', csrf=False, cors='*')
    def add_to_cart(self, **kwargs):
        """Add product to cart"""
        try:
            data = json.loads(request.httprequest.data.decode('utf-8'))
            session_id = request.httprequest.headers.get('X-Session-ID', 'guest')
            
            product_id = data.get('product_id')
            variant_id = data.get('variant_id')
            quantity = data.get('quantity', 1)
            attributes = data.get('attributes', {})
            
            # Get or create cart
            cart = self._get_or_create_cart(session_id)
            
            # Add product to cart
            product = request.env['product.product'].sudo().browse(variant_id)
            if not product.exists():
                return request.make_response(
                    json.dumps({'success': False, 'error': 'Product not found'}),
                    status=404,
                    headers=[('Content-Type', 'application/json; charset=utf-8')]
                )
            
            # Check if item already exists in cart
            existing_line = cart.order_line.filtered(
                lambda l: l.product_id.id == variant_id
            )
            
            if existing_line:
                # Update quantity
                existing_line.product_uom_qty += quantity
            else:
                # Create new line
                cart.order_line.create({
                    'order_id': cart.id,
                    'product_id': variant_id,
                    'product_uom_qty': quantity,
                    'price_unit': product.list_price,
                })
            
            # Recalculate cart
            cart._compute_amount_all()
            
            return request.make_response(
                json.dumps({'success': True, 'message': 'Product added to cart'}),
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
            
        except Exception as e:
            _logger.error(f"Error adding to cart: {str(e)}")
            return request.make_response(
                json.dumps({'success': False, 'error': str(e)}),
                status=500,
                headers=[('Content-Type', 'application/json; charset=utf-8')]
            )
    
    def _get_or_create_cart(self, session_id):
        """Get existing cart or create new one"""
        SaleOrder = request.env['sale.order'].sudo()
        
        # Try to find existing cart
        cart = SaleOrder.search([
            ('state', '=', 'draft'),
            ('x_session_id', '=', session_id)
        ], limit=1)
        
        if not cart:
            # Create new cart
            cart = SaleOrder.create({
                'partner_id': request.env.ref('base.public_partner').id,
                'x_session_id': session_id,
                'state': 'draft'
            })
        
        return cart
    
    def _format_cart_items(self, order_lines):
        """Format cart items for frontend"""
        items = []
        for line in order_lines:
            item = {
                'id': line.id,
                'product_id': line.product_id.product_tmpl_id.id,
                'product_name': line.product_id.display_name,
                'variant_id': line.product_id.id,
                'quantity': int(line.product_uom_qty),
                'unit_price': line.price_unit,
                'total_price': line.price_subtotal,
                'attributes': self._get_product_attributes(line.product_id),
                'images': self._get_product_images(line.product_id)
            }
            items.append(item)
        return items
```

### **📱 1.2 Update Frontend CartContext**

```typescript
// src/contexts/CartContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { CartItem } from '@/types/cart';

interface CartContextType {
  cart: CartItem[];
  loading: boolean;
  sessionId: string;
  addToCart: (productId: number, variantId: number, quantity: number, attributes?: any) => Promise<void>;
  updateQuantity: (itemId: number, quantity: number) => Promise<void>;
  removeFromCart: (itemId: number) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [sessionId] = useState(() => {
    // Generate or get session ID
    let id = localStorage.getItem('cart_session_id');
    if (!id) {
      id = 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('cart_session_id', id);
    }
    return id;
  });

  // Load cart on mount
  useEffect(() => {
    refreshCart();
  }, []);

  const refreshCart = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/cart', {
        headers: {
          'X-Session-ID': sessionId
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setCart(data.data.items);
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId: number, variantId: number, quantity: number, attributes?: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': sessionId
        },
        body: JSON.stringify({
          product_id: productId,
          variant_id: variantId,
          quantity,
          attributes
        })
      });
      
      const data = await response.json();
      if (data.success) {
        await refreshCart(); // Reload cart
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateQuantity = async (itemId: number, quantity: number) => {
    setLoading(true);
    try {
      const response = await fetch('/api/cart/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': sessionId
        },
        body: JSON.stringify({
          item_id: itemId,
          quantity
        })
      });
      
      const data = await response.json();
      if (data.success) {
        await refreshCart();
      }
    } catch (error) {
      console.error('Error updating cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (itemId: number) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/cart/remove/${itemId}`, {
        method: 'DELETE',
        headers: {
          'X-Session-ID': sessionId
        }
      });
      
      const data = await response.json();
      if (data.success) {
        await refreshCart();
      }
    } catch (error) {
      console.error('Error removing from cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearCart = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/cart/clear', {
        method: 'DELETE',
        headers: {
          'X-Session-ID': sessionId
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setCart([]);
      }
    } catch (error) {
      console.error('Error clearing cart:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <CartContext.Provider value={{
      cart,
      loading,
      sessionId,
      addToCart,
      updateQuantity,
      removeFromCart,
      clearCart,
      refreshCart
    }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
```

---

## 🚀 PHASE 2: PRODUCT INTEGRATION

### **🎛️ 2.1 Update Product Detail Page**

```typescript
// src/pages/ProductDetailPage.tsx
import { useCart } from '@/contexts/CartContext';

const ProductDetailPage = ({ productId }: { productId: number }) => {
  const { addToCart, loading: cartLoading } = useCart();
  const [selectedAttributes, setSelectedAttributes] = useState<{[key: number]: number}>({});
  const [currentVariant, setCurrentVariant] = useState(null);
  const [quantity, setQuantity] = useState(1);

  const handleAttributeChange = async (attributeId: number, valueId: number) => {
    const newSelection = { ...selectedAttributes, [attributeId]: valueId };
    setSelectedAttributes(newSelection);
    
    // Get variant for selected attributes
    try {
      const response = await fetch(`/api/products/${productId}/variant`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          attribute_values: Object.values(newSelection),
          uom_id: 1
        })
      });
      
      const data = await response.json();
      if (data.success) {
        setCurrentVariant(data.data);
      }
    } catch (error) {
      console.error('Error getting variant:', error);
    }
  };

  const handleAddToCart = async () => {
    if (!currentVariant) {
      toast.error('Vui lòng chọn thuộc tính sản phẩm');
      return;
    }

    try {
      await addToCart(
        productId,
        currentVariant.variant_id,
        quantity,
        selectedAttributes
      );
      
      toast.success('Đã thêm vào giỏ hàng!');
    } catch (error) {
      toast.error('Có lỗi xảy ra khi thêm vào giỏ hàng');
    }
  };

  return (
    <div className="product-detail">
      {/* Product info */}
      
      {/* Attribute selection */}
      {product?.attributes.map(attr => (
        <div key={attr.id} className="attribute-group">
          <h3>{attr.display_name}</h3>
          <div className="attribute-values">
            {attr.values.map(value => (
              <button
                key={value.id}
                onClick={() => handleAttributeChange(attr.id, value.id)}
                className={selectedAttributes[attr.id] === value.id ? 'selected' : ''}
              >
                {value.name}
              </button>
            ))}
          </div>
        </div>
      ))}

      {/* Price display */}
      <div className="price">
        {currentVariant ? (
          <span>{formatPrice(currentVariant.final_price)}</span>
        ) : (
          <span>{formatPrice(product?.price)}</span>
        )}
      </div>

      {/* Add to cart */}
      <Button 
        onClick={handleAddToCart}
        disabled={cartLoading || (!currentVariant && product?.attributes.length > 0)}
      >
        {cartLoading ? 'Đang thêm...' : 'Thêm vào giỏ hàng'}
      </Button>
    </div>
  );
};
```

---

## 🎯 NEXT STEPS

### **📅 Implementation Order:**
1. **Week 1:** Cart API + Frontend integration
2. **Week 2:** Voucher system với Odoo loyalty
3. **Week 3:** Checkout process + Order creation
4. **Week 4:** Authentication system
5. **Week 5:** Testing + Polish

### **🔧 Development Setup:**
```bash
# 1. Update Odoo module
cp -r jewelry_ecommerce /path/to/odoo/addons/

# 2. Add cart session field to sale.order
# 3. Restart Odoo
# 4. Update module

# 5. Test APIs
curl "https://your-domain.com/api/cart" -H "X-Session-ID: test123"
```

**🎯 GOAL: Seamless integration giữa binhanjewelry frontend và Odoo backend, maintaining excellent UX! 💎✨**
