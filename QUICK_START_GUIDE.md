# 🚀 JEWELRY ECOMMERCE API - QUICK START GUIDE

## 🎯 5-MINUTE SETUP

### **1. 📋 Prerequisites**
```bash
# Check requirements
docker --version          # Docker 20.10+
docker-compose --version  # Docker Compose 1.29+
curl --version            # For API testing
```

### **2. 🔧 Installation**
```bash
# Clone the module (if you have the files)
mkdir jewelry_ecommerce
cd jewelry_ecommerce

# Copy module to Odoo addons
cp -r jewelry_ecommerce /path/to/odoo/addons/

# Restart Odoo container
docker restart your-odoo-container
```

### **3. ✅ Activate Module**
1. Open Odoo web interface
2. Go to **Apps** menu
3. Search for **"jewelry_ecommerce"**
4. Click **Install**
5. Wait for installation to complete

### **4. 🧪 Test API**
```bash
# Test basic endpoint
curl "https://your-domain.com/api/products"

# Should return JSON with products list
```

---

## 🎮 INTERACTIVE TESTING

### **📦 Products API Examples:**

```bash
# 1. Get all products (page 1, 5 items)
curl "https://noithat.erpcloud.vn/api/products?page=1&per_page=5"

# 2. Get product with attributes (Ring with sizes)
curl "https://noithat.erpcloud.vn/api/products/13"

# 3. Search products
curl "https://noithat.erpcloud.vn/api/products?search=nhẫn"

# 4. Filter by category
curl "https://noithat.erpcloud.vn/api/products?category=1"

# 5. Sort by price descending
curl "https://noithat.erpcloud.vn/api/products?sort=list_price&order=desc"
```

### **🎛️ Variant Selection:**

```bash
# Select ring size 14 (attribute value ID = 1)
curl -X POST "https://noithat.erpcloud.vn/api/products/13/variant" \
  -H "Content-Type: application/json" \
  -d '{
    "attribute_values": [1],
    "uom_id": 1
  }'
```

### **🏷️ Categories API:**

```bash
# Get all categories
curl "https://noithat.erpcloud.vn/api/categories"

# Get mega menu data
curl "https://noithat.erpcloud.vn/api/categories/mega-menu"

# Get products in category
curl "https://noithat.erpcloud.vn/api/categories/1/products"
```

---

## 💻 FRONTEND INTEGRATION

### **⚛️ React Example:**

```jsx
import React, { useState, useEffect } from 'react';

const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async (page = 1) => {
    try {
      const response = await fetch(
        `https://noithat.erpcloud.vn/api/products?page=${page}&per_page=12`
      );
      const data = await response.json();
      
      if (data.success) {
        setProducts(data.data.products);
        setPagination(data.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="product-list">
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.map(product => (
          <div key={product.id} className="product-card">
            <img 
              src={product.main_image || '/placeholder.jpg'} 
              alt={product.name}
              className="w-full h-48 object-cover"
            />
            <h3 className="font-semibold mt-2">{product.name}</h3>
            <p className="text-blue-600 font-bold">
              {product.price.toLocaleString()}₫
            </p>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="pagination mt-8 flex justify-center space-x-2">
          <button 
            disabled={!pagination.has_prev}
            onClick={() => fetchProducts(pagination.prev_page)}
            className="px-4 py-2 bg-gray-200 rounded disabled:opacity-50"
          >
            ← Trước
          </button>
          
          <span className="px-4 py-2">
            Trang {pagination.current_page} / {pagination.total_pages}
          </span>
          
          <button 
            disabled={!pagination.has_next}
            onClick={() => fetchProducts(pagination.next_page)}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            Tiếp →
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductList;
```

### **🎛️ Product Detail with Variants:**

```jsx
const ProductDetail = ({ productId }) => {
  const [product, setProduct] = useState(null);
  const [selectedAttributes, setSelectedAttributes] = useState({});
  const [currentVariant, setCurrentVariant] = useState(null);

  const handleAttributeChange = async (attributeId, valueId) => {
    const newSelection = { ...selectedAttributes, [attributeId]: valueId };
    setSelectedAttributes(newSelection);

    // Get variant for selected attributes
    try {
      const response = await fetch(`/api/products/${productId}/variant`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          attribute_values: Object.values(newSelection),
          uom_id: 1
        })
      });
      
      const data = await response.json();
      if (data.success) {
        setCurrentVariant(data.data);
      }
    } catch (error) {
      console.error('Error updating variant:', error);
    }
  };

  return (
    <div className="product-detail">
      <h1>{product?.name}</h1>
      
      {/* Price Display */}
      <div className="price">
        {currentVariant ? (
          <span className="text-2xl font-bold text-blue-600">
            {currentVariant.final_price.toLocaleString()}₫
          </span>
        ) : (
          <span className="text-2xl font-bold">
            {product?.price.toLocaleString()}₫
          </span>
        )}
      </div>

      {/* Attribute Selection */}
      {product?.attributes.map(attr => (
        <div key={attr.id} className="attribute-group mt-4">
          <h3 className="font-semibold">{attr.display_name}</h3>
          <div className="flex space-x-2 mt-2">
            {attr.values.map(value => (
              <button
                key={value.id}
                onClick={() => handleAttributeChange(attr.id, value.id)}
                className={`px-4 py-2 border rounded ${
                  selectedAttributes[attr.id] === value.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-white'
                }`}
              >
                {value.name}
              </button>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
```

---

## 🔍 DEBUGGING TIPS

### **1. 📊 Check API Response:**
```bash
# Pretty print JSON response
curl "https://noithat.erpcloud.vn/api/products/13" | python3 -m json.tool
```

### **2. 🐛 Debug Variant Selection:**
```bash
# Use debug endpoint to see available variants
curl "https://noithat.erpcloud.vn/api/products/13/variant-test?attribute_values=1"
```

### **3. 📝 Check Logs:**
```bash
# Check Odoo logs
docker logs your-odoo-container | grep "jewelry_ecommerce"
```

### **4. 🔧 Verify Module Installation:**
```bash
# Check if module is installed
curl "https://noithat.erpcloud.vn/api/products" | grep "success"
```

---

## 🎯 COMMON USE CASES

### **1. 🛍️ Product Catalog Page:**
```javascript
// Fetch products with pagination and filters
const fetchProducts = async (filters = {}) => {
  const params = new URLSearchParams({
    page: filters.page || 1,
    per_page: filters.per_page || 12,
    category: filters.category || '',
    search: filters.search || '',
    sort: filters.sort || 'name',
    order: filters.order || 'asc'
  });

  const response = await fetch(`/api/products?${params}`);
  return response.json();
};
```

### **2. 🎛️ Product Configuration:**
```javascript
// Handle attribute selection and price updates
const updateProductVariant = async (productId, attributes, uomId) => {
  const response = await fetch(`/api/products/${productId}/variant`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      attribute_values: attributes,
      uom_id: uomId
    })
  });
  
  return response.json();
};
```

### **3. 🏷️ Category Navigation:**
```javascript
// Build mega menu from categories
const buildMegaMenu = async () => {
  const response = await fetch('/api/categories/mega-menu');
  const data = await response.json();
  
  return data.success ? data.data.categories : [];
};
```

---

## 🚨 TROUBLESHOOTING

### **❌ Common Issues:**

1. **Module not found:**
   ```bash
   # Restart Odoo and check logs
   docker restart odoo-container
   docker logs odoo-container
   ```

2. **API returns 404:**
   ```bash
   # Check if module is installed
   # Go to Odoo Apps → Search "jewelry_ecommerce"
   ```

3. **CORS errors:**
   ```javascript
   // API already has CORS enabled
   // Check browser console for specific errors
   ```

4. **Empty product list:**
   ```bash
   # Check if sample data is loaded
   curl "https://your-domain.com/api/products/sample"
   ```

---

## 🎉 SUCCESS CHECKLIST

- [ ] ✅ Odoo 18 running
- [ ] ✅ Module installed
- [ ] ✅ API responds with products
- [ ] ✅ Pagination working
- [ ] ✅ Variant selection working
- [ ] ✅ Categories loading
- [ ] ✅ Frontend integration complete

---

**🚀 YOU'RE READY TO BUILD AMAZING JEWELRY ECOMMERCE! 💎✨**
