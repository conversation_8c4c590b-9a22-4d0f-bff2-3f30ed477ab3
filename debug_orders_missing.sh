#!/bin/bash

# 🔍 DEBUG: Why Orders Are Missing in Odoo
# Comprehensive debugging for order creation issues

echo "🔍 DEBUGGING: Missing Orders in Odoo"
echo "==================================="

BASE_URL="https://noithat.erpcloud.vn/api"
SESSION_ID="debug_orders_$(date +%s)"

echo "🆔 Debug Session: $SESSION_ID"
echo ""

# ========================================
# 1. TEST API CONNECTIVITY
# ========================================
echo "🌐 1. TESTING API CONNECTIVITY"
echo "=============================="

echo "   📡 Testing basic API response..."
API_TEST=$(curl -s -m 10 "$BASE_URL/products" 2>/dev/null)

if echo "$API_TEST" | jq -e '.success' >/dev/null 2>&1; then
    echo "   ✅ API is responding"
    PRODUCT_COUNT=$(echo "$API_TEST" | jq '.data.products | length')
    echo "   📦 Products available: $PRODUCT_COUNT"
else
    echo "   ❌ API not responding properly"
    echo "   📄 Response: $API_TEST"
    echo ""
    echo "🚨 ISSUE: API connectivity problem"
    echo "   → Check server status"
    echo "   → Try again when server is stable"
    exit 1
fi

echo ""

# ========================================
# 2. TEST CART FUNCTIONALITY
# ========================================
echo "🛒 2. TESTING CART FUNCTIONALITY"
echo "================================"

echo "   📥 Getting empty cart..."
EMPTY_CART=$(curl -s -m 10 "$BASE_URL/cart/get" \
  -H "X-Session-ID: $SESSION_ID" 2>/dev/null)

if echo "$EMPTY_CART" | jq -e '.success' >/dev/null 2>&1; then
    echo "   ✅ Cart API working"
else
    echo "   ❌ Cart API failed"
    echo "   📄 Response: $EMPTY_CART"
fi

echo "   ➕ Adding product to cart..."
if [ "$PRODUCT_COUNT" -gt 0 ]; then
    FIRST_PRODUCT_ID=$(echo "$API_TEST" | jq -r '.data.products[0].id')
    
    ADD_CART=$(curl -s -m 10 -X POST "$BASE_URL/cart/add" \
      -H "Content-Type: application/json" \
      -H "X-Session-ID: $SESSION_ID" \
      -d "{\"product_id\": $FIRST_PRODUCT_ID, \"quantity\": 1}" 2>/dev/null)
    
    if echo "$ADD_CART" | jq -e '.success' >/dev/null 2>&1; then
        echo "   ✅ Product added to cart"
        CART_TOTAL=$(echo "$ADD_CART" | jq -r '.data.cart.total_amount')
        echo "   💰 Cart total: $CART_TOTAL VND"
    else
        echo "   ❌ Failed to add product to cart"
        echo "   📄 Response: $ADD_CART"
    fi
fi

echo ""

# ========================================
# 3. TEST ORDER CREATION WITH DETAILED LOGGING
# ========================================
echo "💳 3. TESTING ORDER CREATION (DETAILED)"
echo "======================================"

echo "   📋 Attempting to create order..."

# Create order with detailed error handling
ORDER_RESPONSE=$(curl -s -m 20 -X POST "$BASE_URL/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer_info": {
      "full_name": "Debug Test Customer",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "123 Debug Street, Test City"
    },
    "payment_method": "cod",
    "notes": "Debug order - checking why orders missing in Odoo"
  }' 2>/dev/null)

echo "   📄 Full checkout response:"
echo "$ORDER_RESPONSE" | jq '.' 2>/dev/null || echo "$ORDER_RESPONSE"

if echo "$ORDER_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo ""
    echo "   🎉 ORDER CREATION SUCCESSFUL!"
    
    ORDER_ID=$(echo "$ORDER_RESPONSE" | jq -r '.data.order.id // "Unknown"')
    ORDER_NAME=$(echo "$ORDER_RESPONSE" | jq -r '.data.order.name // "Unknown"')
    ORDER_STATE=$(echo "$ORDER_RESPONSE" | jq -r '.data.order.state // "Unknown"')
    ORDER_TOTAL=$(echo "$ORDER_RESPONSE" | jq -r '.data.order.total_amount // 0')
    
    echo "   📊 Order Details:"
    echo "      - ID: $ORDER_ID"
    echo "      - Name: $ORDER_NAME"
    echo "      - State: $ORDER_STATE"
    echo "      - Total: $ORDER_TOTAL VND"
    
    # Save order details for investigation
    echo "$ORDER_RESPONSE" > "/tmp/debug_order_response.json"
    echo "   💾 Order response saved to /tmp/debug_order_response.json"
    
else
    echo ""
    echo "   ❌ ORDER CREATION FAILED"
    
    # Check for specific error messages
    if echo "$ORDER_RESPONSE" | grep -q "error"; then
        ERROR_MSG=$(echo "$ORDER_RESPONSE" | jq -r '.error // .message // "Unknown error"')
        echo "   🚨 Error: $ERROR_MSG"
    fi
    
    if echo "$ORDER_RESPONSE" | grep -q "cart"; then
        echo "   🔍 Issue may be cart-related"
    fi
    
    if echo "$ORDER_RESPONSE" | grep -q "customer"; then
        echo "   🔍 Issue may be customer data related"
    fi
    
    # Save error response for investigation
    echo "$ORDER_RESPONSE" > "/tmp/debug_order_error.json"
    echo "   💾 Error response saved to /tmp/debug_order_error.json"
fi

echo ""

# ========================================
# 4. CHECK ODOO BACKEND LOCATIONS
# ========================================
echo "🏢 4. ODOO BACKEND CHECK INSTRUCTIONS"
echo "===================================="

echo "   📋 Check these locations in Odoo backend:"
echo ""
echo "   1️⃣ SALES ORDERS:"
echo "      → Sales → Orders → Sales Orders"
echo "      → Filter: All states (draft, sent, confirmed, etc.)"
echo "      → Check date range: Today"
echo ""
echo "   2️⃣ QUOTATIONS:"
echo "      → Sales → Orders → Quotations"
echo "      → May be created as quotations first"
echo ""
echo "   3️⃣ DIFFERENT COMPANIES:"
echo "      → Switch company in top-right corner"
echo "      → Check if orders in different company"
echo ""
echo "   4️⃣ TECHNICAL VIEW:"
echo "      → Settings → Technical → Database Structure → Models"
echo "      → Search: sale.order"
echo "      → View records directly"
echo ""
echo "   5️⃣ LOGS:"
echo "      → Settings → Technical → Logging"
echo "      → Check for errors during order creation"

echo ""

# ========================================
# 5. GENERATE ODOO SEARCH QUERIES
# ========================================
echo "🔍 5. ODOO SEARCH QUERIES"
echo "========================"

echo "   📝 Use these search filters in Odoo:"
echo ""
echo "   🔍 By Customer Email:"
echo "      partner_id.email = '<EMAIL>'"
echo ""
echo "   🔍 By Order Date:"
echo "      create_date >= '$(date +%Y-%m-%d) 00:00:00'"
echo ""
echo "   🔍 By Order Notes:"
echo "      note ilike '%Debug order%'"
echo ""
echo "   🔍 By State:"
echo "      state in ('draft', 'sent', 'sale', 'done', 'cancel')"
echo ""
echo "   🔍 All Recent Orders:"
echo "      create_date >= '$(date -d '1 hour ago' +%Y-%m-%d %H:%M:%S)'"

echo ""

# ========================================
# 6. API ENDPOINT VERIFICATION
# ========================================
echo "🔧 6. API ENDPOINT VERIFICATION"
echo "==============================="

echo "   📡 Testing different order endpoints..."

# Test if there's a different orders endpoint
echo "   🔍 Checking /api/orders endpoint..."
ORDERS_LIST=$(curl -s -m 10 "$BASE_URL/orders" 2>/dev/null)
if echo "$ORDERS_LIST" | jq -e '.success' >/dev/null 2>&1; then
    ORDER_COUNT=$(echo "$ORDERS_LIST" | jq '.data | length // 0')
    echo "   ✅ Found orders endpoint with $ORDER_COUNT orders"
else
    echo "   ❌ No orders endpoint or empty"
fi

# Test sales orders endpoint
echo "   🔍 Checking /api/sales/orders endpoint..."
SALES_ORDERS=$(curl -s -m 10 "$BASE_URL/sales/orders" 2>/dev/null)
if echo "$SALES_ORDERS" | jq -e '.success' >/dev/null 2>&1; then
    SALES_COUNT=$(echo "$SALES_ORDERS" | jq '.data | length // 0')
    echo "   ✅ Found sales orders endpoint with $SALES_COUNT orders"
else
    echo "   ❌ No sales orders endpoint"
fi

echo ""

# ========================================
# 7. SUMMARY AND RECOMMENDATIONS
# ========================================
echo "📊 7. SUMMARY AND RECOMMENDATIONS"
echo "================================="

echo "   🎯 Debugging Results:"
if echo "$ORDER_RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo "      ✅ API order creation: SUCCESS"
    echo "      🔍 Issue: Orders created but not visible in Odoo"
    echo ""
    echo "   💡 Possible Causes:"
    echo "      1. Orders in different company"
    echo "      2. Orders in draft state (hidden by default filter)"
    echo "      3. Database sync delay"
    echo "      4. Permission/access issues"
    echo ""
    echo "   🔧 Next Steps:"
    echo "      1. Check Odoo backend with filters above"
    echo "      2. Switch companies in Odoo"
    echo "      3. Check technical view: sale.order model"
    echo "      4. Look for order: $ORDER_NAME"
else
    echo "      ❌ API order creation: FAILED"
    echo "      🔍 Issue: Orders not being created at all"
    echo ""
    echo "   💡 Possible Causes:"
    echo "      1. API endpoint bug"
    echo "      2. Database connection issues"
    echo "      3. Validation errors"
    echo "      4. Server configuration problems"
    echo ""
    echo "   🔧 Next Steps:"
    echo "      1. Check API logs for errors"
    echo "      2. Verify database connectivity"
    echo "      3. Test with simpler order data"
    echo "      4. Check Odoo server logs"
fi

echo ""
echo "📁 Debug files created:"
echo "   - /tmp/debug_order_response.json (if successful)"
echo "   - /tmp/debug_order_error.json (if failed)"

echo ""
echo "🎯 DEBUG COMPLETED!"
echo "Check Odoo backend with the instructions above."
