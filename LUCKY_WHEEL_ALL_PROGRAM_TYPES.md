# 🎰 LUCKY WHEEL - ALL ODOO PROGRAM TYPES SUPPORT

## 🎯 **OVERVIEW**

Lucky Wheel sẽ hỗ trợ TẤT CẢ 8 loại loyalty program của Odoo 18:

1. **🎫 Coupons** - Phiếu giảm giá
2. **🎁 Gift Card** - Thẻ quà tặng  
3. **⭐ Loyalty** - Thẻ khách hàng thân thiết
4. **🔥 Promotion** - Khuyến mãi
5. **💳 eWallet** - <PERSON><PERSON> điện tử
6. **🏷️ Promo Code** - Mã giảm giá
7. **🛒 Buy X Get Y** - Mua X Tặng Y
8. **📅 Next Order Coupons** - Phiếu giảm giá đơn hàng tiếp theo

---

## 🎫 **1. COUPONS - Phiếu giảm giá**

### **🔧 Characteristics:**
- **Trigger:** `with_code` (cần nhập mã)
- **Applies_on:** `current` (dùng ngay)
- **Usage:** Single-use, một lần duy nhất

### **🎰 Lucky Wheel Implementation:**
```python
# Admin tạo program
program = {
    'name': 'Lucky Coupon - Giảm 15%',
    'program_type': 'coupons',
    'trigger': 'with_code',
    'applies_on': 'current',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'minimum_amount': 100000,  # Tối thiểu 100k
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 15,
        'discount_applicability': 'order',
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Giảm 15%",
        "type": "coupon",
        "description": "Phiếu giảm giá 15% cho đơn từ 100k"
    },
    "voucher_code": "044a-1234-5678",  # Code từ loyalty.card
    "usage": "Nhập mã khi checkout, chỉ dùng 1 lần"
}
```

---

## 🎁 **2. GIFT CARD - Thẻ quà tặng**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `future` (dùng sau)
- **Usage:** Có giá trị tiền cố định

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Gift Card - 50k',
    'program_type': 'gift_card',
    'trigger': 'auto',
    'applies_on': 'future',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'reward_point_amount': 1,
        'reward_point_mode': 'money',
        'reward_point_split': True,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'per_point',
        'discount': 1,  # 1 point = 1 VND
        'required_points': 50000,  # 50k points = 50k VND
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Thẻ quà tặng 50k",
        "type": "gift_card",
        "value": 50000,
        "description": "Thẻ quà tặng trị giá 50.000đ"
    },
    "voucher_code": "044b-2345-6789",
    "usage": "Dùng cho đơn hàng bất kỳ, trừ dần giá trị"
}
```

---

## ⭐ **3. LOYALTY - Thẻ khách hàng thân thiết**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `both` (hiện tại & tương lai)
- **Usage:** Tích điểm, đổi thưởng

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Loyalty - 100 điểm',
    'program_type': 'loyalty',
    'trigger': 'auto',
    'applies_on': 'both',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'reward_point_mode': 'money',
        'reward_point_amount': 1,  # 1 VND = 1 point
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 5,  # Giảm 5%
        'required_points': 100,  # Cần 100 điểm
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "100 điểm thưởng",
        "type": "loyalty_points",
        "value": 100,
        "description": "Nhận 100 điểm, đổi được giảm 5%"
    },
    "voucher_code": "044c-3456-7890",
    "usage": "Tích lũy điểm, dùng khi đủ 100 điểm"
}
```

---

## 🔥 **4. PROMOTION - Khuyến mãi**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `current` (dùng ngay)
- **Usage:** Tự động áp dụng khi đủ điều kiện

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Promotion - Giảm 20%',
    'program_type': 'promotion',
    'trigger': 'auto',
    'applies_on': 'current',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'mode': 'auto',
        'minimum_amount': 200000,  # Tối thiểu 200k
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 20,
        'discount_applicability': 'order',
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Khuyến mãi 20%",
        "type": "promotion",
        "description": "Tự động giảm 20% cho đơn từ 200k"
    },
    "voucher_code": null,  # Không cần code, tự động áp dụng
    "usage": "Tự động giảm khi đơn hàng đủ điều kiện"
}
```

---

## 💳 **5. EWALLET - Ví điện tử**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `future` (dùng sau)
- **Usage:** Nạp tiền vào ví, dùng dần

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky eWallet - 30k',
    'program_type': 'ewallet',
    'trigger': 'auto',
    'applies_on': 'future',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'reward_point_amount': 1,
        'reward_point_mode': 'money',
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'per_point',
        'discount': 1,
        'required_points': 30000,  # 30k trong ví
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "eWallet 30k",
        "type": "ewallet",
        "value": 30000,
        "description": "Nạp 30.000đ vào ví điện tử"
    },
    "voucher_code": "044d-4567-8901",
    "usage": "Dùng dần trong các đơn hàng tiếp theo"
}
```

---

## 🏷️ **6. PROMO CODE - Mã giảm giá**

### **🔧 Characteristics:**
- **Trigger:** `with_code` (cần nhập mã)
- **Applies_on:** `current` (dùng ngay)
- **Usage:** Giảm giá sản phẩm cụ thể

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Promo - Giảm 100k cho jewelry',
    'program_type': 'promo_code',
    'trigger': 'with_code',
    'applies_on': 'current',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'mode': 'with_code',
        'product_category_id': jewelry_category.id,
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'fixed_amount',
        'discount_fixed_amount': 100000,
        'discount_applicability': 'specific',
        'discount_product_category_id': jewelry_category.id,
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Giảm 100k cho jewelry",
        "type": "promo_code",
        "value": 100000,
        "description": "Giảm 100k cho sản phẩm jewelry"
    },
    "voucher_code": "044e-5678-9012",
    "usage": "Nhập mã khi mua jewelry"
}
```

---

## 🛒 **7. BUY X GET Y - Mua X Tặng Y**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `current` (dùng ngay)
- **Usage:** Mua đủ số lượng → Tặng sản phẩm

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Buy 2 Get 1',
    'program_type': 'buy_x_get_y',
    'trigger': 'auto',
    'applies_on': 'current',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'mode': 'auto',
        'minimum_qty': 2,  # Mua tối thiểu 2
        'reward_point_amount': 1,
        'reward_point_mode': 'unit',
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'product',
        'reward_product_id': gift_product.id,  # Sản phẩm tặng
        'reward_product_qty': 1,  # Tặng 1 cái
        'required_points': 2,  # Cần 2 điểm (mua 2)
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Mua 2 Tặng 1",
        "type": "buy_x_get_y",
        "description": "Mua 2 sản phẩm, tặng 1 sản phẩm"
    },
    "voucher_code": "044f-6789-0123",
    "usage": "Tự động tặng khi mua đủ 2 sản phẩm"
}
```

---

## 📅 **8. NEXT ORDER COUPONS - Phiếu giảm giá đơn hàng tiếp theo**

### **🔧 Characteristics:**
- **Trigger:** `auto` (tự động)
- **Applies_on:** `future` (dùng sau)
- **Usage:** Đơn hiện tại → Coupon cho đơn sau

### **🎰 Lucky Wheel Implementation:**
```python
program = {
    'name': 'Lucky Next Order - Giảm 25%',
    'program_type': 'next_order_coupons',
    'trigger': 'auto',
    'applies_on': 'future',
    'is_lucky_wheel': True,
    'rule_ids': [(0, 0, {
        'mode': 'auto',
        'minimum_amount': 150000,  # Đơn hiện tại từ 150k
    })],
    'reward_ids': [(0, 0, {
        'reward_type': 'discount',
        'discount_mode': 'percent',
        'discount': 25,
        'discount_applicability': 'order',
    })],
}

# Lucky Wheel trả về
{
    "prize": {
        "name": "Giảm 25% đơn tiếp theo",
        "type": "next_order_coupon",
        "description": "Coupon giảm 25% cho lần mua sau"
    },
    "voucher_code": "044g-7890-1234",
    "usage": "Dùng cho đơn hàng tiếp theo"
}
```

---

## 🎰 **LUCKY WHEEL API INTEGRATION**

### **🔧 Universal Prize Structure:**
```json
{
  "success": true,
  "data": {
    "prize": {
      "id": 123,
      "name": "Prize Name",
      "type": "program_type",
      "value": 0,
      "program_id": 123,
      "program_type": "coupons|gift_card|loyalty|promotion|ewallet|promo_code|buy_x_get_y|next_order_coupons",
      "description": "Prize description",
      "usage_instructions": "How to use this prize",
      "color": "#FFD700",
      "icon": "🎁"
    },
    "voucher_code": "044x-xxxx-xxxx",  // null for auto-apply types
    "spin_time": "2025-05-26T16:00:00",
    "spins_remaining": 9,
    "program_details": {
      "trigger": "auto|with_code",
      "applies_on": "current|future|both",
      "minimum_amount": 100000,
      "expiry_date": "2025-12-31"
    }
  }
}
```

### **🎯 Implementation Strategy:**
1. **Admin creates programs** với `is_lucky_wheel = True`
2. **Lucky Wheel API** lấy tất cả 8 loại programs
3. **Random selection** equal chance cho tất cả
4. **Generate appropriate response** theo program type
5. **Handle voucher codes** - Có code hoặc auto-apply

---

**🎯 Với strategy này, Lucky Wheel sẽ hỗ trợ TOÀN BỘ ecosystem loyalty của Odoo! 💎✨**
