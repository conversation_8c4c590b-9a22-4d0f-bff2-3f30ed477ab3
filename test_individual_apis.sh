#!/bin/bash

# 🧪 Individual API Tests for Cart & Checkout

BASE_URL="https://noithat.erpcloud.vn"
SESSION_ID="test_$(date +%s)"

echo "🚀 Testing Individual APIs"
echo "🔑 Session ID: $SESSION_ID"
echo "=================================="

# Test 1: Get empty cart
echo "1️⃣ Testing: Get Empty Cart"
curl -s -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart"
echo -e "\n"

# Test 2: Add product to cart
echo "2️⃣ Testing: Add Product to Cart"
curl -s -X POST "$BASE_URL/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "product_id": 13,
    "variant_id": 23,
    "quantity": 1
  }'
echo -e "\n"

# Test 3: Get cart with items
echo "3️⃣ Testing: Get Cart with Items"
curl -s -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart"
echo -e "\n"

# Test 4: Get payment methods
echo "4️⃣ Testing: Get Payment Methods"
curl -s "$BASE_URL/api/checkout/payment-methods"
echo -e "\n"

# Test 5: Create order
echo "5️⃣ Testing: Create Order"
curl -s -X POST "$BASE_URL/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{
    "customer": {
      "full_name": "Test User",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "address": "Test Address"
    },
    "payment_method": "cod"
  }'
echo -e "\n"

# Test 6: Track orders
echo "6️⃣ Testing: Track Orders"
curl -s "$BASE_URL/api/orders/track?email=<EMAIL>"
echo -e "\n"

echo "✅ Individual tests completed!"
echo "🔑 Session used: $SESSION_ID"
