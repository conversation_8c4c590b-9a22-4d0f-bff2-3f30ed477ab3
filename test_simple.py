#!/usr/bin/env python3
import requests
import json
import time

BASE_URL = "https://noithat.erpcloud.vn"
SESSION_ID = f"test_{int(time.time())}"

def test_api(name, method, url, data=None, headers=None):
    """Test API endpoint"""
    print(f"\n🧪 Testing: {name}")
    print(f"📋 {method} {url}")
    
    if headers is None:
        headers = {}
    
    # Add session ID header for cart operations
    if '/api/cart' in url or '/api/checkout' in url:
        headers['X-Session-ID'] = SESSION_ID
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=10)
        elif method == 'POST':
            headers['Content-Type'] = 'application/json'
            response = requests.post(url, headers=headers, data=json.dumps(data) if data else None, timeout=10)
        elif method == 'PUT':
            headers['Content-Type'] = 'application/json'
            response = requests.put(url, headers=headers, data=json.dumps(data) if data else None, timeout=10)
        elif method == 'DELETE':
            response = requests.delete(url, headers=headers, timeout=10)
        
        print(f"📊 Status: {response.status_code}")
        
        try:
            result = response.json()
            if result.get('success'):
                print(f"✅ SUCCESS: {result.get('message', 'OK')}")
                if 'data' in result:
                    # Print key info only
                    data = result['data']
                    if isinstance(data, dict):
                        for key in ['cart_id', 'order_id', 'total_amount', 'total_items']:
                            if key in data:
                                print(f"   {key}: {data[key]}")
            else:
                print(f"❌ ERROR: {result.get('error', 'Unknown error')}")
        except:
            print(f"📄 Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"💥 EXCEPTION: {str(e)}")

def main():
    print("🚀 Starting Simple API Tests")
    print(f"🔑 Session ID: {SESSION_ID}")
    print("=" * 50)
    
    # Test 1: Get empty cart
    test_api("Get Empty Cart", "GET", f"{BASE_URL}/api/cart")
    
    # Test 2: Add product to cart
    test_api("Add Product to Cart", "POST", f"{BASE_URL}/api/cart/add", {
        "product_id": 13,
        "variant_id": 23,
        "quantity": 1
    })
    
    # Test 3: Get cart with items
    test_api("Get Cart with Items", "GET", f"{BASE_URL}/api/cart")
    
    # Test 4: Get payment methods
    test_api("Get Payment Methods", "GET", f"{BASE_URL}/api/checkout/payment-methods")
    
    # Test 5: Create order
    test_api("Create Order", "POST", f"{BASE_URL}/api/checkout/create-order", {
        "customer": {
            "full_name": "Test Customer",
            "email": "<EMAIL>",
            "phone": "0123456789",
            "address": "123 Test Street"
        },
        "payment_method": "cod"
    })
    
    # Test 6: Track orders
    test_api("Track Orders", "GET", f"{BASE_URL}/api/orders/track?email=<EMAIL>")
    
    print("\n" + "=" * 50)
    print("✅ Simple tests completed!")
    print(f"🔑 Session used: {SESSION_ID}")

if __name__ == "__main__":
    main()
