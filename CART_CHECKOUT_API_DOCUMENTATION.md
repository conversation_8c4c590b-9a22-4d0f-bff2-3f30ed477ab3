# 🛒 CART & CHECKOUT APIs DOCUMENTATION

## 📋 **TỔNG QUAN**

Hệ thống Cart & Checkout APIs được xây dựng trên nền tảng Odoo 18 Community Edition, tận dụng tối đa các tính năng có sẵn của Odoo để cung cấp một giải pháp eCommerce hoàn chỉnh cho ngành trang sức.

### **🎯 Đặc điểm chính:**
- **Session-based Cart:** Hỗ trợ guest users không cần đăng nhập
- **Native Odoo Integration:** Sử dụng 100% Odoo's built-in methods
- **Universal Loyalty Support:** Hỗ trợ tất cả 8 loại loyalty programs của Odoo
- **Real-time Calculation:** Tự động tính toán giá, thuế, khuyến mại
- **RESTful APIs:** Chuẩn REST với JSON response format

---

## 🛒 **CART APIs**

### **Base URL:** `https://noithat.erpcloud.vn/api/cart`

### **Authentication:**
- **Type:** Session-based (Guest users)
- **Header:** `X-Session-ID: your_session_id`

---

### **1. GET /api/cart - Lấy thông tin giỏ hàng**

#### **Request:**
```bash
curl -H "X-Session-ID: session123" "https://noithat.erpcloud.vn/api/cart"
```

#### **Response:**
```json
{
  "success": true,
  "data": {
    "cart_id": 1,
    "session_id": "session123",
    "items": [
      {
        "id": 1,
        "product_id": 19,
        "product_name": "Mặt dây chuyền Vàng trắng 14K đính đá Topaz PNJ TPXMW000445 (Kích thước 14)",
        "variant_id": 32,
        "quantity": 1,
        "unit_price": 5883000.0,
        "total_price": 5883000.0,
        "currency": "USD",
        "attributes": [
          {
            "attribute_name": "Chọn size",
            "value_name": "Kích thước 14",
            "attribute_id": 1,
            "value_id": 1
          }
        ],
        "images": [],
        "product_url": "/products/19"
      }
    ],
    "summary": {
      "total_items": 1.0,
      "subtotal": 5883000.0,
      "tax_amount": 882450.0,
      "total_amount": 6765450.0,
      "currency": "USD"
    }
  }
}
```

---

### **2. POST /api/cart/add - Thêm sản phẩm vào giỏ hàng**

#### **Request:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: session123" \
  -d '{
    "product_id": 19,
    "variant_id": 32,
    "quantity": 1,
    "attributes": {
      "size_id": 1,
      "size_name": "Kích thước 14"
    }
  }'
```

#### **Parameters:**
- **product_id** (required): ID của product template
- **variant_id** (required): ID của product variant
- **quantity** (required): Số lượng sản phẩm
- **attributes** (optional): Thông tin attributes đã chọn

#### **Response:**
```json
{
  "success": true,
  "message": "Product added to cart",
  "data": {
    "cart_id": 1,
    "total_items": 1.0,
    "total_amount": 6765450.0
  }
}
```

---

### **3. PUT /api/cart/update - Cập nhật số lượng sản phẩm**

#### **Request:**
```bash
curl -X PUT "https://noithat.erpcloud.vn/api/cart/update" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: session123" \
  -d '{
    "item_id": 1,
    "quantity": 3
  }'
```

#### **Parameters:**
- **item_id** (required): ID của cart line item
- **quantity** (required): Số lượng mới

---

### **4. DELETE /api/cart/remove/{item_id} - Xóa sản phẩm khỏi giỏ hàng**

#### **Request:**
```bash
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/remove/1" \
  -H "X-Session-ID: session123"
```

---

### **5. DELETE /api/cart/clear - Xóa toàn bộ giỏ hàng**

#### **Request:**
```bash
curl -X DELETE "https://noithat.erpcloud.vn/api/cart/clear" \
  -H "X-Session-ID: session123"
```

---

## 💳 **CHECKOUT APIs**

### **Base URL:** `https://noithat.erpcloud.vn/api/checkout`

---

### **1. GET /api/checkout/payment-methods - Lấy phương thức thanh toán**

#### **Request:**
```bash
curl "https://noithat.erpcloud.vn/api/checkout/payment-methods"
```

#### **Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cod",
      "name": "Thanh toán khi nhận hàng",
      "description": "Thanh toán bằng tiền mặt khi nhận hàng",
      "icon": "truck",
      "enabled": true,
      "fee": 0
    },
    {
      "id": "bank_transfer",
      "name": "Chuyển khoản ngân hàng",
      "description": "Chuyển khoản qua ngân hàng",
      "icon": "credit-card",
      "enabled": true,
      "fee": 0,
      "bank_info": {
        "account_number": "**********",
        "bank_name": "Sacombank HCM",
        "account_holder": "Công Ty TNHH MTV Toloco Việt Nam",
        "branch": "Chi nhánh TP.HCM"
      }
    }
  ]
}
```

---

### **2. POST /api/checkout/validate - Validate thông tin checkout**

#### **Request:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/validate" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: session123" \
  -d '{
    "customer": {
      "full_name": "Nguyen Van Test",
      "email": "<EMAIL>",
      "phone": "**********",
      "address": "123 Test Street",
      "province": "Ho Chi Minh",
      "district": "District 1",
      "ward": "Ward 1"
    },
    "voucher_code": "WELCOME10"
  }'
```

#### **Customer Fields:**
- **full_name** (required): Họ tên khách hàng
- **email** (required): Email khách hàng
- **phone** (required): Số điện thoại
- **address** (required): Địa chỉ giao hàng
- **province** (optional): Tỉnh/Thành phố
- **district** (optional): Quận/Huyện
- **ward** (optional): Phường/Xã

---

### **3. POST /api/checkout/create-order - Tạo đơn hàng**

#### **Request:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: session123" \
  -d '{
    "customer": {
      "full_name": "Nguyen Van Test",
      "email": "<EMAIL>",
      "phone": "**********",
      "address": "123 Test Street"
    },
    "payment_method": "cod",
    "voucher_code": "044b-7f1d-4a23",
    "delivery_notes": "Giao hàng giờ hành chính",
    "gift_options": {
      "is_gift": true,
      "recipient_name": "Nguyen Thi B",
      "recipient_gender": "female",
      "gift_message": "Chúc mừng sinh nhật!"
    }
  }'
```

#### **Parameters:**
- **customer** (required): Thông tin khách hàng
- **payment_method** (required): Phương thức thanh toán (cod, bank_transfer)
- **voucher_code** (optional): Mã khuyến mại
- **delivery_notes** (optional): Ghi chú giao hàng
- **gift_options** (optional): Tùy chọn quà tặng

#### **Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "order_id": 6,
    "order_number": "S00006",
    "total_amount": 461.3,
    "currency": "USD",
    "status": "sale",
    "estimated_delivery": "2025-05-29",
    "payment_method": "cod",
    "customer": {
      "name": "Nguyen Van Test",
      "email": "<EMAIL>",
      "phone": "**********"
    },
    "items": [
      {
        "id": 8,
        "product_name": "Mặt dây chuyền Vàng trắng 14K đính đá Topaz PNJ TPXMW000445 (Kích thước 14)",
        "quantity": 2,
        "unit_price": 201.0,
        "total_price": 402.0,
        "currency": "USD"
      },
      {
        "id": 13,
        "product_name": "Thẻ quà tặng",
        "quantity": 1,
        "unit_price": -1.0,
        "total_price": -1.0,
        "currency": "USD"
      }
    ],
    "tracking_url": "/orders/track/6?email=<EMAIL>"
  }
}
```

---

## 🎫 **HỆ THỐNG KHUYẾN MẠI (LOYALTY SYSTEM)**

### **🔧 Technical Implementation:**

API sử dụng 100% Odoo's native loyalty system thông qua các methods:

```python
# Apply voucher code
status = order._try_apply_code(voucher_code)

# Apply rewards automatically
order._apply_program_reward(rewards, coupon)

# Recalculate totals
order._update_programs_and_rewards()
```

### **✅ Supported Program Types:**

#### **1. 🎫 Coupons - Phiếu giảm giá**
- **Mô tả:** Phiếu giảm giá một lần sử dụng
- **Ví dụ:** Giảm 10%, giảm 50.000đ, miễn phí ship
- **Usage:** `"voucher_code": "DISCOUNT10"`

#### **2. 🎁 Gift Card - Thẻ quà tặng**
- **Mô tả:** Thẻ quà tặng có giá trị định trước
- **Ví dụ:** Thẻ quà tặng 100.000đ, 500.000đ
- **Usage:** `"voucher_code": "044b-7f1d-4a23"`
- **✅ Tested:** Hoạt động hoàn hảo với discount -1 USD

#### **3. 💎 Loyalty - Thẻ khách hàng thân thiết**
- **Mô tả:** Hệ thống tích điểm và đổi quà
- **Ví dụ:** Tích 100 điểm = 1 voucher, VIP tiers
- **Usage:** Automatic point accumulation

#### **4. 🔥 Promotion - Khuyến mãi**
- **Mô tả:** Khuyến mãi tự động theo điều kiện
- **Ví dụ:** Giảm 20% cho đơn hàng > 1.000.000đ
- **Usage:** Auto-apply when conditions met

#### **5. 💳 eWallet - Ví điện tử**
- **Mô tả:** Hệ thống ví điện tử prepaid
- **Ví dụ:** Nạp tiền vào ví, thanh toán bằng ví
- **Usage:** Balance deduction

#### **6. 🏷️ Promo Code - Mã giảm giá**
- **Mô tả:** Mã giảm giá cho chiến dịch marketing
- **Ví dụ:** "WELCOME10", "SUMMER2024", "NEWUSER"
- **Usage:** `"voucher_code": "WELCOME10"`

#### **7. 🛒 Buy X Get Y - Mua X Tặng Y**
- **Mô tả:** Khuyến mãi theo combo sản phẩm
- **Ví dụ:** Mua 2 nhẫn tặng 1 dây chuyền
- **Usage:** Auto-apply when cart conditions met

#### **8. 📅 Next Order Coupons - Phiếu giảm giá đơn hàng tiếp theo**
- **Mô tả:** Tặng voucher cho đơn hàng tiếp theo
- **Ví dụ:** Hoàn thành đơn này, nhận voucher 15% cho lần mua sau
- **Usage:** Generated after order completion

---

## 📦 **ORDER TRACKING APIs**

### **Base URL:** `https://noithat.erpcloud.vn/api/orders`

### **1. GET /api/orders/track - Theo dõi đơn hàng**

#### **Request:**
```bash
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"
```

### **2. GET /api/orders/{order_id} - Chi tiết đơn hàng**

#### **Request:**
```bash
curl "https://noithat.erpcloud.vn/api/orders/6?email=<EMAIL>"
```

### **3. POST /api/orders/{order_id}/cancel - Hủy đơn hàng**

#### **Request:**
```bash
curl -X POST "https://noithat.erpcloud.vn/api/orders/6/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "reason": "Đổi ý không mua nữa"
  }'
```

---

## 🔍 **VOUCHER VALIDATION API**

### **GET /api/vouchers/validate/{voucher_code} - Kiểm tra mã voucher**

#### **Request:**
```bash
curl "https://noithat.erpcloud.vn/api/vouchers/validate/044b-7f1d-4a23"
```

#### **Response:**
```json
{
  "success": true,
  "data": {
    "voucher_code": "044b-7f1d-4a23",
    "program_id": 1,
    "program_name": "Thẻ quà tặng",
    "is_valid": true,
    "active": true,
    "reward_info": {
      "discount_mode": "per_point",
      "discount_percent": 0,
      "discount_fixed": 0,
      "currency": "USD"
    },
    "description": "Thẻ quà tặng"
  }
}
```

---

## ⚠️ **ERROR HANDLING**

### **Standard Error Response:**
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

### **Common Error Codes:**
- **CART_NOT_FOUND:** Giỏ hàng không tồn tại
- **PRODUCT_NOT_FOUND:** Sản phẩm không tồn tại
- **INVALID_QUANTITY:** Số lượng không hợp lệ
- **VOUCHER_INVALID:** Mã voucher không hợp lệ
- **VOUCHER_EXPIRED:** Mã voucher đã hết hạn
- **VOUCHER_USED:** Mã voucher đã được sử dụng
- **CUSTOMER_INVALID:** Thông tin khách hàng không hợp lệ
- **CART_EMPTY:** Giỏ hàng trống

---

## 🚀 **INTEGRATION EXAMPLES**

### **Complete Shopping Flow:**

```bash
# 1. Add product to cart
curl -X POST "https://noithat.erpcloud.vn/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: flow123" \
  -d '{"product_id": 19, "variant_id": 32, "quantity": 1}'

# 2. Check cart
curl -H "X-Session-ID: flow123" "https://noithat.erpcloud.vn/api/cart"

# 3. Validate voucher
curl "https://noithat.erpcloud.vn/api/vouchers/validate/044b-7f1d-4a23"

# 4. Create order with voucher
curl -X POST "https://noithat.erpcloud.vn/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: flow123" \
  -d '{
    "customer": {
      "full_name": "Complete Flow Test",
      "email": "<EMAIL>",
      "phone": "0987654321",
      "address": "123 Flow Street"
    },
    "payment_method": "cod",
    "voucher_code": "044b-7f1d-4a23"
  }'

# 5. Track order
curl "https://noithat.erpcloud.vn/api/orders/track?email=<EMAIL>"
```

---

## 📊 **PERFORMANCE & SCALABILITY**

### **✅ Optimizations:**
- **Native Odoo Methods:** Tận dụng Odoo's optimized calculations
- **Session Management:** Efficient guest cart handling
- **Automatic Cleanup:** Old carts auto-cleanup
- **Database Indexing:** Optimized queries for performance

### **📈 Scalability Features:**
- **Stateless APIs:** Horizontal scaling support
- **Caching:** Odoo's built-in caching mechanisms
- **Load Balancing:** Multiple Odoo instances support
- **Database Optimization:** PostgreSQL performance tuning

---

## 🎯 **CONCLUSION**

Hệ thống Cart & Checkout APIs cung cấp một giải pháp eCommerce hoàn chỉnh với:

- ✅ **Complete Shopping Experience:** Từ browse → cart → checkout → tracking
- ✅ **Universal Loyalty Support:** Tất cả 8 loại loyalty programs của Odoo
- ✅ **Guest-Friendly:** Không cần đăng nhập để mua hàng
- ✅ **Production-Ready:** Sử dụng Odoo's battle-tested methods
- ✅ **Scalable Architecture:** Sẵn sàng cho high-traffic eCommerce

**🚀 APIs sẵn sàng cho frontend integration và production deployment! 💎✨**

---

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **🏗️ Architecture Overview:**

```
Frontend (React/Vue/Angular)
    ↓ HTTP Requests
API Controllers (Python)
    ↓ Method Calls
Odoo Core Models (sale.order, loyalty.program)
    ↓ Database Operations
PostgreSQL Database
```

### **📁 File Structure:**
```
jewelry_ecommerce/
├── controllers/
│   ├── api_cart.py          # Cart management APIs
│   ├── api_checkout.py      # Checkout & order creation
│   ├── api_orders.py        # Order tracking & management
│   └── api_vouchers.py      # Voucher validation
├── models/
│   └── sale_order.py        # Extended sale.order model
└── data/
    └── sample_data.xml      # Sample products & categories
```

### **🔑 Key Odoo Methods Used:**

#### **Cart Management:**
```python
# Create guest cart
cart = SaleOrder.create({
    'partner_id': partner.id,
    'session_id': session_id,
    'is_guest_cart': True,
    'state': 'draft',
})

# Add product to cart (Odoo auto-calculates price, tax)
cart.order_line.create({
    'order_id': cart.id,
    'product_id': variant_id,
    'product_uom_qty': quantity,
})
```

#### **Loyalty System Integration:**
```python
# Apply voucher using Odoo's native method
status = order._try_apply_code(voucher_code)

# Apply rewards automatically
if status:
    for coupon, rewards in status.items():
        order._apply_program_reward(rewards, coupon)

# Recalculate totals
order._update_programs_and_rewards()
```

#### **Order Confirmation:**
```python
# Convert cart to order
order = cart.convert_to_order(customer_data)

# Confirm order (auto-generates order number)
order.action_confirm()
```

---

## 🧪 **TESTING GUIDE**

### **🔧 Test Environment Setup:**
```bash
# Base URL
BASE_URL="https://noithat.erpcloud.vn"

# Test session ID
SESSION_ID="test_$(date +%s)"
```

### **📋 Test Scenarios:**

#### **Scenario 1: Basic Cart Operations**
```bash
# 1. Get empty cart
curl -H "X-Session-ID: $SESSION_ID" "$BASE_URL/api/cart"

# 2. Add product
curl -X POST "$BASE_URL/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{"product_id": 19, "variant_id": 32, "quantity": 1}'

# 3. Update quantity
curl -X PUT "$BASE_URL/api/cart/update" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION_ID" \
  -d '{"item_id": 1, "quantity": 2}'

# 4. Remove item
curl -X DELETE "$BASE_URL/api/cart/remove/1" \
  -H "X-Session-ID: $SESSION_ID"
```

#### **Scenario 2: Voucher Testing**
```bash
# Test each voucher type
VOUCHER_CODES=(
  "044b-7f1d-4a23"    # Gift Card
  "WELCOME10"         # Promo Code
  "LOYALTY2024"       # Loyalty Program
  "SUMMER50"          # Coupon
)

for code in "${VOUCHER_CODES[@]}"; do
  echo "Testing voucher: $code"
  curl "https://noithat.erpcloud.vn/api/vouchers/validate/$code"
done
```

#### **Scenario 3: Complete Purchase Flow**
```bash
# Complete flow with voucher
SESSION="complete_$(date +%s)"

# Add product
curl -X POST "$BASE_URL/api/cart/add" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION" \
  -d '{"product_id": 19, "variant_id": 32, "quantity": 1}'

# Create order with voucher
curl -X POST "$BASE_URL/api/checkout/create-order" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: $SESSION" \
  -d '{
    "customer": {
      "full_name": "Test Customer",
      "email": "<EMAIL>",
      "phone": "**********",
      "address": "Test Address"
    },
    "payment_method": "cod",
    "voucher_code": "044b-7f1d-4a23"
  }'
```

---

## 🎯 **FRONTEND INTEGRATION GUIDE**

### **🔧 JavaScript/React Example:**

#### **Cart Context Setup:**
```javascript
// CartContext.js
import React, { createContext, useContext, useReducer } from 'react';

const CartContext = createContext();
const BASE_URL = 'https://noithat.erpcloud.vn/api';

// Generate session ID
const getSessionId = () => {
  let sessionId = localStorage.getItem('cart_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem('cart_session_id', sessionId);
  }
  return sessionId;
};

// API calls
const cartAPI = {
  async getCart() {
    const response = await fetch(`${BASE_URL}/cart`, {
      headers: { 'X-Session-ID': getSessionId() }
    });
    return response.json();
  },

  async addToCart(productId, variantId, quantity, attributes = {}) {
    const response = await fetch(`${BASE_URL}/cart/add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': getSessionId()
      },
      body: JSON.stringify({
        product_id: productId,
        variant_id: variantId,
        quantity,
        attributes
      })
    });
    return response.json();
  },

  async createOrder(customerData, paymentMethod, voucherCode = null) {
    const response = await fetch(`${BASE_URL}/checkout/create-order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Session-ID': getSessionId()
      },
      body: JSON.stringify({
        customer: customerData,
        payment_method: paymentMethod,
        voucher_code: voucherCode
      })
    });
    return response.json();
  }
};
```

#### **Voucher Component:**
```javascript
// VoucherInput.js
import React, { useState } from 'react';

const VoucherInput = ({ onVoucherApply }) => {
  const [voucherCode, setVoucherCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [voucherInfo, setVoucherInfo] = useState(null);

  const validateVoucher = async (code) => {
    setIsValidating(true);
    try {
      const response = await fetch(`${BASE_URL}/vouchers/validate/${code}`);
      const result = await response.json();

      if (result.success) {
        setVoucherInfo(result.data);
        onVoucherApply(code, result.data);
      } else {
        alert('Mã voucher không hợp lệ');
      }
    } catch (error) {
      console.error('Voucher validation error:', error);
    }
    setIsValidating(false);
  };

  return (
    <div className="voucher-input">
      <input
        type="text"
        value={voucherCode}
        onChange={(e) => setVoucherCode(e.target.value)}
        placeholder="Nhập mã voucher"
        className="form-input"
      />
      <button
        onClick={() => validateVoucher(voucherCode)}
        disabled={!voucherCode || isValidating}
        className="btn btn-primary"
      >
        {isValidating ? 'Đang kiểm tra...' : 'Áp dụng'}
      </button>

      {voucherInfo && (
        <div className="voucher-info">
          <p>✅ {voucherInfo.program_name}</p>
          <p>{voucherInfo.description}</p>
        </div>
      )}
    </div>
  );
};
```

---

## 📱 **MOBILE OPTIMIZATION**

### **🔧 Mobile-Specific Considerations:**

#### **Session Management:**
```javascript
// Mobile session handling
const MobileCartManager = {
  // Use device ID for session
  getDeviceSessionId() {
    const deviceId = this.getDeviceId();
    return `mobile_${deviceId}_${Date.now()}`;
  },

  // Persist cart across app restarts
  async syncCart() {
    const localCart = await AsyncStorage.getItem('cart_data');
    const serverCart = await cartAPI.getCart();

    // Merge local and server cart if needed
    return this.mergeCartData(localCart, serverCart);
  }
};
```

#### **Offline Support:**
```javascript
// Offline cart management
const OfflineCartManager = {
  async addToCartOffline(product) {
    const offlineCart = await AsyncStorage.getItem('offline_cart') || '[]';
    const cart = JSON.parse(offlineCart);
    cart.push(product);
    await AsyncStorage.setItem('offline_cart', JSON.stringify(cart));
  },

  async syncWhenOnline() {
    const offlineCart = await AsyncStorage.getItem('offline_cart');
    if (offlineCart && navigator.onLine) {
      const items = JSON.parse(offlineCart);
      for (const item of items) {
        await cartAPI.addToCart(item.product_id, item.variant_id, item.quantity);
      }
      await AsyncStorage.removeItem('offline_cart');
    }
  }
};
```

---

## 🔒 **SECURITY CONSIDERATIONS**

### **🛡️ Security Features:**

#### **Session Security:**
- **Session ID Generation:** Cryptographically secure random IDs
- **Session Expiration:** Auto-cleanup old sessions
- **Rate Limiting:** Prevent API abuse
- **CORS Protection:** Configured for specific domains

#### **Data Validation:**
```python
# Input validation example
def validate_customer_data(data):
    required_fields = ['full_name', 'email', 'phone', 'address']
    for field in required_fields:
        if not data.get(field):
            raise ValidationError(f'{field} is required')

    # Email validation
    if not re.match(r'^[^@]+@[^@]+\.[^@]+$', data['email']):
        raise ValidationError('Invalid email format')

    # Phone validation
    if not re.match(r'^\d{10,11}$', data['phone']):
        raise ValidationError('Invalid phone number')
```

#### **Order Security:**
- **Guest Order Access:** Email-based verification
- **Order Tracking:** Secure URLs with tokens
- **Payment Security:** No sensitive payment data stored
- **Audit Trail:** Complete order history logging

---

## 📊 **MONITORING & ANALYTICS**

### **📈 Key Metrics to Track:**

#### **Cart Metrics:**
- Cart abandonment rate
- Average cart value
- Items per cart
- Session duration

#### **Voucher Metrics:**
- Voucher usage rate
- Discount amount distribution
- Popular voucher types
- Conversion rate with vouchers

#### **Order Metrics:**
- Order completion rate
- Average order value
- Payment method distribution
- Guest vs registered user ratio

### **🔍 Logging Example:**
```python
# Enhanced logging for analytics
_logger.info(f"Cart created: session={session_id}, user_agent={request.httprequest.user_agent}")
_logger.info(f"Product added: product_id={product_id}, quantity={quantity}, cart_value={cart.amount_total}")
_logger.info(f"Voucher applied: code={voucher_code}, discount={discount_amount}, order_id={order.id}")
_logger.info(f"Order completed: order_id={order.id}, total={order.amount_total}, payment={payment_method}")
```

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **✅ Pre-Production Checklist:**

#### **Environment Setup:**
- [ ] Odoo 18 Community Edition installed
- [ ] jewelry_ecommerce module installed and configured
- [ ] Sample data loaded (products, categories, loyalty programs)
- [ ] SSL certificate configured
- [ ] Database backup strategy in place

#### **API Configuration:**
- [ ] CORS settings configured for production domains
- [ ] Rate limiting configured
- [ ] Error logging enabled
- [ ] Performance monitoring setup

#### **Testing:**
- [ ] All API endpoints tested
- [ ] Voucher system tested with all program types
- [ ] Load testing completed
- [ ] Security testing completed
- [ ] Mobile compatibility verified

#### **Documentation:**
- [ ] API documentation updated
- [ ] Frontend integration guide provided
- [ ] Error handling documented
- [ ] Support procedures documented

---

## 📞 **SUPPORT & MAINTENANCE**

### **🔧 Common Issues & Solutions:**

#### **Cart Issues:**
- **Empty cart after page refresh:** Check session ID persistence
- **Products not adding:** Verify product_id and variant_id
- **Price calculation errors:** Check tax configuration in Odoo

#### **Voucher Issues:**
- **Voucher not applying:** Check loyalty program configuration
- **Discount not calculating:** Verify reward rules and conditions
- **Expired voucher errors:** Check expiration dates in loyalty.card

#### **Order Issues:**
- **Order creation fails:** Check customer data validation
- **Email not sending:** Verify email server configuration
- **Order tracking not working:** Check guest email access

### **📧 Support Contacts:**
- **Technical Support:** <EMAIL>
- **API Documentation:** https://docs.binhanjewelry.com/api
- **Bug Reports:** https://github.com/binhanjewelry/issues

---

## 🎯 **ROADMAP & FUTURE ENHANCEMENTS**

### **📅 Planned Features:**

#### **Phase 1: Authentication (Q2 2025)**
- JWT-based authentication
- User registration/login APIs
- Guest to user migration
- User profile management

#### **Phase 2: Advanced Features (Q3 2025)**
- Wishlist APIs
- Product reviews and ratings
- Advanced search and filtering
- Recommendation engine

#### **Phase 3: Enterprise Features (Q4 2025)**
- Multi-currency support
- Multi-language support
- Advanced analytics dashboard
- Third-party integrations (payment gateways, shipping)

### **🔧 Technical Improvements:**
- GraphQL API support
- Real-time notifications (WebSocket)
- Advanced caching strategies
- Microservices architecture migration

---

**📚 Tài liệu này cung cấp hướng dẫn đầy đủ để implement và maintain hệ thống Cart & Checkout APIs với Odoo loyalty system. Để biết thêm chi tiết, vui lòng tham khảo source code và Odoo documentation. 🚀💎✨**
