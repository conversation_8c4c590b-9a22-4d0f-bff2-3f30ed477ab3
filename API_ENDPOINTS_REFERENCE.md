# 📚 JEWELRY ECOMMERCE API - ENDPOINTS REFERENCE

## 🌐 Base URL
```
https://noithat.erpcloud.vn
```

---

## 📦 PRODUCTS API

### **GET /api/products**
L<PERSON>y danh sách sản phẩm với pagination và filtering

#### **Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | int | 1 | Số trang |
| `per_page` | int | 20 | Số sản phẩm mỗi trang (max: 100) |
| `limit` | int | 20 | Alias cho per_page (backward compatibility) |
| `offset` | int | 0 | Số sản phẩm bỏ qua |
| `category` | int | - | Filter theo category ID |
| `brand` | int | - | Filter theo brand ID |
| `search` | string | - | Tìm kiếm trong tên và mô tả |
| `product_type` | string | - | Filter: product, service, combo |
| `detailed_type` | string | - | Filter: product, service, consu |
| `sort` | string | name | Sắp xếp: name, list_price, create_date |
| `order` | string | asc | Thứ tự: asc, desc |

#### **Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": 16,
        "name": "Dây chuyền Vàng 18K PNJ",
        "price": 12450000.0,
        "currency": "USD",
        "product_type": "product",
        "main_image": "/web/image/product.template/16/image_1920",
        "images": [...],
        "videos": [...],
        "variants": [...],
        "attributes": [...],
        "uom_info": {...}
      }
    ],
    "pagination": {
      "total_items": 7,
      "total_pages": 3,
      "current_page": 1,
      "per_page": 3,
      "has_next": true,
      "has_prev": false,
      "showing_text": "Hiển thị 1-3 trong tổng số 7 sản phẩm"
    }
  }
}
```

### **GET /api/products/{id}**
Lấy chi tiết sản phẩm

#### **Response:**
```json
{
  "success": true,
  "data": {
    "id": 13,
    "name": "Nhẫn cưới Vàng 18K PNJ",
    "description": "Nhẫn cưới vàng 18K thiết kế truyền thống...",
    "price": 8950000.0,
    "variants": [
      {
        "id": 23,
        "name": "Nhẫn cưới... (Kích thước 14)",
        "attributes": [
          {
            "attribute_name": "Chọn size",
            "value_name": "Kích thước 14",
            "value_id": 1
          }
        ]
      }
    ],
    "attributes": [
      {
        "id": 1,
        "name": "Chọn size",
        "display_type": "radio",
        "values": [
          {
            "id": 1,
            "name": "Kích thước 14",
            "frontend_config": {
              "css_class": "attribute-value radio-style"
            }
          }
        ]
      }
    ]
  }
}
```

### **POST /api/products/{id}/variant**
Lấy variant dựa trên attributes và UoM đã chọn

#### **Request Body:**
```json
{
  "attribute_values": [1, 2],
  "uom_id": 1
}
```

#### **Response:**
```json
{
  "success": true,
  "data": {
    "variant_id": 23,
    "variant_name": "Nhẫn cưới... (Kích thước 14)",
    "base_price": 8950000.0,
    "extra_price": 0,
    "final_price": 8950000.0,
    "uom_price": 8950000.0,
    "selected_attributes": [
      {
        "attribute_name": "Chọn size",
        "value_name": "Kích thước 14",
        "extra_price": 0
      }
    ]
  }
}
```

### **GET /api/products/{id}/combo**
Lấy chi tiết combo product

#### **Response:**
```json
{
  "success": true,
  "data": {
    "product_id": 5,
    "product_name": "Combo tháng 10",
    "combo_price": 1200000.0,
    "combo_items": [
      {
        "id": 16,
        "name": "Dây chuyền Vàng 18K",
        "quantity": 1,
        "original_price": 12450000.0,
        "extra_price": 10450000.0,
        "final_price": 22900000.0
      }
    ],
    "combo_summary": {
      "total_items": 1,
      "savings_amount": 21700000.0,
      "savings_percentage": 94.76
    }
  }
}
```

---

## 🏷️ CATEGORIES API

### **GET /api/categories**
Lấy danh sách categories

#### **Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Nhẫn",
        "parent_id": null,
        "sequence": 1,
        "product_count": 1,
        "image_url": "/web/image/product.public.category/1/image_1920",
        "children": []
      }
    ]
  }
}
```

### **GET /api/categories/mega-menu**
Lấy dữ liệu cho mega menu

#### **Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Nhẫn",
        "image_url": "/web/image/product.public.category/1/image_1920",
        "description": "Nhẫn cao cấp...",
        "featured_products": [
          {
            "id": 13,
            "name": "Nhẫn cưới Vàng 18K",
            "price": 8950000.0,
            "image_url": "/web/image/product.template/13/image_512"
          }
        ]
      }
    ]
  }
}
```

### **GET /api/categories/{id}/products**
Lấy sản phẩm trong category

#### **Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | int | 1 | Số trang |
| `per_page` | int | 20 | Số sản phẩm mỗi trang |
| `sort` | string | name | Sắp xếp |
| `order` | string | asc | Thứ tự |

---

## 🎨 ATTRIBUTES API

### **GET /api/product-attributes/filterable**
Lấy attributes có thể filter

#### **Response:**
```json
{
  "success": true,
  "data": {
    "filterable_attributes": [
      {
        "id": 1,
        "name": "Chọn size",
        "display_type": "radio",
        "values": [
          {
            "id": 1,
            "name": "Kích thước 14",
            "products_count": 1,
            "frontend_config": {
              "css_class": "attribute-value radio-style"
            }
          }
        ],
        "frontend_config": {
          "filter_type": "radio",
          "component_type": "RadioGroup"
        }
      }
    ],
    "filter_config": {
      "display_types": {
        "radio": "Radio buttons",
        "pills": "Pill buttons",
        "color": "Color swatches"
      }
    }
  }
}
```

---

## 🎫 VOUCHERS API

### **GET /api/vouchers**
Lấy danh sách vouchers

#### **Response:**
```json
{
  "success": true,
  "data": {
    "vouchers": [
      {
        "id": 1,
        "name": "Giảm giá 10%",
        "code": "DISCOUNT10",
        "discount_type": "percentage",
        "discount_value": 10.0,
        "minimum_amount": 500000.0,
        "is_active": true,
        "valid_from": "2024-01-01",
        "valid_to": "2024-12-31"
      }
    ]
  }
}
```

### **POST /api/vouchers/apply**
Áp dụng voucher

#### **Request Body:**
```json
{
  "voucher_code": "DISCOUNT10",
  "order_amount": 1000000
}
```

#### **Response:**
```json
{
  "success": true,
  "data": {
    "voucher_id": 1,
    "voucher_code": "DISCOUNT10",
    "discount_amount": 100000.0,
    "final_amount": 900000.0,
    "is_valid": true
  }
}
```

---

## 📏 UOM API

### **GET /api/uom/categories**
Lấy danh sách UoM categories

#### **Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "Đơn vị",
        "measure_type": "custom",
        "uoms": [
          {
            "id": 1,
            "name": "Đơn vị",
            "factor": 1.0,
            "is_reference": true
          },
          {
            "id": 2,
            "name": "Dozens",
            "factor": 0.08333,
            "is_reference": false
          }
        ]
      }
    ]
  }
}
```

### **GET /api/products/{id}/uom-pricing**
Lấy pricing theo UoM cho sản phẩm

#### **Response:**
```json
{
  "success": true,
  "data": {
    "product_id": 16,
    "product_name": "Dây chuyền Vàng 18K",
    "base_price": 12450000.0,
    "uom_info": {
      "available_uoms": [
        {
          "id": 1,
          "name": "Đơn vị",
          "price": 12450000.0,
          "is_base": true
        },
        {
          "id": 2,
          "name": "Dozens",
          "price": 149400000.0,
          "is_base": false
        }
      ]
    }
  }
}
```

---

## 🚨 ERROR RESPONSES

### **Standard Error Format:**
```json
{
  "success": false,
  "error": "Product not found",
  "message": "Product with ID 999 not found or not published"
}
```

### **Common HTTP Status Codes:**
- **200:** Success
- **400:** Bad Request (invalid parameters)
- **404:** Not Found (product/category not found)
- **500:** Internal Server Error

---

## 🔧 DEVELOPMENT NOTES

### **CORS Support:**
All endpoints support CORS with `cors='*'`

### **Authentication:**
Currently all endpoints use `auth='public'` for demo purposes

### **Rate Limiting:**
No rate limiting implemented (add in production)

### **Caching:**
No caching implemented (add Redis in production)

---

**📚 API REFERENCE HOÀN CHỈNH - READY FOR INTEGRATION! 🚀**
